package com.chervon.common.oss.uitl;

import com.amazonaws.HttpMethod;
import com.amazonaws.auth.AWSCredentials;
import com.amazonaws.auth.AWSStaticCredentialsProvider;
import com.amazonaws.auth.BasicAWSCredentials;
import com.amazonaws.regions.Regions;
import com.amazonaws.services.s3.AmazonS3;
import com.amazonaws.services.s3.AmazonS3ClientBuilder;
import com.amazonaws.services.s3.model.*;
import com.chervon.common.core.exception.ServiceException;
import com.chervon.common.core.prop.AwsProperties;
import com.chervon.common.oss.constant.RedisKeyConstant;
import com.chervon.common.redis.utils.RedisUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.context.properties.EnableConfigurationProperties;

import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.IOException;
import java.net.URL;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @className S3Util
 * @description
 * @date 2022/3/8 18:49
 */
@EnableConfigurationProperties(AwsProperties.class)
@Slf4j
public class S3Util {

    private final AwsProperties awsProperties;

    private AmazonS3 amazonS3;

    private static final Long HALF_HOUR_MILLIS = 30 * 60 * 1000L;

    @Autowired
    public S3Util(AwsProperties awsProperties) {
        this.awsProperties = awsProperties;
    }

    private void initAmazonS3() {
        if (null != amazonS3) {
            return;
        }
        AWSCredentials credentials = new BasicAWSCredentials(awsProperties.getAccessId(),
                awsProperties.getSecretKey());
        this.amazonS3 = AmazonS3ClientBuilder.standard()
                .withRegion(Regions.fromName(awsProperties.getRegion()))
                .withCredentials(new AWSStaticCredentialsProvider(credentials))
                .withDualstackEnabled(true)
                .build();
    }

    /**
     * 获取S3 Object
     *
     * <AUTHOR>
     * @date 14:12 2022/3/9
     * @return: java.lang.Boolean
     **/
    public S3Object getS3Object(String bucketName, String key) {
        initAmazonS3();
        try {
            return amazonS3.getObject(bucketName, key);

        } catch (AmazonS3Exception ex) {
            log.error(ex.getMessage(), ex);
        }
        return null;
    }

    /**
     * 上传文件到s3,不返回url
     *
     * @param isPublic: 是否公开访问，为避免误调用，该参数必填
     * <AUTHOR>
     * @date 11:44 2022/3/9
     * @return: void
     **/
    public void uploadFile(String bucketName, String key, File file, Boolean isPublic) {
        initAmazonS3();
        log.info("开始上传文件到s3, key: {}, isPublic: {}", key, isPublic);
        try {
            ObjectMetadata data = new ObjectMetadata();
            PutObjectRequest obj = new PutObjectRequest(bucketName, key, file);
            if (isPublic) {
                data.setHeader("x-amz-acl", "public-read");
            }
            obj.setMetadata(data);
            amazonS3.putObject(obj);
            log.info("完成上传文件到s3,key:[{}]", key);
        } catch (Exception ex) {
            log.error(ex.getMessage(), ex);
            // todo
            throw new ServiceException();
//            throw new ServiceException(CodeEnum.OPERATE_S3_EXCEPTION, ex.getMessage());
        }
    }

    public String getPreSignedPutPublicUrl(String bucketName, String key, Date expiration) {
        return getPreSignedUrl(bucketName, key, expiration, true, true);
    }

    public String getPreSignedPutPublicUrl(String bucketName, String key) {
        Date expiration = new Date(System.currentTimeMillis() + HALF_HOUR_MILLIS);
        return getPreSignedUrl(bucketName, key, expiration, true, true);
    }

    public String getPreSignedPutPrivateUrl(String bucketName, String key, Date expiration) {
        return getPreSignedUrl(bucketName, key, expiration, true, false);
    }

    public String getPreSignedPutPrivateUrl(String bucketName, String key) {
        Date expiration = new Date(System.currentTimeMillis() + HALF_HOUR_MILLIS);
        return getPreSignedUrl(bucketName, key, expiration, true, false);
    }

    public String getPreSignedGetPublicUrl(String bucketName, String key, Date expiration) {
        return getPreSignedUrl(bucketName, key, expiration, false, true);
    }

    public String getPreSignedGetPublicUrl(String bucketName, String key) {
        Date expiration = new Date(System.currentTimeMillis() + HALF_HOUR_MILLIS);
        return getPreSignedUrl(bucketName, key, expiration, false, true);
    }

    public String getPreSignedGetPrivateUrl(String bucketName, String key, Date expiration) {
        return getPreSignedUrl(bucketName, key, expiration, false, false);
    }

    public String getPreSignedGetPrivateUrl(String bucketName, String key) {
        Date expiration = new Date(System.currentTimeMillis() + HALF_HOUR_MILLIS);
        return getPreSignedUrl(bucketName, key, expiration, false, false);
    }

    /**
     * 获取预签名的url
     *
     * @param expiration: url过期时间，默认一分钟
     * @param isPut:      是否上传文件预签名，true 上传， false 下载
     * @param isPublic:   上传文件的是否有公共读权限
     * <AUTHOR>
     * @date 14:43 2022/3/9
     * @return: java.lang.String
     **/
    private String getPreSignedUrl(String bucketName, String key, Date expiration, Boolean isPut,
                                   Boolean isPublic) {
        initAmazonS3();
        try {
            GeneratePresignedUrlRequest generatePresignedUrlRequest;
            generatePresignedUrlRequest = new GeneratePresignedUrlRequest(bucketName, key);
            if (expiration == null) {
                // 默认过期时间一分钟
                expiration = new Date();
                long expTimeMillis = expiration.getTime() + 1000 * 60;
                expiration.setTime(expTimeMillis);
            }
            generatePresignedUrlRequest.setExpiration(expiration);
            if (isPut) {
                generatePresignedUrlRequest.withMethod(HttpMethod.PUT);
                if (isPublic) {
                    generatePresignedUrlRequest.addRequestParameter("x-amz-acl", "public-read");
                }
            }
            URL url = amazonS3.generatePresignedUrl(generatePresignedUrlRequest);
            return url.toString();
        } catch (Exception ex) {
            // todo
            log.error(ex.getMessage(), ex);
            throw new ServiceException();
//            throw new ServiceException(CodeEnum.OPERATE_S3_EXCEPTION, ex.getMessage());
        }
    }

    /**
     * 分段获取预签名的url
     *
     * @param expiration: url过期时间，默认一分钟
     * @param isPut:      是否上传文件预签名，true 上传， false 下载
     * @param isPublic:   上传文件的是否有公共读权限
     * <AUTHOR>
     * @date 14:43 2022/3/9
     * @return: java.lang.String
     **/
    public String getPartPreSignedUrl(String bucketName, String key, Date expiration, Boolean isPut,
                                      Boolean isPublic, String uploadId, Integer partNumber) {
        initAmazonS3();
        try {
            GeneratePresignedUrlRequest generatePresignedUrlRequest;
            generatePresignedUrlRequest = new GeneratePresignedUrlRequest(bucketName, key);
            if (expiration == null) {
                // 默认过期时间一分钟
                expiration = new Date();
                long expTimeMillis = expiration.getTime() + 1000 * 60;
                expiration.setTime(expTimeMillis);
            }
            generatePresignedUrlRequest.setExpiration(expiration);
            if (isPut) {
                generatePresignedUrlRequest.withMethod(HttpMethod.PUT);
                if (isPublic) {
                    generatePresignedUrlRequest.addRequestParameter("x-amz-acl", "public-read");
                }
            }
            generatePresignedUrlRequest.addRequestParameter("uploadId", uploadId);
            generatePresignedUrlRequest.addRequestParameter("partNumber", partNumber.toString());
            URL url = amazonS3.generatePresignedUrl(generatePresignedUrlRequest);
            return url.toString();
        } catch (Exception ex) {
            log.error(ex.getMessage(), ex);
            throw new ServiceException();
//            throw new ServiceException(CodeEnum.OPERATE_S3_EXCEPTION, ex.getMessage());
        }
    }

    /**
     * 创建分段上传
     *
     * @param key:      文件标识
     * @param total:    文件分段总数
     * @param isPublic: 是否公开访问权限
     * <AUTHOR>
     * @date 16:55 2022/3/9
     * @return: java.lang.String 上传ID
     **/
    public String createMultipartUpload(String bucketName, String key, Integer total,
                                        Boolean isPublic) {
        initAmazonS3();
        log.info("开始创建分段上传");
        try {
            InitiateMultipartUploadRequest request = new InitiateMultipartUploadRequest(bucketName, key);
            ObjectMetadata data = new ObjectMetadata();
            if (isPublic) {
                data.setHeader("x-amz-acl", "public-read");
            }
            request.setObjectMetadata(data);
            InitiateMultipartUploadResult initResult = amazonS3.initiateMultipartUpload(request);
            String uploadId = initResult.getUploadId();
            String totalKey = RedisKeyConstant.S3_PART_TOTAL_PRE + uploadId;
            RedisUtils.setCacheObject(totalKey, total);
            String fileKey = RedisKeyConstant.S3_PART_TOTAL_PRE + uploadId;
            RedisUtils.setCacheObject(fileKey, key);
            log.info("完成创建分段上传");
            return uploadId;
        } catch (Exception ex) {
            // todo
            log.error(ex.getMessage(), ex);
            throw new ServiceException();
//            throw new ServiceException(CodeEnum.OPERATE_S3_EXCEPTION, ex.getMessage());
        }
    }

    /**
     * 合并
     *
     * @param uploadId: 上传ID
     * <AUTHOR>
     * @date 17:48 2022/3/9
     * @return: Boolean true:所有分段已上传，文件合并完成 false:文件未合并
     **/
    public Boolean completeMultipartUpload(String bucketName, String key, String uploadId, List<PartETag> partETags) {
        initAmazonS3();
        log.info("所有分段上传完成，开始合并，uploadId:{}", uploadId);
        CompleteMultipartUploadRequest comRequest = new CompleteMultipartUploadRequest(
                bucketName, key, uploadId, partETags);
        try {
            amazonS3.completeMultipartUpload(comRequest);
            String eTagKey = RedisKeyConstant.S3_PART_ETAG_PRE + uploadId;
            RedisUtils.deleteObject(eTagKey);
            log.info("完成合并，uploadId:{}", uploadId);
            return true;
        } catch (Exception ex) {
            log.error("completeMultipartUpload  uploadId:{} error:{}", uploadId, ex.getMessage());
        }
        return false;
    }

    /**
     * 下载到指定文件
     *
     * @param bucketName bucketName
     * @param key        key
     * @param file       指定文件
     */
    public void download(String bucketName, String key, File file) {
        initAmazonS3();
        GeneratePresignedUrlRequest generatePresignedUrlRequest;
        generatePresignedUrlRequest = new GeneratePresignedUrlRequest(bucketName, key);
        generatePresignedUrlRequest.setExpiration(new Date(System.currentTimeMillis() + 1000 * 60));
        URL url = amazonS3.generatePresignedUrl(generatePresignedUrlRequest);
        amazonS3.download(new PresignedUrlDownloadRequest(url), file);
    }
    /**
     * * 文件下载返回数据流
     * @param bucketName 桶名字
     * @param fileName 文件名
     * @return 文件流
     */
    public final byte[] fileDownLoad(String bucketName, String fileName) {
        initAmazonS3();
        S3Object downloadObject = amazonS3.getObject(new GetObjectRequest(bucketName, fileName));
        S3ObjectInputStream downloadObjectInput = downloadObject.getObjectContent();
        ByteArrayOutputStream result = new ByteArrayOutputStream();
        byte[] buffer = new byte[1024];
        int length;
        try {
            while ((length = downloadObjectInput.read(buffer)) != -1) {
                result.write(buffer, 0, length);
            }
            return result.toByteArray();
        } catch (IOException e1) {
            log.error("S3 File download error，error：{}", e1);
        } finally {
            try {
                downloadObjectInput.close();
                result.close();
            } catch (IOException e2) {
                log.error("S3 File download error，error：{}", e2);
            }
        }
        return null;
    }
}
