package com.chervon.iot.app.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.chervon.common.core.error.ErrorCode;
import com.chervon.common.core.exception.ServiceException;
import com.chervon.common.core.utils.Assert;
import com.chervon.common.core.utils.BeanCopyUtils;
import com.chervon.common.core.utils.StringUtils;
import com.chervon.iot.app.domain.dataobject.DeviceAlarm;
import com.chervon.iot.app.domain.dto.device.DeviceAlarmDto;
import com.chervon.iot.app.mapper.DeviceAlarmMapper;
import com.chervon.iot.app.service.DeviceAlarmService;
import com.chervon.iot.app.util.AppStringUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Objects;

/**
 * 设备警报设置服务实现
 * <AUTHOR> 2024/2/26
 */
@Service
@Slf4j
public class DeviceAlarmServiceImpl  extends ServiceImpl<DeviceAlarmMapper, DeviceAlarm> implements DeviceAlarmService {
    @Override
    public DeviceAlarmDto detail(String deviceId,Long userId) {
        LambdaQueryWrapper<DeviceAlarm> queryWrapper = new LambdaQueryWrapper<DeviceAlarm>()
                .eq(DeviceAlarm::getDeviceId, deviceId)
                .eq(DeviceAlarm::getUserId,userId);
        DeviceAlarm deviceAlarm = this.getOne(queryWrapper);
        if(Objects.isNull(deviceAlarm)) {
            return null;
        }
        DeviceAlarmDto alarmDto = BeanCopyUtils.copy(deviceAlarm, DeviceAlarmDto.class);
        alarmDto.setTelNumber(deviceAlarm.getShowNumber());
        return alarmDto;
    }

    @Override
    public Boolean create(DeviceAlarmDto requestDto) {
        Assert.hasText(requestDto.getDeviceId(), ErrorCode.PARAMETER_NOT_PROVIDED,"deviceId");
        Assert.hasText(requestDto.getFirstName(), ErrorCode.PARAMETER_NOT_PROVIDED,"firstName");
        Assert.hasText(requestDto.getLastName(), ErrorCode.PARAMETER_NOT_PROVIDED,"lastName");
        Assert.hasText(requestDto.getCountryCode(), ErrorCode.PARAMETER_NOT_PROVIDED,"countryCode");
        Assert.hasText(requestDto.getLanguage(), ErrorCode.PARAMETER_NOT_PROVIDED,"language");
        if(!requestDto.getCountryCode().contains("+")){
            throw new ServiceException(ErrorCode.PARAMETER_FORMAT_ERROR, "countryCode");
        }
        Assert.hasText(requestDto.getTelNumber(), ErrorCode.PARAMETER_NOT_PROVIDED, "telNumber");
        final String realNumber = AppStringUtils.removeSymbols(requestDto.getTelNumber());
        if(realNumber.length()<3){
            throw new ServiceException(ErrorCode.PARAMETER_FORMAT_ERROR, "telNumber");
        }
        final Long count = count(requestDto);
        if(count > 0){
            throw new ServiceException(ErrorCode.DATA_DUPLICATION, "deviceId setting");
        }
        DeviceAlarm deviceAlarm = BeanCopyUtils.copy(requestDto, DeviceAlarm.class);
        deviceAlarm.setShowNumber(requestDto.getTelNumber());
        deviceAlarm.setTelNumber(requestDto.getCountryCode().concat(realNumber));
        deviceAlarm.setNotificationByCall(Objects.isNull(requestDto.getNotificationByCall())? 0 : requestDto.getNotificationByCall());
        deviceAlarm.setNotificationByMsg(Objects.isNull(requestDto.getNotificationByMsg())? 0 : requestDto.getNotificationByMsg());
        return this.save(deviceAlarm);
    }

    @Override
    public Boolean edit(DeviceAlarmDto requestDto) {
        final List<DeviceAlarm> list = query(requestDto);
        if(CollectionUtils.isEmpty(list)){
            throw new ServiceException(ErrorCode.DATA_NOT_FOUND, "id:"+requestDto.getId());
        }
        DeviceAlarm deviceAlarm = list.get(0);
        if(!requestDto.getUserId().equals(deviceAlarm.getUserId())){
            throw new ServiceException(ErrorCode.PARAMETER_ERROR, "userId");
        }
        if(!StringUtils.isEmpty(requestDto.getFirstName())) {
            deviceAlarm.setFirstName(requestDto.getFirstName());
        }
        if (!StringUtils.isEmpty(requestDto.getLastName())) {
            deviceAlarm.setLastName(requestDto.getLastName());
        }
        if (!StringUtils.isEmpty(requestDto.getCountryCode())) {
            deviceAlarm.setCountryCode(requestDto.getCountryCode());
        }
        if (!StringUtils.isEmpty(requestDto.getTelNumber())) {
            deviceAlarm.setShowNumber(requestDto.getTelNumber());
        }
        deviceAlarm.setTelNumber(deviceAlarm.getCountryCode().concat(AppStringUtils.removeSymbols(deviceAlarm.getShowNumber())));
        deviceAlarm.setLanguage(requestDto.getLanguage());
        if(!Objects.isNull(requestDto.getNotificationByCall())) {
            deviceAlarm.setNotificationByCall(requestDto.getNotificationByCall());
        }
        if (!Objects.isNull(requestDto.getNotificationByMsg())) {
            deviceAlarm.setNotificationByMsg(requestDto.getNotificationByMsg());
        }
        return this.updateById(deviceAlarm);
    }

    private List<DeviceAlarm> query(DeviceAlarmDto requestDto) {
        LambdaQueryWrapper<DeviceAlarm> queryWrapper = new LambdaQueryWrapper<DeviceAlarm>();
        queryWrapper.eq(!Objects.isNull(requestDto.getId()),DeviceAlarm::getId, requestDto.getId());
        queryWrapper.eq(StringUtils.isNotEmpty(requestDto.getDeviceId()),DeviceAlarm::getDeviceId, requestDto.getDeviceId());
        queryWrapper.eq(!Objects.isNull(requestDto.getUserId()),DeviceAlarm::getUserId,requestDto.getUserId());
        List<DeviceAlarm> list = this.list(queryWrapper);
        return list;
    }

    private Long count(DeviceAlarmDto requestDto) {
        LambdaQueryWrapper<DeviceAlarm> queryWrapper = new LambdaQueryWrapper<DeviceAlarm>()
                .eq(DeviceAlarm::getDeviceId, requestDto.getDeviceId())
                .eq(DeviceAlarm::getUserId,requestDto.getUserId());
        return this.count(queryWrapper);
    }
}
