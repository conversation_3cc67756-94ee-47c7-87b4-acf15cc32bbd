package com.chervon.iot.app.service.impl;

import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.chervon.common.core.domain.LoginSysUser;
import com.chervon.common.core.domain.LoginUserContext;
import com.chervon.common.core.prop.AwsProperties;
import com.chervon.common.core.utils.ConvertUtil;
import com.chervon.common.core.utils.SnowFlake;
import com.chervon.common.oss.uitl.UrlUtil;
import com.chervon.common.redis.utils.RedisUtils;
import com.chervon.iot.app.api.exception.AppErrorCode;
import com.chervon.iot.app.common.CommonConstant;
import com.chervon.iot.app.config.ExceptionMessageUtil;
import com.chervon.iot.app.config.IotAppCommonConstant;
import com.chervon.iot.app.domain.dataobject.AppUserRn;
import com.chervon.iot.app.domain.dataobject.UserSetting;
import com.chervon.iot.app.domain.dto.user.*;
import com.chervon.iot.app.domain.vo.user.LoginUserResp;
import com.chervon.iot.app.service.*;
import com.chervon.iot.app.util.EmailUtil;
import com.chervon.iot.middle.api.service.RemoteDeviceService;
import com.chervon.message.api.RemoteMessageService;
import com.chervon.message.api.dto.DeviceInfoDto;
import com.chervon.message.api.dto.LastMessageDto;
import com.chervon.technology.api.vo.DeviceRpcVo;
import com.chervon.usercenter.api.dto.*;
import com.chervon.usercenter.api.exception.UserCenterErrorCode;
import com.chervon.usercenter.api.service.UserCommandService;
import com.chervon.usercenter.api.service.UserQueryService;
import com.chervon.usercenter.api.vo.UserVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

import static com.chervon.iot.app.service.impl.UserSettingServiceImpl.*;

/**
 * <AUTHOR>
 * @date 2022/6/26 12:58
 */
@Service
@Slf4j
public class AppUserServiceImpl implements AppUserService {

    @DubboReference
    private UserQueryService userQueryService;

    @DubboReference
    private UserCommandService userCommandService;

    @DubboReference(timeout = 120000, retries = 0)
    private RemoteMessageService messageService;

    @DubboReference(timeout = 120000, retries = 0)
    private RemoteDeviceService remoteDeviceService;

    @Autowired
    private AppDeviceService appDeviceService;

    @Autowired
    private UserSettingService userSettingService;

    @Autowired
    private AppAgreementService appAgreementService;

    @Autowired
    private AwsProperties awsProperties;

    @Autowired
    private AppUserRnService appUserRnService;

    @Autowired
    private CommonService commonService;

    @Autowired
    private DeviceInfoAsyncService deviceInfoAsyncService;

    @Autowired
    private DeviceShareService deviceShareService;

    @Transactional(rollbackFor = Exception.class)
    @Override
    public LoginUserResp register(RegisterDto req, String lang) {
        if (req.getCheck() == null || !req.getCheck()) {
            throw ExceptionMessageUtil.getException(AppErrorCode.APP_REGISTER_CHECK_ERROR);
        }
        // 用户firstName和lastName校验
        checkName(req.getFirstName(), req.getLastName());
        String email=req.getEmail();
        EmailUtil.check(email);
        email=email.trim();
        UserRegisterDto dto = new UserRegisterDto();
        BeanUtils.copyProperties(req, dto);
        dto.setEmail(email);
        if (!userCommandService.register(dto)) {
            throw ExceptionMessageUtil.getException(AppErrorCode.APP_REGISTER_ERROR);
        }
        LoginDto loginDto = new LoginDto();
        loginDto.setEmail(email);
        loginDto.setPassword(req.getPassword());
        loginDto.setCheck(req.getCheck());
        LoginUserResp login = this.login(loginDto, lang);
        // 设置协议
        appAgreementService.agreeLatest(StpUtil.getLoginIdAsLong(), email);
        // 新增用户设置
        insertDefaultUserSetting(StpUtil.getLoginIdAsLong(),lang,req.getEmail());
        return login;
    }

    /**
     * 创建默认用户设置信息
     * @param userId 用户id
     * @param lang 用户语言
     * @param email 邮箱地址
     */
    private void insertDefaultUserSetting(Long userId,String lang,String email){
        String key = "UserSetting:" + StpUtil.getLoginIdAsLong();
        Integer smartCache = RedisUtils.getCacheMapValue(key,SMART);
        if (smartCache != null) {
            return;
        }
        UserSetting userSetting = new UserSetting();
        userSetting.setUserId(userId);
        userSetting.setSmartSwitch(1);
        userSetting.setDeviceMessageSwitch(1);
        userSetting.setSystemMessageSwitch(1);
        userSetting.setMarketingMessageSwitch(1);
        userSetting.setLanguage(lang);
        userSettingService.save(userSetting);
        RedisUtils.setCacheMapValue(key, SMART, 1);
        RedisUtils.setCacheMapValue(key, DEVICE, 1);
        RedisUtils.setCacheMapValue(key, SYSTEM, 1);
        RedisUtils.setCacheMapValue(key, MARKETING, 1);
        RedisUtils.setCacheMapValue(key, LANGUAGE, lang);
        deviceShareService.updateAfterRegister(email, StpUtil.getLoginIdAsLong());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean cancelUser() {
        log.info("cancel user");
        UserVo userVo = RedisUtils.getCacheObject(StpUtil.getTokenValue());
        // 删除用户消息相关的消息
        List<DeviceRpcVo> deviceVos = appDeviceService.listBoundDevices();
        LastMessageDto lastMessage = new LastMessageDto();
        if (CollectionUtils.isEmpty(deviceVos)) {
            lastMessage.setDeviceInfoList(null);
        } else {
            List<DeviceInfoDto> deviceInfos = new ArrayList<>(deviceVos.size());
            deviceVos.forEach(deviceVo -> {
                DeviceInfoDto deviceInfoDto = new DeviceInfoDto();
                deviceInfoDto.setProductId(deviceVo.getProductId());
                deviceInfoDto.setDeviceId(deviceVo.getDeviceId());
                deviceInfos.add(deviceInfoDto);
            });
            messageService.deleteMessageByUserId(userVo.getId(), deviceInfos);
        }
        // 删除用户和设备的关联关系
        appDeviceService.deleteUserDevice(userVo.getId());
        // 清空设备分享
        deviceShareService.clearShareOfUser(userVo.getId());
        // 删除用户的设置信息
        userSettingService.deleteSetting(userVo.getId());
        // 删除用户信息
        userCommandService.logOffUser(userVo.getEmail());
        // 撤销协议
        appAgreementService.withdraw();
        // 删除用户登录token
        StpUtil.logout();
        RedisUtils.deleteObject(StpUtil.getTokenValue());
        return true;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public LoginUserResp login(LoginDto req, String lang) {
        if (req.getCheck() == null || !req.getCheck()) {
            throw ExceptionMessageUtil.getException(AppErrorCode.APP_REGISTER_CHECK_ERROR);
        }
        EmailUtil.check(req.getEmail());
        // 远程调用用户中台接口
        UserLoginDto dto = new UserLoginDto();
        dto.setEmail(req.getEmail());
        dto.setPassword(req.getPassword());
        UserVo appUserVo = userQueryService.login(dto);
        StpUtil.login(appUserVo.getId());
        LoginUserResp loginUser = new LoginUserResp();
        String token = StpUtil.getTokenValue();
        loginUser.setToken(token);
        loginUser.setUserInfo(appUserVo);
        RedisUtils.setCacheObject(loginUser.getToken(), appUserVo);
        // 设置短token
        String shortToken = SnowFlake.nextId() + "";
        RedisUtils.setCacheObject(shortToken, token);
        loginUser.setShortToken(shortToken);

        // 设置login context
        LoginSysUser app = new LoginSysUser();
        app.setId(StpUtil.getLoginIdAsLong());
        app.setLoginType("app");
        LoginUserContext.setUser(app);
        StpUtil.getSession().set(StpUtil.getLoginIdAsString(), app);

        // 设置协议和设置语言环境
        commonService.setAgreementAndLang(appUserVo.getId(), req.getEmail(), lang);
        // 设置语言
        UserSetting one = userSettingService.getOne(new LambdaQueryWrapper<UserSetting>().eq(UserSetting::getUserId, StpUtil.getLoginIdAsLong()));
        if (one != null) {
            userSettingService.setUserLanguage(StpUtil.getLoginIdAsLong(), lang);
        }else {
            //如果登录时没有用户配置信息，创建一条默认值
            insertDefaultUserSetting(StpUtil.getLoginIdAsLong(), lang,req.getEmail());
        }

        return loginUser;
    }

    @Override
    public void logout() {
        String token = StpUtil.getTokenValue();
        if (token == null) {
            return;
        }
        UserVo user = RedisUtils.getCacheObject(token);
        if (user != null && user.getId() != null) {
            userSettingService.update(new UserSetting(), new LambdaUpdateWrapper<UserSetting>()
                    .eq(UserSetting::getUserId, user.getId())
                    .set(UserSetting::getPushToken, null));
            // 设置用户的在线状态为在线
            userCommandService.editUserPresenceState(user.getId(), "offline");
        }
        StpUtil.logout();
        RedisUtils.deleteObject(token);
    }

    @Override
    public void delete() {
        String token = StpUtil.getTokenValue();
        if (StringUtils.isEmpty(token)) {
            throw ExceptionMessageUtil.getException(AppErrorCode.APP_TOKEN_NOT_FOUND);
        }
        UserVo userVo = RedisUtils.getCacheObject(token);
        if (null == userVo) {
            throw ExceptionMessageUtil.getException(AppErrorCode.APP_TOKEN_INVALID);
        }
        delete(userVo);
        // 删除用户登录token
        StpUtil.logout();
        RedisUtils.deleteObject(StpUtil.getTokenValue());
    }

    @Override
    public void deleteByEmail(String email) {
        UserVo userVo = userQueryService.getUserInfo(email);
        if (null == userVo) {
            log.error("deleteByEmail#{} -> user not find",email);
            throw ExceptionMessageUtil.getException(AppErrorCode.APP_GET_USER_FAIL);
        }
        delete(userVo);
        StpUtil.logout(userVo.getId());
    }

    private void delete(UserVo userVo) {
        log.debug("AppUserServiceImpl#delete -> delete user");
        // 删除用户消息相关的消息
        List<DeviceRpcVo> deviceVos = appDeviceService.listBoundDevices(userVo);
        LastMessageDto lastMessage = new LastMessageDto();
        if (CollectionUtils.isEmpty(deviceVos)) {
            lastMessage.setDeviceInfoList(null);
        } else {
            List<DeviceInfoDto> deviceInfos = new ArrayList<>(deviceVos.size());
            deviceVos.forEach(deviceVo -> {
                DeviceInfoDto deviceInfoDto = new DeviceInfoDto();
                deviceInfoDto.setProductId(deviceVo.getProductId());
                deviceInfoDto.setDeviceId(deviceVo.getDeviceId());
                deviceInfos.add(deviceInfoDto);
            });
            messageService.deleteMessageByUserId(userVo.getId(), deviceInfos);
        }
        // 删除用户和设备的关联关系
        appDeviceService.deleteUserDevice(userVo.getId());
        // 清空设备分享
        deviceShareService.clearShareOfUser(userVo.getId());
        // 删除用户的设置信息
        userSettingService.deleteSetting(userVo.getId());
        // 删除用户信息
        userCommandService.logOffUser(userVo.getEmail());
        // 撤销协议
        appAgreementService.withdraw(userVo.getId());

    }
    @Override
    public String uploadPhoto(PhotoDto req) {
        UserEditDto userEditDto = new UserEditDto();
        UserVo userVo = RedisUtils.getCacheObject(StpUtil.getTokenValue());
        log.debug("uploadPhoto userVo:{}", userVo);
        userEditDto.setId(userVo.getId());
        String fileName = IotAppCommonConstant.PHOTO_PATH + IotAppCommonConstant.FILE_SPLIT_CODE + req.getPhoto();
        userEditDto.setPhoto(fileName);
        log.debug("uploadPhoto:{}", userEditDto);
        userCommandService.editUserPhoto(userEditDto);
        // 更新redis中的用户信息
        String s3Url = UrlUtil.completeUrl(awsProperties.getPictureBucket().getCdnHost(), fileName);
        userVo.setPhoto(s3Url);
        RedisUtils.setCacheObject(StpUtil.getTokenValue(), userVo);
        return s3Url;
    }

    @Override
    public UserVo info() {
        UserVo userVo = userQueryService.getUserInfo(StpUtil.getLoginIdAsLong());
        return userVo;
    }

    @Override
    public void resetPassword(ResetPwdDto req) {
        //修改用户信息
        EmailUtil.check(req.getEmail());
        ResetPasswordDto dto = new ResetPasswordDto();
        BeanUtils.copyProperties(req, dto);
        userCommandService.resetPassword(dto);
        // 删除token,重新登录
        String token = StpUtil.getTokenValue();
        if (token != null && !"".equals(token)) {
            RedisUtils.deleteObject(token);
        }
    }

    @Override
    public Boolean confirmPassword(ConfirmPwdDto req) {
        EmailUtil.check(req.getEmail());
        ConfirmPasswordDto dto = new ConfirmPasswordDto();
        BeanUtils.copyProperties(req, dto);
        return userCommandService.confirmPassword(dto);
    }

    @Override
    public void editPassword(EditPwdDto req) {
        EmailUtil.check(req.getEmail());
        EditPasswordDto dto = new EditPasswordDto();
        BeanUtils.copyProperties(req, dto);
        userCommandService.editPassword(dto);
        // 删除token,重新登录
        String token = StpUtil.getTokenValue();
        RedisUtils.deleteObject(token);
    }

    @Override
    public UserVo editInfo(SaveDto req) {
        if (req == null || req.getId() == null) {
            throw ExceptionMessageUtil.getException(AppErrorCode.APP_USER_ID_BLANK);
        }
        // 用户firstName和lastName校验
        checkName(req.getFirstName(), req.getLastName());

        UserVo userVo = userQueryService.getUserInfo(req.getId());
        userVo.setFirstName(req.getFirstName());
        userVo.setLastName(req.getLastName());

        // 组装官网用户对象UserEditDto
        String sfUserId = userVo.getSfUserId();
        UserEditDto userEditDto = ConvertUtil.convert(req, UserEditDto.class);
        userEditDto.setSfUserId(sfUserId);
        // 更新用户信息到数据库表及同步SF官网
        userCommandService.editUserInfo(userEditDto);

        // 更新redis中的用户信息
        RedisUtils.setCacheObject(StpUtil.getTokenValue(), userVo);
        return userVo;
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void reportPhoneInfo(PhoneInfoDto phoneInfoDto) {
        if (phoneInfoDto == null) {
            return;
        }
        // 处理数据，上报到aws有正则要求[a-zA-Z0-9_.,@/:#-]*]，暂时只考虑去除空格
        if (StringUtils.isNotBlank(phoneInfoDto.getPhoneModel())) {
            phoneInfoDto.setPhoneModel(phoneInfoDto.getPhoneModel().replace(" ", ""));
        }
        if (StringUtils.isNotBlank(phoneInfoDto.getPhoneOsVersion())) {
            phoneInfoDto.setPhoneOsVersion(phoneInfoDto.getPhoneOsVersion().replace(" ", ""));
        }
        if (StringUtils.isNotBlank(phoneInfoDto.getAppVersion())) {
            phoneInfoDto.setAppVersion(phoneInfoDto.getAppVersion().replace(" ", ""));
        }
        if (StringUtils.isNotBlank(phoneInfoDto.getIp())) {
            phoneInfoDto.setIp(phoneInfoDto.getIp().replace(" ", ""));
        }
        if (StringUtils.isNotBlank(phoneInfoDto.getAppTypeCode())) {
            phoneInfoDto.setAppTypeCode(phoneInfoDto.getAppTypeCode().replace(" ", ""));
        }
        if (!CollectionUtils.isEmpty(phoneInfoDto.getRnMap())) {
            Map<String, String> m = new HashMap<>();
            phoneInfoDto.getRnMap().forEach((k, v) -> {
                String key = null;
                if (k != null) {
                    key = k.replace(" ", "");
                }
                String value = null;
                if (v != null) {
                    value = v.replace(" ", "");
                }
                m.put(key, value);
            });
            phoneInfoDto.setRnMap(m);
        }
        UserVo userVo = RedisUtils.getCacheObject(StpUtil.getTokenValue());
        // 手机信息同步给用户中心
        userCommandService.reportPhoneInfo(userVo.getId(), phoneInfoDto);

        if (phoneInfoDto.getRnMap() != null) {
            List<String> existedDeviceIds = appUserRnService.list(new LambdaQueryWrapper<AppUserRn>()
                    .eq(AppUserRn::getUserId, userVo.getId())
                    .select(AppUserRn::getDeviceId)).stream().map(AppUserRn::getDeviceId).filter(Objects::nonNull).collect(Collectors.toList());
            List<String> notDeleteDeviceIds = new ArrayList<>();
            List<AppUserRn> appUserRnList = new ArrayList<>();
            for (String key : phoneInfoDto.getRnMap().keySet()) {
                AppUserRn appUserRn = new AppUserRn();
                appUserRn.setUserId(userVo.getId());
                appUserRn.setDeviceId(key);
                appUserRn.setRnVersion(phoneInfoDto.getRnMap().get(key));
                if (existedDeviceIds.contains(key)) {
                    notDeleteDeviceIds.add(key);
                    appUserRnService.update(appUserRn, new LambdaQueryWrapper<AppUserRn>()
                            .eq(AppUserRn::getUserId, userVo.getId()).eq(AppUserRn::getDeviceId, key));
                } else {
                    appUserRnList.add(appUserRn);
                }
            }
            if (!appUserRnList.isEmpty()) {
                appUserRnService.saveBatch(appUserRnList);
            }
            existedDeviceIds.removeAll(notDeleteDeviceIds);
            if (CollectionUtil.isNotEmpty(existedDeviceIds)) {
                appUserRnService.remove(new LambdaQueryWrapper<AppUserRn>()
                        .in(AppUserRn::getDeviceId, existedDeviceIds));
            }
        }
    }

    public void checkName(String firstName, String lastName){
        if (StringUtils.isNotEmpty(firstName) && firstName.length() > CommonConstant.USER_FIRSTNAME_LEN_LIMIT) {
            throw ExceptionMessageUtil.getException(AppErrorCode.USER_FIRST_NAME_TOO_LONG);
        }
        if (StringUtils.isNotEmpty(lastName) && lastName.length() > CommonConstant.USER_LASTNAME_LEN_LIMIT) {
            throw ExceptionMessageUtil.getException(AppErrorCode.USER_LAST_NAME_TOO_LONG);
        }

    }

    @Override
    public void checkEmail(String email) {
        //校验邮箱格式
        EmailUtil.check(email);
        email=email.trim();
        //校验邮箱是否存在
        if (userQueryService.checkEmailUsed(email)) {
            throw ExceptionMessageUtil.getException(UserCenterErrorCode.USER_CENTER_EMAIL_USED, email);
        }
    }
}
