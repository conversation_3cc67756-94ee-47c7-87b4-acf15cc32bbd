package com.chervon.iot.app.controller;

import com.chervon.common.core.domain.PageResult;
import com.chervon.common.core.domain.R;
import com.chervon.iot.middle.api.dto.log.ShadowLogPageDto;
import com.chervon.iot.middle.api.service.RemoteDeviceShadowService;
import com.chervon.iot.middle.api.vo.log.DeviceShadowLogVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @date 2022-07-13
 */
@Api(tags = "iot Log 相关接口")
@RestController
@RequestMapping("/iot/log")
public class IotLogController {
    @DubboReference
    private RemoteDeviceShadowService remoteDeviceShadowService;
    /**
     * 分页获取设备属性历史
     */
    @ApiOperation("分页获取设备属性历史")
    @PostMapping("/page")
    public R<PageResult<DeviceShadowLogVo>> pageDeviceShadowLog(
        @RequestBody ShadowLogPageDto shadowLogPageDto) {
        PageResult<DeviceShadowLogVo> result = remoteDeviceShadowService
            .pageDeviceShadowLog(shadowLogPageDto);
        return R.ok(result);
    }
}
