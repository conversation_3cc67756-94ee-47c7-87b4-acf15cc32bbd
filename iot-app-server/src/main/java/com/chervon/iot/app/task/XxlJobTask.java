package com.chervon.iot.app.task;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.chervon.common.core.prop.SfProperties;
import com.chervon.common.core.utils.StringUtils;
import com.chervon.iot.app.domain.dataobject.DeviceInfo;
import com.chervon.iot.app.domain.dataobject.DeviceInfoSyncFail;
import com.chervon.iot.app.domain.dataobject.UserSetting;
import com.chervon.iot.app.mapper.DeviceInfoMapper;
import com.chervon.iot.app.service.DeviceInfoAsyncService;
import com.chervon.iot.app.service.DeviceInfoService;
import com.chervon.iot.app.service.DeviceShareService;
import com.chervon.iot.app.service.DeviceInfoSyncFailService;
import com.chervon.iot.app.service.UserSettingService;
import com.chervon.usercenter.api.service.SaleForceService;
import com.chervon.usercenter.api.vo.sf.SfQueryVo;
import com.chervon.usercenter.api.vo.sf.SfWarrantyRecord;
import com.google.common.base.Splitter;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/6/28
 */
@Component
@Slf4j
public class XxlJobTask {

    private final DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss.SSSZ");

    @DubboReference(timeout=20000, group = "${sf.direction}")
    private SaleForceService saleForceService;
    @Autowired
    private SfProperties sfProperties;
    @Autowired
    private DeviceInfoService deviceInfoService;
    @Autowired
    private DeviceInfoMapper deviceInfoMapper;
    @Autowired
    private DeviceInfoAsyncService deviceInfoAsyncService;
    @Autowired
    private UserSettingService userSettingService;
    @Autowired
    private DeviceShareService deviceShareService;
    @Autowired
    private DeviceInfoSyncFailService deviceInfoSyncFailService;

    /**
     * 同步SF最近更新的质保到IoT
     */
    @XxlJob("syncSfWarrantyRecentlyUpdated")
    public void syncSfWarrantyRecentlyUpdated() {
        if(sfProperties.isEnable()) {
            long startTime = System.currentTimeMillis();
            log.info("XxlJobTask#syncSfWarrantyRecentlyUpdated -> 同步SF最近更新的质保开始");
            Long syncTime = saleForceService.getWarrantySyncTime();
            if(syncTime == null) {
                DeviceInfo deviceInfo = deviceInfoMapper.selectOne(new LambdaQueryWrapper<DeviceInfo>()
                        .select(DeviceInfo::getLastSyncTime, DeviceInfo::getId)
                        .orderByDesc(DeviceInfo::getLastSyncTime).last("LIMIT 1"));
                if(deviceInfo != null && deviceInfo.getLastSyncTime() != null) {
                    syncTime = deviceInfo.getLastSyncTime().toEpochSecond(ZoneOffset.UTC)*1000L;
                } else {
                    syncTime = System.currentTimeMillis() - 300000L;
                }
            }
            List<SfWarrantyRecord> sfWarrantyRecords = saleForceService.listSfWarrantyLastUpdated(syncTime);
            log.info("XxlJobTask#syncSfWarrantyRecentlyUpdated ->从SF获取最近更新的质保数量为{}", sfWarrantyRecords.size());
            try {
                deviceInfoService.syncDeviceFromCrm(sfWarrantyRecords);
            } catch (Exception e) {
                log.warn("XxlJobTask#syncSfWarrantyRecentlyUpdated, error: ", e);
            }
            if(sfWarrantyRecords.isEmpty()) {
                saleForceService.updateWarrantySyncTime(startTime);
            } else {
                String lastModifiedDate = sfWarrantyRecords.get(sfWarrantyRecords.size() - 1).getLastModifiedDate();
                LocalDateTime dateTime = LocalDateTime.parse(lastModifiedDate, dateTimeFormatter);
                saleForceService.updateWarrantySyncTime(dateTime.toEpochSecond(ZoneOffset.UTC) * 1000L);
            }
            log.info("XxlJobTask#syncSfWarrantyRecentlyUpdated -> 同步SF最近更新的质保结束,耗时{}ms."
                    , System.currentTimeMillis() - startTime);
        }
    }

    /**
     * 全量同步SF平台的质保到IoT
     */
    @XxlJob("syncAllSfWarranty")
    public void syncAllSfWarranty() {
        long startTime = System.currentTimeMillis();
        String queryUrl = XxlJobHelper.getJobParam();
        log.info("XxlJobTask#syncAllSfWarranty -> 全量同步SF质保开始, queryUrl:{}", queryUrl);
        SfQueryVo<SfWarrantyRecord> warrantyVo = saleForceService.batchSfWarranty(queryUrl);
        try {
            deviceInfoService.syncDeviceFromCrm(warrantyVo.getRecords());
        } catch (Exception e) {
            log.warn("XxlJobTask#syncAllSfWarranty, error: ", e);
        }
        String lastModifiedDate = null;
        if(!warrantyVo.getRecords().isEmpty()) {
            lastModifiedDate = warrantyVo.getRecords().get(warrantyVo.getRecords().size() - 1).getLastModifiedDate();
            log.info("XxlJobTask#syncAllSfWarranty -> 全量同步SF质保分批结束, sfTime:{}", lastModifiedDate);
        }

        while(!warrantyVo.getDone()) {
            String nextRecordsUrl = warrantyVo.getNextRecordsUrl();
            warrantyVo = saleForceService.batchSfWarranty(nextRecordsUrl);
            List<SfWarrantyRecord> records = warrantyVo.getRecords();
            try {
                deviceInfoService.syncDeviceFromCrm(records);
            } catch (Exception e) {
                log.warn("XxlJobTask#syncAllSfWarranty, error: ", e);
            }
            lastModifiedDate = records.get(records.size() - 1).getLastModifiedDate();
            log.info("XxlJobTask#syncAllSfWarranty -> 全量同步SF质保分批结束, url:{}, sfTime:{}"
                    , nextRecordsUrl, lastModifiedDate);
        }

        if(lastModifiedDate != null) {
            LocalDateTime dateTime = LocalDateTime.parse(lastModifiedDate, dateTimeFormatter);
            saleForceService.updateWarrantySyncTime(dateTime.toEpochSecond(ZoneOffset.UTC) * 1000L);
        }
        log.info("XxlJobTask#syncAllSfWarranty -> 全量同步SF质保结束,耗时{}ms.", System.currentTimeMillis() - startTime);
    }

    /**
     * 同步指定SF质保到IoT
     */
    @XxlJob("syncOneSfWarranty")
    public void syncOneSfWarranty() {
        String jobParam = XxlJobHelper.getJobParam();
        log.info("XxlJobTask#syncOneSfWarranty, jobParam:{}", jobParam);
        if(StringUtils.isEmpty(jobParam)) {
            return;
        }

        String[] split = jobParam.split(",");
        for(String sn : split) {
            log.info("XxlJobTask#syncOneSfWarranty -> 同步指定质保开始, sn:{}", sn);
            List<SfWarrantyRecord> sfWarrantyRecord = saleForceService.getWarrantyBySn(sn);
            if(sfWarrantyRecord == null) {
                log.info("no warranty found, sn:{}", sn);
                continue;
            }
            try {
                deviceInfoService.syncDeviceFromCrm(sfWarrantyRecord);
            } catch (Exception e) {
                log.warn("XxlJobTask#syncOneSfWarranty, error: ", e);
            }
            log.info("XxlJobTask#syncOneSfWarranty -> 同步指定质保结束, sn:{}", sn);
        }
    }

    /**
     * 同步登陆过的用户质保到IoT
     */
    @XxlJob("syncUserSfWarranty")
    public void syncUserSfWarranty() {
        String jobParam = XxlJobHelper.getJobParam();
        if(StringUtils.isNotEmpty(jobParam)) {
            String[] split = jobParam.split(",");
            for(String userId : split) {
                try {
                    deviceInfoAsyncService.syncUserWarranty(Long.parseLong(userId));
                } catch (Exception e) {
                    log.warn("syncUserWarranty error, userId:{}, ", userId, e);
                }
            }
            return;
        }

        int pageSize = 1000;
        int index = 0;
        List<UserSetting> list = userSettingService.list(new LambdaQueryWrapper<UserSetting>()
                .select(UserSetting::getUserId).last("LIMIT "+index+", "+pageSize));
        while(CollectionUtils.isNotEmpty(list)) {
            for(UserSetting userSetting : list) {
                try {
                    deviceInfoAsyncService.syncUserWarranty(userSetting.getUserId());
                } catch (Exception e) {
                    log.warn("syncUserWarranty error, userId:{}, ", userSetting.getUserId(), e);
                }
            }
            index += pageSize;
            list = userSettingService.list(new LambdaQueryWrapper<UserSetting>()
                    .select(UserSetting::getUserId).last("LIMIT "+index+", "+pageSize));
        }
    }

    /**
     * 处理已经过期的设备分享
     */
    @XxlJob("expireDeviceShare")
    public void expireDeviceShare() {
        deviceShareService.expire();
    }

    /**
     * 再次同步同步失败质保数据
     */
    @XxlJob("syncWarrantyFail")
    public void syncWarrantyFail() {
        String jobParam = XxlJobHelper.getJobParam();
        if(StringUtils.isNotEmpty(jobParam)) {
            List<String> ids = Splitter.on(",").splitToList(jobParam);
            try {
                List<DeviceInfoSyncFail> deviceInfoSyncFails = deviceInfoSyncFailService.listByIds(ids);
                deviceInfoSyncFailService.dealSyncFailList(deviceInfoSyncFails);
            } catch (Exception e) {
                log.warn("sync fail warranty error, jobParam:{}", jobParam, e);
            }
            return;
        }
        int limit = 100;
        List<DeviceInfoSyncFail> notDealSyncFail = deviceInfoSyncFailService.findNotDealSyncFail(limit);
        deviceInfoSyncFailService.dealSyncFailList(notDealSyncFail);
    }
}
