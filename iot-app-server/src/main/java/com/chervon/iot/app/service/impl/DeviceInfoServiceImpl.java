package com.chervon.iot.app.service.impl;

import cn.dev33.satoken.stp.StpUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.chervon.common.core.constant.CommonConstant;
import com.chervon.common.core.prop.AwsProperties;
import com.chervon.common.core.utils.ConvertUtil;
import com.chervon.common.core.utils.StringUtils;
import com.chervon.common.oss.uitl.S3Util;
import com.chervon.common.oss.uitl.UrlUtil;
import com.chervon.iot.app.api.exception.AppErrorCode;
import com.chervon.iot.app.api.exception.AppException;
import com.chervon.iot.app.config.ExceptionMessageUtil;
import com.chervon.iot.app.config.IotAppCommonConstant;
import com.chervon.iot.app.domain.dataobject.AppUserDevice;
import com.chervon.iot.app.domain.dataobject.DeviceInfo;
import com.chervon.iot.app.domain.dataobject.DeviceInfoReceipt;
import com.chervon.iot.app.domain.dataobject.DeviceInfoSyncTemp;
import com.chervon.iot.app.domain.dataobject.promotion.RecommendedAccessory;
import com.chervon.iot.app.domain.dataobject.*;
import com.chervon.iot.app.domain.dto.device.DeviceInfoAddDto;
import com.chervon.iot.app.domain.dto.device.DeviceInfoDetailDto;
import com.chervon.iot.app.domain.dto.device.UploadReceiptDto;
import com.chervon.iot.app.domain.enums.ApplyWithEnum;
import com.chervon.iot.app.domain.enums.DeviceInfoSyncDealStatusEnum;
import com.chervon.iot.app.domain.enums.ProductTypeEnum;
import com.chervon.iot.app.domain.enums.PurchasePlaceEnum;
import com.chervon.iot.app.domain.enums.ReceiptInformationStatusEnum;
import com.chervon.iot.app.domain.vo.PreSignedUrlVo;
import com.chervon.iot.app.domain.vo.device.DeviceInfoVo;
import com.chervon.iot.app.domain.vo.device.PartsInfoVo;
import com.chervon.iot.app.domain.vo.dict.DictNode;
import com.chervon.iot.app.domain.vo.dict.DictVo;
import com.chervon.iot.app.mapper.DeviceInfoMapper;
import com.chervon.iot.app.mapper.DeviceInfoSyncTempMapper;
import com.chervon.iot.app.service.AppDeviceService;
import com.chervon.iot.app.service.DeviceInfoAsyncService;
import com.chervon.iot.app.service.DeviceInfoReceiptService;
import com.chervon.iot.app.service.DeviceInfoService;
import com.chervon.iot.app.service.DeviceInfoSyncFailService;
import com.chervon.iot.app.service.DeviceWarrantyQuestionnaireService;
import com.chervon.iot.app.service.DictService;
import com.chervon.iot.app.service.RecommendAccessoryService;
import com.chervon.iot.app.util.DateUtils;
import com.chervon.operation.api.RemotePartsService;
import com.chervon.operation.api.dto.RecommendPartsDto;
import com.chervon.technology.api.RemoteDeviceCodeService;
import com.chervon.technology.api.RemoteDeviceManageService;
import com.chervon.technology.api.RemoteProductService;
import com.chervon.technology.api.dto.ProductReleaseEnum;
import com.chervon.technology.api.exception.TechnologyErrorCode;
import com.chervon.technology.api.vo.DeviceBindBo;
import com.chervon.technology.api.vo.DeviceCodeRpcVo;
import com.chervon.technology.api.vo.DeviceRpcVo;
import com.chervon.technology.api.vo.ProductRpcVo;
import com.chervon.usercenter.api.service.UserQueryService;
import com.chervon.usercenter.api.vo.UserVo;
import com.chervon.usercenter.api.vo.sf.SfWarrantyRecord;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;
import org.springframework.transaction.support.TransactionSynchronizationManager;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2022-08-31 20:19
 **/
@Service
@Slf4j
public class DeviceInfoServiceImpl extends ServiceImpl<DeviceInfoMapper, DeviceInfo> implements DeviceInfoService {

    private static final int MIN_PURCHASE_YEAR = 2000;
    private static final String LIMIT_ONE_STR = "LIMIT 1";
    private static DateTimeFormatter dateFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");

    @Autowired
    private S3Util s3Util;
    @Autowired
    @Lazy
    private DeviceInfoAsyncService deviceInfoAsyncService;
    @Resource
    private DeviceInfoReceiptService deviceInfoReceiptService;
    @Resource
    private DeviceWarrantyQuestionnaireService deviceWarrantyQuestionnaireService;
    @DubboReference
    private RemoteDeviceManageService remoteDeviceManageService;
    @DubboReference
    private RemoteProductService remoteProductService;
    @Autowired
    private AwsProperties awsProperties;
    @Autowired
    private DeviceInfoSyncFailService deviceInfoSyncFailService;
    @Autowired
    private DeviceInfoSyncTempMapper deviceInfoSyncTempMapper;
    @Autowired
    private AppDeviceService appDeviceService;
    @DubboReference
    private RemoteDeviceCodeService remoteDeviceCodeService;
    @DubboReference
    private UserQueryService userQueryService;
    @Value("${sf.direction}")
    private String direction;
    @Autowired
    private DictService dictService;
    @Autowired
    private RecommendAccessoryService recommendAccessoryService;
    @DubboReference
    private RemotePartsService remotePartsService;

    @Override
    public String checkAddBefore(@Valid DeviceInfoAddDto dto){
        String sfSn = getSn(dto);
        Boolean alreadyRegistered = ifDeviceAlreadyRegistered(dto.getDeviceId());
        if (Boolean.TRUE.equals(alreadyRegistered)) {
            throw ExceptionMessageUtil.getException(AppErrorCode.APP_DEVICE_INFO_ALREADY_REGISTERED, dto.getDeviceId());
        }
        if (StringUtils.isNotEmpty(dto.getSn())) {
            DeviceInfo snDeviceInfo = findDeviceInfoBySn(sfSn);
            //如果sn有值，deviceId为空的质保记录，将deviceId更新，且device表更新sn
            if (snDeviceInfo != null) {
                snDeviceInfo.setDeviceId(dto.getDeviceId());
                this.updateById(snDeviceInfo);
                remoteDeviceManageService.updateSnByDeviceId(dto.getDeviceId(),sfSn);
                throw ExceptionMessageUtil.getException(
                        AppErrorCode.APP_DEVICE_INFO_ALREADY_REGISTERED, dto.getDeviceId());
            }
        }
        return sfSn;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public PartsInfoVo add(@Valid DeviceInfoAddDto dto,String sfSn) {
        DeviceInfo deviceInfo = ConvertUtil.convert(dto, DeviceInfo.class);
        LocalDateTime localDateTime = checkAndConvertPurchaseTime(dto.getPurchaseTime());
        checkSnPurchaseTime(sfSn, localDateTime);
        deviceInfo.setPurchaseTime(localDateTime);
        // 截止时间默认为3年
        LocalDateTime expireTime = localDateTime.plusYears(CommonConstant.THREE);
        // 根据是否填写维保问卷设置维保到期时间(如果填过问卷额外加2年)
        deviceInfo.setExpireTime((Objects.equals(dto.getIfCheckedWarranty(), CommonConstant.ONE)) ?
                expireTime.plusYears(CommonConstant.TWO) : expireTime);
        deviceInfo.setLastSyncTime(LocalDateTime.now());
        deviceInfo.setSourceCode(CommonConstant.ZERO);
        this.save(deviceInfo);
        registerKitSn(dto, deviceInfo, sfSn);
        bindMoreDevice(dto.getDeviceId(), sfSn);

        // 如果收据文件Key列表不为空
        if (!CollectionUtils.isEmpty(dto.getReceiptFileKey())) {
            List<DeviceInfoReceipt> deviceInfoReceiptList = new ArrayList<>();
            for (String receiptFileKey : dto.getReceiptFileKey()) {
                DeviceInfoReceipt deviceInfoReceipt = new DeviceInfoReceipt();
                deviceInfoReceipt.setDeviceId(dto.getDeviceId());
                deviceInfoReceipt.setReceiptFileKey(receiptFileKey);
                deviceInfoReceiptList.add(deviceInfoReceipt);
            }
            deviceInfoReceiptService.saveBatch(deviceInfoReceiptList);
        }
        // 异步注册设备质保到SaleForce
        // 暂时不异步
        deviceInfoAsyncService.registerToSaleForce(deviceInfo, dto, sfSn, StpUtil.getLoginIdAsLong());
        saveSyncTemp(sfSn, dto.getKitSnList());

        /**
         * 用户手动输入SN不为空，更新设备表SN
         */
        if (StringUtils.isNotEmpty(dto.getSn())) {
            remoteDeviceManageService.updateSnByDeviceId(dto.getDeviceId(), dto.getSn());
        }

        // 注册完质保后，获取推荐配件信息
        try {
            String snCode = getSnCodeFromSn(dto.getSn());
            RecommendedAccessory recommendedAccessory = recommendAccessoryService.selectAccessoryBySnCode(snCode);

            if (recommendedAccessory == null) {
                log.info("No accessories to recommend!");
                return null;
            }
            RecommendPartsDto partsDto = remotePartsService.getRecommendParts(recommendedAccessory.getRecommendedAccessory());
            if (partsDto == null) {
                log.warn("Accessories information is not maintained!");
                return null;
            }
            return buildPartsInfoVo(partsDto);
        } catch (Exception e) {
            log.error("Failed to get recommended accessories: ", e);
            return null;
        }
    }

    // 抽取构建PartsInfoVo的方法
    private PartsInfoVo buildPartsInfoVo(RecommendPartsDto partsDto) {
        PartsInfoVo partsInfoVo = new PartsInfoVo();
        partsInfoVo.setPartId(partsDto.getPartId());
        partsInfoVo.setPartModel(partsDto.getPartModel());
        partsInfoVo.setPartName(partsDto.getPartName());
        partsInfoVo.setImageUrl(partsDto.getPartIconUrl());
        partsInfoVo.setShopUrl(partsDto.getPartShopUrl());
        return partsInfoVo;
    }

    /**
     * 检查并转换购买时间
     *
     * @param purchaseTime
     * @return
     */
    private LocalDateTime checkAndConvertPurchaseTime(Long purchaseTime) {
        if (DateUtils.localDateIsAfter(DateUtils.timestamp2LocalDate(purchaseTime), LocalDate.now())) {
            throw ExceptionMessageUtil.getException(AppErrorCode.DEVICE_INFO_PURCHASETIME_EXCEED, purchaseTime);
        }
        return DateUtils.timestamp2LocalDateTime(purchaseTime);
    }


    /**
     * 获取sn
     */
    private String getSn(DeviceInfoAddDto dto) {
        String deviceId = dto.getDeviceId();
        String sn = dto.getSn();

        DeviceCodeRpcVo deviceCodeRpcVo = remoteDeviceCodeService.deviceCodeDetail(deviceId);
        if (Objects.nonNull(deviceCodeRpcVo) && StringUtils.isNotEmpty(deviceCodeRpcVo.getSn())) {
            return deviceCodeRpcVo.getSn();
        }
        if (StringUtils.isNotEmpty(sn)) {
            //用户手动输入sn,校验格式
            String manualSn = checkAndConvertSn(sn);
            //校验SN和deviceId多码关系是否正确
            checkSnAndDeviceId(manualSn,deviceId);
            return manualSn;
        }
        throw ExceptionMessageUtil.getException(AppErrorCode.APP_DEVICE_INFO_REGISTER_FAILED, deviceId);
    }

    /**
     * 校验SN和deviceId多码关系是否正确
     * @param manualSn
     * @param deviceId
     */
    private void checkSnAndDeviceId(String manualSn,String deviceId){
        if (!Objects.equals(getSnCodeFromSn(manualSn), deviceId.substring(1, 5))) {
            throw ExceptionMessageUtil.getException(AppErrorCode.APP_DEVICE_INFO_REGISTER_FAILED, deviceId);
        }
        //根据SN码查询多码关系, 比较deviceId
        DeviceCodeRpcVo deviceCodeDetailBySn = remoteDeviceCodeService.deviceCodeDetailBySn(manualSn);
        if (deviceCodeDetailBySn != null && !deviceId.equals(deviceCodeDetailBySn.getDeviceId())) {
            throw ExceptionMessageUtil.getException(AppErrorCode.DEVICE_INFO_SN_ERROR, manualSn);
        }
        DeviceRpcVo deviceById = remoteDeviceManageService.deviceDetail(deviceId);
        //查询设备表是否有数据
        if (Objects.isNull(deviceById)) {
            throw ExceptionMessageUtil.getException(AppErrorCode.APP_DEVICE_INFO_REGISTER_FAILED, deviceId);
        }
        if (StringUtils.isEmpty(deviceById.getSn()) || !deviceById.getSn().equals(manualSn)) {
            DeviceRpcVo deviceBySn = remoteDeviceManageService.deviceDetailBySn(manualSn);
            if (deviceBySn != null && !deviceId.equals(deviceBySn.getDeviceId())) {
                throw ExceptionMessageUtil.getException(AppErrorCode.DEVICE_INFO_SN_ERROR, manualSn);
            }
        }
    }

    private String checkAndConvertSn(String sn) {
        //去空格，转大写
        String snConvert = checkAndConvertSnNoThrow(sn);
        if (StringUtils.isEmpty(snConvert)) {
            throw ExceptionMessageUtil.getException(AppErrorCode.TECHNOLOGY_DEVICE_CODE_SN_ERROR, sn);
        }
        return snConvert;
    }

    /**
     *
     * @param sn
     * @return
     */
    private String checkAndConvertSnNoThrow(String sn) {
        //去空格，转大写
        sn = sn.trim().toUpperCase();
        if (!sn.matches(IotAppCommonConstant.DEVICE_CODE_CHECK) &&
                !sn.matches(IotAppCommonConstant.NO_IOT_DEVICE_CODE_CHECK)) {
            return null;
        }
        return sn;
    }
    /**
     * 校验SN码和购买时间
     */
    private void checkSnPurchaseTime(String sn, LocalDateTime purchaseTime) {
        if (IotAppCommonConstant.NA.equals(direction)) {
            return;
        }
        int year = Integer.parseInt("20" + sn.substring(5, 7));
        int weeks = Integer.parseInt(sn.substring(7, 9));
        LocalDate date = LocalDate.of(year, 1, 1).plusWeeks(weeks);
        if (DateUtils.localDateIsAfter(date, purchaseTime.toLocalDate())) {
            throw ExceptionMessageUtil.getException(AppErrorCode.DEVICE_INFO_PURCHASETIME_SN_ERROR, sn);
        }
    }

    private String getSnCodeFromSn(String sn) {
        if (sn.matches(IotAppCommonConstant.DEVICE_CODE_CHECK)) {
            // 15位多码
            return sn.substring(1, 5);
        } else if (sn.matches(IotAppCommonConstant.NO_IOT_DEVICE_CODE_CHECK)) {
            // 16位，R开头的非IOT设备多码
            return sn.substring(2, 6);
        } else {
            throw ExceptionMessageUtil.getException(TechnologyErrorCode.TECHNOLOGY_DEVICE_CODE_SN_ERROR, sn);
        }
    }

    /**
     * 注册kitSn
     */
    private void registerKitSn(DeviceInfoAddDto dto, DeviceInfo deviceInfo, String sn) {
        List<String> kitSnList = dto.getKitSnList();
        if(CollectionUtils.isEmpty(kitSnList)) {
            return;
        }
        String parentDeviceId = deviceInfo.getDeviceId();
        Set<String> kitSnSet = Sets.newHashSet();
        for(String kitSn : kitSnList) {
            if (StringUtils.isEmpty(kitSn) || kitSnSet.contains(kitSn) || kitSn.equals(sn)) {
                continue;
            }
            this.checkSnPurchaseTime(kitSn, deviceInfo.getPurchaseTime());
            this.registerBindDevice(kitSn,parentDeviceId,deviceInfo);
            kitSnSet.add(kitSn);
        }
        deviceInfo.setDeviceId(parentDeviceId);
        deviceInfo.setParentDeviceId(null);
        //将重复的SN过滤掉，避免再去DeviceInfoAsyncService修改
        dto.setKitSnList(Lists.newArrayList(kitSnSet));
    }

    /**
     * kitSN去注册绑定用户
     * @param kitSn
     * @param parentDeviceId
     * @param deviceInfo
     */
    private void registerBindDevice(String kitSn,String parentDeviceId,DeviceInfo deviceInfo){
        ProductRpcVo productRpcVo = remoteProductService.getProductIdBySnCode(getSnCodeFromSn(kitSn));
        //产品型号是发布状态，才进行质保数据保存和设备绑定操作
        if (productRpcVo != null && ProductReleaseEnum.RELEASED.getValue().equals(productRpcVo.getReleaseStatus())) {
            String deviceId = this.bindSnOfDeviceInfo(kitSn, StpUtil.getLoginIdAsLong());
            if (StringUtils.isEmpty(deviceId)) {
                throw ExceptionMessageUtil.getException(AppErrorCode.APP_DEVICE_NOT_EXIST, kitSn);
            }
            final Boolean alreadyRegistered = ifDeviceAlreadyRegistered(deviceId);
            if (Boolean.TRUE.equals(alreadyRegistered)) {
                //检查质保是否存在
                throw ExceptionMessageUtil.getException(AppErrorCode.APP_DEVICE_INFO_ALREADY_REGISTERED, deviceId);
            }
            //如果质保未注册，且传了SN参数
            LambdaQueryWrapper<DeviceInfo> deviceInfoWrapper = new LambdaQueryWrapper<DeviceInfo>()
                    .eq(DeviceInfo::getSn, kitSn).isNull(DeviceInfo::getDeviceId)
                    .last(LIMIT_ONE_STR);
            DeviceInfo snDeviceInfo = baseMapper.selectOne(deviceInfoWrapper);
            //如果存在sn有值，deviceId为空的质保记录，将deviceId更新
            if(snDeviceInfo!=null){
                snDeviceInfo.setDeviceId(deviceId);
                this.updateById(snDeviceInfo);
            }else{
                deviceInfo.setId(null);
                deviceInfo.setDeviceId(deviceId);
                deviceInfo.setSn(kitSn);
                deviceInfo.setParentDeviceId(parentDeviceId);
                this.save(deviceInfo);
            }
        }
    }
    /**
     * 绑定用户设备(注册更多设备)
     *
     * @param deviceId 设备Id
     * @param sn       设备SN码
     */
    private void bindMoreDevice(String deviceId, String sn) {
        AppUserDevice existsOne = appDeviceService.getOne(new LambdaQueryWrapper<AppUserDevice>()
                .select(AppUserDevice::getId)
                .eq(AppUserDevice::getDeviceId, deviceId).eq(AppUserDevice::getUserId, StpUtil.getLoginIdAsLong())
                .last(LIMIT_ONE_STR));
        if (existsOne != null) {
            return;
        }
        ProductRpcVo productRpcVo = remoteProductService.getProductIdBySnCode(getSnCodeFromSn(sn));
        //产品型号是发布状态，才进行质保数据保存和设备绑定操作
        if (productRpcVo != null && ProductReleaseEnum.RELEASED.getValue().equals(productRpcVo.getReleaseStatus())) {
            bindSnOfDeviceInfo(sn, StpUtil.getLoginIdAsLong());
        }
    }

    @Override
    public DeviceInfo getByDeviceId(String deviceId) {
        LambdaQueryWrapper<DeviceInfo> deviceInfoLambdaQueryWrapper = new LambdaQueryWrapper<DeviceInfo>()
                .eq(DeviceInfo::getDeviceId, deviceId)
                .last(LIMIT_ONE_STR);
        return this.getOne(deviceInfoLambdaQueryWrapper);
    }

    private DeviceInfoVo changVo(DeviceInfo deviceInfo) {
        // 从配置中心获取字典对应多语言文案结果
        List<String> dictNames = Arrays.asList(IotAppCommonConstant.PURCHASE_PLACE, IotAppCommonConstant.APPLY_WITH);
        Map<String, DictVo> dictMap = dictService
                .listByDictName(LocaleContextHolder.getLocale().getLanguage(), dictNames)
                .stream().collect(Collectors.toMap(DictVo::getDictName, Function.identity()));
        Map<String, Map<String, String>> quickMap = new HashMap<>(dictMap.size());
        dictMap.forEach((key, value) -> {
            Map<String, String> contentMap = new HashMap<>(dictMap.get(key).getNodes().size());
            dictMap.get(key).getNodes().forEach(a -> contentMap.put(a.getLabel(), a.getDescription()));
            quickMap.put(key, contentMap);
        });

        // Vo的拼接
        DeviceInfoVo result = new DeviceInfoVo();
        result.setApplyWith(quickMap.get(IotAppCommonConstant.APPLY_WITH).get(deviceInfo.getApplyWith()));
        if (deviceInfo.getPurchasePlace().equals(IotAppCommonConstant.OTHER)) {
            result.setPurchasePlace(deviceInfo.getPurchasePlaceOther());
        } else {
            result.setPurchasePlace(quickMap.get(IotAppCommonConstant.PURCHASE_PLACE)
                    .get(deviceInfo.getPurchasePlace()));
        }
        result.setPurchaseTime(deviceInfo.getPurchaseTime());
        //获取注册质保收据信息
        List<String> receptFileUrls = deviceInfoReceiptService.listByDeviceId(deviceInfo.getDeviceId())
                .stream()
                .map(x -> UrlUtil.completeUrl(awsProperties.getReceiptBucket().getCdnHost(), x.getReceiptFileKey()))
                .collect(Collectors.toList());
        result.setReceiptFileUrls(receptFileUrls);
        return result;
    }

    @Override
    public DeviceInfoVo detail(@Valid DeviceInfoDetailDto dto) {
        DeviceInfo one = this.getByDeviceId(dto.getDeviceId());
        if (null == one) {
            throw ExceptionMessageUtil.getException(AppErrorCode.APP_DEVICE_INFO_NOT_REGISTERED, dto.getDeviceId());
        }
        return changVo(one);
    }

    @Override
    public Boolean ifDeviceAlreadyRegistered(String deviceId) {
        LambdaQueryWrapper<DeviceInfo> deviceInfoWrapper = new LambdaQueryWrapper<DeviceInfo>()
                .eq(DeviceInfo::getDeviceId, deviceId)
                .last(LIMIT_ONE_STR);
        return this.getOne(deviceInfoWrapper) != null;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public PreSignedUrlVo uploadReceipt(@Valid UploadReceiptDto dto) {
        String deviceId = dto.getDeviceId();
        String fileName = dto.getFileName();
        String key = IotAppCommonConstant.RECEIPT_PATH + IotAppCommonConstant.FILE_SPLIT_CODE;
        if (null != deviceId) {
            key = key + deviceId + IotAppCommonConstant.FILE_SPLIT_CODE + fileName;
        } else {
            key = key + fileName;
        }
        String preSignedUrl = s3Util.getPreSignedPutPrivateUrl(awsProperties.getReceiptBucket().getName(), key);
        PreSignedUrlVo preSignedUrlVo = new PreSignedUrlVo();
        preSignedUrlVo.setKey(key);
        preSignedUrlVo.setPreSignedUrl(preSignedUrl);
        return preSignedUrlVo;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteByDeviceId(@Valid DeviceInfoDetailDto deviceInfoDetailDto) {
        DeviceInfo deviceInfo = this.getByDeviceId(deviceInfoDetailDto.getDeviceId());
        if (null == deviceInfo) {
            throw ExceptionMessageUtil.getException(AppErrorCode.APP_DEVICE_INFO_NOT_REGISTERED1,
                    deviceInfoDetailDto.getDeviceId());
        }
        LambdaQueryWrapper<DeviceInfoReceipt> deviceInfoReceiptLambdaQueryWrapper
                = new LambdaQueryWrapper<DeviceInfoReceipt>()
                .eq(DeviceInfoReceipt::getDeviceId, deviceInfoDetailDto.getDeviceId());
        deviceInfoReceiptService.remove(deviceInfoReceiptLambdaQueryWrapper);
        this.removeById(deviceInfo);
    }

    @Override
    public boolean deviceIsRegistered(String deviceId) {
        long count = this.count(new LambdaQueryWrapper<DeviceInfo>().eq(DeviceInfo::getDeviceId, deviceId));
        return count > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void removeDeviceInfoBySn(String sn) {
        //sn校验
        sn = checkAndConvertSn(sn);
        //查询设备信息
        DeviceRpcVo deviceRpcVo = remoteDeviceManageService.deviceDetailBySn(sn);
        if (Objects.isNull(deviceRpcVo)) {
            //如果多码关系缺失，校验是否存在已同步过来的质保数据
            DeviceInfo deviceInfoBySn = findDeviceInfoBySn(sn);
            if (deviceInfoBySn != null) {
                removeById(deviceInfoBySn.getId());
                return;
            }
            throw ExceptionMessageUtil.getException(AppErrorCode.APP_DEVICE_NOT_EXIST, sn);
        }
        String deviceId = deviceRpcVo.getDeviceId();
        //判断该设备是否注册过
        if (!ifDeviceAlreadyRegistered(deviceId)) {
            //用户补充多码关系，绑定设备未注册时，CRM删除质保数据
            DeviceInfo deviceInfoBySn = findDeviceInfoBySn(sn);
            if (deviceInfoBySn != null) {
                removeById(deviceInfoBySn.getId());
                return;
            }
            throw ExceptionMessageUtil.getException(AppErrorCode.APP_DEVICE_INFO_NOT_REGISTERED, deviceId);
        }
        //删除质保表
        removeByDeviceId(deviceId);
        //删除质保关联收据表
        deviceInfoReceiptService.removeByDeviceId(deviceId);
        //删除质保关联问卷表
        deviceWarrantyQuestionnaireService.removeByDeviceId(deviceId);
        //删除缓存
        deviceInfoSyncTempMapper.delete(new LambdaQueryWrapper<DeviceInfoSyncTemp>().eq(DeviceInfoSyncTemp::getSn, sn));
        //修正设备表SN
        revisedDeviceSn(sn, deviceId);
    }

    private void revisedDeviceSn(String sn, String deviceId) {
        //判断设备表SN是否需要修正
        DeviceCodeRpcVo deviceCodeRpcVo = remoteDeviceCodeService.deviceCodeDetail(deviceId);
        if (Objects.nonNull(deviceCodeRpcVo)) {
            if (!Objects.equals(sn, deviceCodeRpcVo.getSn())) {
                remoteDeviceManageService.updateSnByDeviceId(deviceId, deviceCodeRpcVo.getSn());
            }
        } else {
            remoteDeviceManageService.updateSnByDeviceId(deviceId, null);
        }
    }

    @Override
    public void removeByDeviceId(String deviceId) {
        remove(Wrappers.<DeviceInfo>lambdaQuery().eq(DeviceInfo::getDeviceId, deviceId));
    }

    @Override
    public void validateSn(String sn) {
        checkAndConvertSn(sn);
        String deviceId = null;
        DeviceCodeRpcVo deviceCodeRpcVo = remoteDeviceCodeService.deviceCodeDetailBySn(sn);
        if(deviceCodeRpcVo==null || StringUtils.isEmpty(deviceCodeRpcVo.getDeviceId())) {
            DeviceRpcVo deviceBySn = remoteDeviceManageService.deviceDetailBySn(sn);
            if(deviceBySn != null) {
                deviceId = deviceBySn.getDeviceId();
            }
        } else {
            deviceId = deviceCodeRpcVo.getDeviceId();
        }
        if(StringUtils.isEmpty(deviceId)) {
            return;
        }
        if(deviceIsRegistered(deviceId)) {
            throw ExceptionMessageUtil.getException(AppErrorCode.APP_DEVICE_INFO_ALREADY_REGISTERED, sn);
        }
    }

    @Override
    public void syncDeviceFromCrm(List<SfWarrantyRecord> sfWarrantyRecords) {
        if (CollectionUtils.isEmpty(sfWarrantyRecords)) {
            return;
        }
        Map<String, DeviceInfo> addDeviceInfoMap = Maps.newHashMap();
        for (SfWarrantyRecord sfWarrantyRecord : sfWarrantyRecords) {
            if (StringUtils.isEmpty(sfWarrantyRecord.getSn())) {
                continue;
            }
            try {
                //SN格式不正确时记录异常数据
                String convertSn = checkAndConvertSnNoThrow(sfWarrantyRecord.getSn());
                if (StringUtils.isEmpty(convertSn)) {
                    deviceInfoSyncFailService.saveSyncFailInfo(sfWarrantyRecord.getSn(), sfWarrantyRecord.getSfUserId(),
                            "SN format verification failed", DeviceInfoSyncDealStatusEnum.DEAL_IGNORE.getValue());
                    continue;
                }
                sfWarrantyRecord.setSn(convertSn);

                //质保记录，sfUser不存在，同步查询已加上非空条件，一般不会存在
                if (StringUtils.isEmpty(sfWarrantyRecord.getSfUserId())) {
                    deviceInfoSyncFailService.saveSyncFailInfo(convertSn, null,
                            "CRM userId is null", DeviceInfoSyncDealStatusEnum.DEAL_IGNORE.getValue());
                    continue;
                }
                //购买日期格式异常时，记录异常表
                LocalDateTime purchaseTime = parsePurchase(sfWarrantyRecord.getPurchaseDate());
                if (purchaseTime!=null&& this.checkDateTimestamp(purchaseTime)) {
                    deviceInfoSyncFailService.saveSyncFailInfo(convertSn, sfWarrantyRecord.getSfUserId(),
                            "purchase time is error, year:" + purchaseTime.getYear(),
                            DeviceInfoSyncDealStatusEnum.DEAL_IGNORE.getValue());
                    continue;
                }
                this.dealWarrantyFromCrm(sfWarrantyRecord, addDeviceInfoMap,purchaseTime);
            } catch (Exception e) {
                log.warn("syncDeviceFromCrm, sn:{}, user:{}, error: ",
                        sfWarrantyRecord.getSn(), sfWarrantyRecord.getSfUserId(), e);
                deviceInfoSyncFailService.saveSyncFailInfo(sfWarrantyRecord.getSn(),
                        sfWarrantyRecord.getSfUserId(),
                        e.getMessage(), DeviceInfoSyncDealStatusEnum.NOT_DEAL.getValue());
            }
        }

        this.saveBatch(addDeviceInfoMap.values());
    }

    /**
     * 格式化购买时间
     * @param purchaseDate
     * @return
     */
    private LocalDateTime parsePurchase(String purchaseDate){
        if (StringUtils.isNotEmpty(purchaseDate)) {
            LocalDate date = LocalDate.parse(purchaseDate, dateFormatter);
            return date.atTime(0, 0, 0);
        }
        return null;
    }
    /**
     * 处理CRM的质保记录
     * @param sfWarrantyRecord
     * @param addDeviceInfoMap
     */
    public void dealWarrantyFromCrm(SfWarrantyRecord sfWarrantyRecord,
                                    Map<String, DeviceInfo> addDeviceInfoMap,
                                    LocalDateTime purchaseTime) {
        String sn = sfWarrantyRecord.getSn();
        //IOT不存在UserId也不进行阻断
        Long userId = userQueryService.getUserIdBySfUserId(sfWarrantyRecord.getSfUserId());
        if (userId == null) {
            deviceInfoSyncFailService.saveSyncFailInfo(sn, sfWarrantyRecord.getSfUserId(),
                    "IoT userId is null", DeviceInfoSyncDealStatusEnum.DEAL_USER_FIRST.getValue());
        }
        boolean existsTemp = existsSyncTemp(sn, sfWarrantyRecord.getSfUserId());
        //根据SN码查询多码表
        String deviceId = findDeviceIdBySn(sn);
        if (!existsTemp) {
            boolean isIotDevice = isIoTDevice(sn);
            String failMsg = "deviceId is null";
            //deviceId存在，或者不是IOT设备
            if (StringUtils.isNotEmpty(deviceId) || !isIotDevice) {
                try {
                    deviceId = bindSnOfCrm(sn, userId, isIotDevice, deviceId);
                } catch (Exception e) {
                    failMsg = e.getMessage();
                    log.warn("User ID is:{} and sn is:{} ,Exception: ", userId, sn, e);
                }
                if (StringUtils.isEmpty(deviceId)) {
                    deviceInfoSyncFailService.saveSyncFailInfo(sn, sfWarrantyRecord.getSfUserId(),
                            failMsg, DeviceInfoSyncDealStatusEnum.NOT_DEAL.getValue());
                    return;
                }
            }else {
                //多码关系不存在的IOT设备，需要保存质保数据,和异常记录
                deviceInfoSyncFailService.saveSyncFailInfo(sn, sfWarrantyRecord.getSfUserId(),
                        failMsg, DeviceInfoSyncDealStatusEnum.NOT_DEAL.getValue());
            }

        }
        //保存质保信息
        this.saveDeviceInfoOfCrm(sfWarrantyRecord,deviceId,addDeviceInfoMap,purchaseTime);
        //同步缓存不存在，且IOT 存在该用户，deviceId也存在时时保存缓存数据
        if (!existsTemp && userId != null && StringUtils.isNotEmpty(deviceId)) {
            this.saveSyncTempInfo(sfWarrantyRecord);
        }

    }

    /**
     * CRM质保数据保存
     * @param sfWarrantyRecord
     * @param deviceId
     * @param addDeviceInfoMap
     * @param purchaseTime
     */
    private void saveDeviceInfoOfCrm(SfWarrantyRecord sfWarrantyRecord,
                                     String deviceId,
                                     Map<String, DeviceInfo> addDeviceInfoMap,
                                     LocalDateTime purchaseTime){
        DeviceInfo deviceInfo = new DeviceInfo();
        deviceInfo.setLastSyncTime(LocalDateTime.now());
        deviceInfo.setPurchaseTime(purchaseTime);
        //设置设备购买地点
        assembleDeviceInfoPurchasePlace(sfWarrantyRecord, deviceInfo);
        //设置设备票据信息
        assembleDeviceInfoReceipt(sfWarrantyRecord, deviceInfo);
        //设置设备用途
        assembleDeviceInfoApply(sfWarrantyRecord, deviceInfo);
        deviceInfo.setSfId(sfWarrantyRecord.getId());
        deviceInfo.setSn(sfWarrantyRecord.getSn());
        DeviceInfo deviceInfoInDb = null;
        //如果多码关系不存在，则根据sn去查询质保数据
        if (StringUtils.isEmpty(deviceId)) {
            deviceInfoInDb = this.getOne(new LambdaQueryWrapper<DeviceInfo>()
                    .select(DeviceInfo::getId, DeviceInfo::getSfId,DeviceInfo::getPurchaseTime)
                    .eq(DeviceInfo::getSn, sfWarrantyRecord.getSn()).last(LIMIT_ONE_STR));
        } else {
            deviceInfoInDb = this.getOne(new LambdaQueryWrapper<DeviceInfo>()
                    .select(DeviceInfo::getId, DeviceInfo::getSfId,DeviceInfo::getPurchaseTime)
                    .eq(DeviceInfo::getDeviceId, deviceId).last(LIMIT_ONE_STR));
        }

        //已经注册过质保
        if (deviceInfoInDb != null) {
            if (StringUtils.isEmpty(deviceInfoInDb.getSfId())
                    || deviceInfoInDb.getSfId().equals(deviceInfo.getSfId())) {
                LocalDateTime dbPurchaseTime = deviceInfoInDb.getPurchaseTime();
                if (dbPurchaseTime != null && purchaseTime != null) {
                    deviceInfo.setPurchaseTime(purchaseTime.plusHours(dbPurchaseTime.getHour()));
                }
                this.update(deviceInfo,
                        new LambdaQueryWrapper<DeviceInfo>()
                                .eq(DeviceInfo::getId, deviceInfoInDb.getId()));
            } else {
                // 不更新，记录日志
                log.info("warranty registered, deviceId:{}, warranty info:{}",
                        deviceId, JSON.toJSONString(sfWarrantyRecord));
            }
        }else {
            //如果添加列表没有这台设备，则进行新增，使用map为了去重
            deviceInfo.setDeviceId(deviceId);
            deviceInfo.setSourceCode(CommonConstant.ONE);
            addDeviceInfoMap.putIfAbsent(deviceId, deviceInfo);
        }
    }

    /**
     * 判断时间
     * @param date
     * @return
     */
    private boolean checkDateTimestamp(LocalDateTime date){
        return date.getYear() >= IotAppCommonConstant.SQL_YEAR_LIMIT || date.getYear() < MIN_PURCHASE_YEAR;
    }
    /**
     * 判断是否为IoT设备
     *
     * @param sn 设备SN码
     */
    private boolean isIoTDevice(String sn) {
        String snCodeFromSn = getSnCodeFromSn(sn);
        ProductRpcVo productRpcVo = remoteProductService.getProductIdBySnCode(snCodeFromSn);
        if (productRpcVo != null) {
            return ProductTypeEnum.GATEWAY_DEVICE.getValue().equals(productRpcVo.getProductType()) ||
                    ProductTypeEnum.GATEWAY_SUB_DEVICE.getValue().equals(productRpcVo.getProductType()) ||
                    ProductTypeEnum.DIRECT_CONNECTED_DEVICE.getValue().equals(productRpcVo.getProductType());
        }
        return false;
    }

    /**
     * 绑定设备
     *
     * @param sn     设备SN码
     * @param userId 用户ID
     * @return deviceId
     */
    private String bindSnOfCrm(String sn, Long userId,boolean isIotDevice,String deviceId) {
        //IoT且多码缺失，则按异常处理
        if (StringUtils.isEmpty(deviceId) && isIotDevice) {
            return null;
        }
        AppUserDevice appUserDevice = new AppUserDevice();
        DeviceBindBo deviceBindBo = null;
        if (isIotDevice) {
            //IoT设备根据deviceId绑定
            //绑定方法中，若对应的product不存在会报错，若设备已停用则返回3，若被其它APP绑定过则返回1
            deviceBindBo = remoteDeviceManageService.bindByDeviceId(deviceId, 1);
        } else {
            //非IoT设备根据SN码绑定
            deviceBindBo = remoteDeviceManageService.bindBySn(sn, 1);
        }

        if (deviceBindBo != null && deviceBindBo.getResult() == 0 && userId != null) {
            deviceId = deviceBindBo.getDeviceId();
            appUserDevice.setUserId(userId);
            appUserDevice.setDeviceId(deviceId);
            appUserDevice.setSourceCode(CommonConstant.ONE);
            Object savepoint = null;
            boolean transactionActive = TransactionSynchronizationManager.isActualTransactionActive();
            if(transactionActive) {
                savepoint = TransactionAspectSupport.currentTransactionStatus().createSavepoint();
            }
            try {
                appDeviceService.bindFromSf(appUserDevice, deviceBindBo, isIotDevice);
            } catch (Exception e) {
                log.error("Exception when bind device(deviceId:{}) and user({}), ", deviceId, userId, e);
                if(savepoint != null) {
                    TransactionAspectSupport.currentTransactionStatus().rollbackToSavepoint(savepoint);
                }
                throw e;
            }
        }
        return deviceId;
    }

    private String bindSnOfDeviceInfo(String sn, Long userId) {
        String deviceId = findDeviceIdBySn(sn);
        //根据SN码查询多码表
        boolean isIotDevice = isIoTDevice(sn);

        //IoT且多码缺失，则按异常处理
        if (StringUtils.isEmpty(deviceId) && isIotDevice) {
            return null;
        }
        try {
            deviceId = bindSnOfCrm(sn, userId, isIotDevice, deviceId);
        }catch (AppException appException){
            //如果不是用户已绑定错误则继续抛出异常，对于kit注册已绑定设备的可以继续注册质保
            if(!AppErrorCode.APP_USER_DEVICE_ALREADY_BOUND.getCode().equals(appException.getCode())){
                throw appException;
            }
        }
        return deviceId;
    }

    /**
     * 设置设备购买地点
     */
    private void assembleDeviceInfoPurchasePlace(SfWarrantyRecord sfWarrantyRecord, DeviceInfo deviceInfo) {
        if (StringUtils.isEmpty(sfWarrantyRecord.getPurchasePlace())) {
            deviceInfo.setPurchasePlace(IotAppCommonConstant.OTHER);
            return;
        }
        String purchasePlace0 = sfWarrantyRecord.getPurchasePlace();
        String purchasePlace = purchasePlace0.equals(PurchasePlaceEnum.ACE_HARDWARE.getLabel()) ?
                PurchasePlaceEnum.ACE_HARDWARE.getValue().toLowerCase() : purchasePlace0.toLowerCase();
        List<DictVo> dictList = dictService.listByDictName(LocaleContextHolder.getLocale().getLanguage(),
                Collections.singletonList(IotAppCommonConstant.PURCHASE_PLACE));
        Map<String, String> map = dictList.stream().flatMap(dictVo -> dictVo.getNodes().stream())
                .collect(Collectors.toMap(DictNode::getDescription, DictNode::getLabel));
        Map<String, String> map2 = map.entrySet().stream()
                .collect(Collectors.toMap(
                        // 将 key 转为小写
                        entry -> entry.getKey().toLowerCase().replace("'", ""),
                        Map.Entry::getValue
                ));
        if (map2.containsKey(purchasePlace)) {
            deviceInfo.setPurchasePlace(map2.get(purchasePlace));
            deviceInfo.setPurchasePlaceOther(sfWarrantyRecord.getPurchasePlaceOther());
            return;
        }

        deviceInfo.setPurchasePlace(IotAppCommonConstant.OTHER);
        deviceInfo.setPurchasePlaceOther(sfWarrantyRecord.getPurchasePlaceOther());
    }

    /**
     * 设置设备票据信息
     */
    private void assembleDeviceInfoReceipt(SfWarrantyRecord sfWarrantyRecord, DeviceInfo deviceInfo) {
        if (StringUtils.isNotEmpty(sfWarrantyRecord.getReceiptUrl())) {
            deviceInfo.setReceiptInformation(ReceiptInformationStatusEnum.HAVE_RECEIPT.getValue());
            return;
        }

        if (StringUtils.isEmpty(sfWarrantyRecord.getReceiptStatus())) {
            deviceInfo.setReceiptInformation(ReceiptInformationStatusEnum.LOST_RECEIPT.getValue());
            return;
        }
        String receiptStatus = sfWarrantyRecord.getReceiptStatus().toUpperCase();
        if (receiptStatus.contains(ReceiptInformationStatusEnum.GIFT.name())) {
            deviceInfo.setReceiptInformation(ReceiptInformationStatusEnum.GIFT.getValue());
            return;
        }
        deviceInfo.setReceiptInformation(ReceiptInformationStatusEnum.LOST_RECEIPT.getValue());
    }

    /**
     * 设置设备用途
     */
    private void assembleDeviceInfoApply(SfWarrantyRecord sfWarrantyRecord, DeviceInfo deviceInfo) {
        if (StringUtils.isEmpty(sfWarrantyRecord.getUseType()) ||
                sfWarrantyRecord.getUseType().toLowerCase().contains(ApplyWithEnum.RESIDENTIAL.name().toLowerCase())) {
            deviceInfo.setApplyWith(ApplyWithEnum.RESIDENTIAL.getValue());
        } else {
            deviceInfo.setApplyWith(ApplyWithEnum.INDUSTRIAL_PROFESSIONAL_COMMERCIAL.getValue());
        }
    }


    /**
     * 保存质保同步缓存(来自SF)
     */
    private void saveSyncTempInfo(SfWarrantyRecord sfWarrantyRecord) {
        DeviceInfoSyncTemp entity = new DeviceInfoSyncTemp();
        entity.setSn(sfWarrantyRecord.getSn());
        entity.setSfUserId(sfWarrantyRecord.getSfUserId());
        entity.setWarrantyId(sfWarrantyRecord.getId());
        entity.setLastModifiedDate(sfWarrantyRecord.getLastModifiedDate());
        try {
            deviceInfoSyncTempMapper.insert(entity);
        } catch (Exception e) {
            log.warn("saveSyncTempInfo error, sn:{}, sfUserId:{}, ", entity.getSn(), entity.getSfUserId(), e);
        }
    }

    /**
     * 保存质保同步缓存(来自APP)
     * @param sfSn         sn
     * @param kitSnList    kitSn
     */
    private void saveSyncTemp(String sfSn, List<String> kitSnList) {
        UserVo userInfo = userQueryService.getUserInfo(StpUtil.getLoginIdAsLong());
        if(userInfo == null || StringUtils.isEmpty(userInfo.getSfUserId())) {
            return;
        }

        Set<String> snSet = Sets.newHashSet();
        snSet.add(sfSn);
        if(!CollectionUtils.isEmpty(kitSnList)) {
            snSet.addAll(kitSnList);
        }
        for(String sn : snSet) {
            if(existsSyncTemp(sn, userInfo.getSfUserId())) {
                continue;
            }
            DeviceInfoSyncTemp entity = new DeviceInfoSyncTemp();
            entity.setSn(sn);
            entity.setSfUserId(userInfo.getSfUserId());
            entity.setWarrantyId("");//避免对北美的影响，该字段数据库设置为非空
            try {
                deviceInfoSyncTempMapper.insert(entity);
            } catch (Exception e) {
                log.warn("saveSyncTempInfo error, sn:{}, sfUserId:{}, ", entity.getSn(), entity.getSfUserId(), e);
            }
        }
    }


    @Override
    public boolean existsSyncTemp(String sn, String sfUserId) {
        return deviceInfoSyncTempMapper.exists(new LambdaQueryWrapper<DeviceInfoSyncTemp>()
                .eq(DeviceInfoSyncTemp::getSn, sn)
                .eq(DeviceInfoSyncTemp::getSfUserId, sfUserId));
    }

    /**
     * 根据SN查询多码关系中的deviceId
     * @param sn
     * @return
     */
    private String findDeviceIdBySn(String sn){
        String deviceId = null;
        DeviceCodeRpcVo deviceCodeRpcVo = remoteDeviceCodeService.deviceCodeDetailBySn(sn);
        if (deviceCodeRpcVo != null) {
            deviceId = deviceCodeRpcVo.getDeviceId();
        }else {
            DeviceRpcVo deviceRpcVo = remoteDeviceManageService.deviceDetailBySn(sn);
            if (deviceRpcVo!= null) {
                deviceId = deviceRpcVo.getDeviceId();
            }
        }
        return deviceId;
    }

    /**
     * 查询多码缺失保存的质保数据
     * @param sn
     * @return
     */
    private DeviceInfo findDeviceInfoBySn(String sn ){
        //如果质保未注册，且传了SN参数
        LambdaQueryWrapper<DeviceInfo> deviceInfoWrapper = new LambdaQueryWrapper<DeviceInfo>()
                .eq(DeviceInfo::getSn, sn).isNull(DeviceInfo::getDeviceId)
                .last(LIMIT_ONE_STR);
        return baseMapper.selectOne(deviceInfoWrapper);
    }
}
