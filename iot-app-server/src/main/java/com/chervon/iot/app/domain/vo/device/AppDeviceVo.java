package com.chervon.iot.app.domain.vo.device;

import com.chervon.common.core.config.LocalDateTimeSerializerConfig;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2022-07-13 17:12
 **/
@Data
public class AppDeviceVo implements Serializable {

    private static final long serialVersionUID = 4628122394970306007L;
    /**
     * 设备所属产品ID
     */
    @ApiModelProperty("设备所属产品ID")
    private Long productId;
    /**
     * 设备出厂时烧录的ID，唯一标识：设备类型+mes码
     */
    @ApiModelProperty("设备出厂时烧录的ID，唯一标识：设备类型+mes码")
    private String deviceId;
    /**
     * 设备SN
     */
    @ApiModelProperty("设备SN")
    private String sn;
    /**
     * 设备名称
     */
    @ApiModelProperty("设备名称")
    private String deviceName;
    /**
     * 设备昵称
     */
    @ApiModelProperty("设备昵称")
    private String nickName;
    /**
     * 设备版本号
     */
    @ApiModelProperty("设备版本号")
    private String version;
    /**
     * 是否在线 1：在线 0：离线
     */
    @ApiModelProperty("是否在线 1：在线 0：离线")
    private Integer isOnline;
    /**
     * 设备状态 0：禁用 1：启用
     */
    @ApiModelProperty("设备状态 0：禁用 1：启用")
    private Integer status;

    @ApiModelProperty("设备快捷开关配置")
    private List<ShortCutVo> shortCutVos;
    /**
     * 设备多码状态 0：废弃 1：启用
     */
    @ApiModelProperty("设备多码状态 0：废弃 1：启用")
    private Integer deviceCodeStatus;
    /**
     * 设备图片地址在S3中存储Key
     */
    @ApiModelProperty("设备图片地址在S3中存储Key")
    private String deviceIcon;
    /**
     * 设备类型，wifiAndBle：Wifi+BLE，4GAndBle：4G+BLE，ble：BLE，DOrT：D/T，wifi：WIFI，4G：4G，lan：LAN，notNetworked：不联网
     */
    @ApiModelProperty("通讯方式:wifi > 4G > BLE > DT> LAN >noNetworked")
    private String communicateMode;
    /**
     * 设备类型，directConnectedDevice：直连设备，gatewayDevice：网关设备，gatewaySubDevice：网关子设备，notIotDevice：非Iot设备，oldIotDevice：老iot设备
     */
    @ApiModelProperty("设备类型，directConnectedDevice：直连设备，gatewayDevice：网关设备，gatewaySubDevice：网关子设备，notIotDevice：非Iot设备，oldIotDevice：老iot设备")
    private String productType;
    /**
     * 设备MAC地址
     */
    @ApiModelProperty("设备MAC地址")
    private String mac;

    /**
     * 设备排序编号
     */
    @ApiModelProperty("设备排序编号")
    private Integer sort;
    /**
     * 被绑定用户Id列表
     */
    @ApiModelProperty("被绑定用户Id列表")
    private List<Long> userIdList;
    /**
     * 绑定时间
     */
    @ApiModelProperty("绑定时间")
    @JsonSerialize(using = LocalDateTimeSerializerConfig.LocalDateTimeSerializer.class)
    private LocalDateTime bindTime;
    /**
     * 设备信息注册状态:0未注册 1已注册
     */
    @ApiModelProperty("设备信息注册状态:0未注册 1已注册")
    private Integer infoStatus;

    @ApiModelProperty("rnBundleName")
    private String rnBundleName;
    /**
     * 总成零件序列号列表
     */
    @ApiModelProperty("总成零件序列号列表")
    private List<String> assemblySnList;

    @ApiModelProperty(value = "商品型号")
    private String commodityModel;

    @ApiModelProperty(value = "设备信息有效时长，单位秒")
    private Long period;
    @ApiModelProperty(value = "配件个数")
    private Long accessoryQuantity;

    @ApiModelProperty(value = "离线天数")
    private Long offlineDays;

    @ApiModelProperty(value = "产品名称")
    private String productName;

    @ApiModelProperty(value = "问卷模板：commonTemplate，extendedWarrantyTemplate")
    private String questionTemplate;

    @ApiModelProperty("是否可被分享，0:老设备不可分享，1:主用户可分享，2:子用户不可再分享")
    private Integer shareType;

    @ApiModelProperty("产品型号")
    private String model;

    @ApiModelProperty("所属品类ID")
    private String categoryId;

    @ApiModelProperty("品类名称")
    private String categoryName;
}
