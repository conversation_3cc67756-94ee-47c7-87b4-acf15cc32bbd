package com.chervon.iot.app.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.chervon.iot.app.domain.dataobject.AppUserDevice;
import com.chervon.iot.app.domain.dto.device.*;
import com.chervon.iot.app.domain.vo.device.AddUserDeviceBindVo;
import com.chervon.iot.app.domain.vo.device.AppDeviceStatusVo;
import com.chervon.iot.app.domain.vo.device.AppDeviceVo;
import com.chervon.technology.api.dto.DeviceEditDto;
import com.chervon.technology.api.vo.DeviceBindBo;
import com.chervon.technology.api.vo.DeviceRpcVo;
import com.chervon.usercenter.api.vo.UserVo;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2022-07-13 16:56
 **/
public interface AppDeviceService extends IService<AppUserDevice> {
    /**
     * 通过设备ID绑定设备
     *
     * @param appUserDeviceIdDto 设备Id
     * @return 绑定成功后的一些判定
     */
    AddUserDeviceBindVo bindByDeviceId(AppUserDeviceIdDto appUserDeviceIdDto);

    /**
     * 通过设备Sn绑定设备
     *
     * @param appUserDeviceSnDto 设备SN
     * @return 绑定成功后的一些判定
     */
    AddUserDeviceBindVo bindBySn(AppUserDeviceSnDto appUserDeviceSnDto);

    /**
     * 来自SF平台的设备用户绑定
     *
     * @param appUserDevice  设备用户信息
     * @param deviceBindBo   设备绑定信息
     * @param isIotDevice    是否为IoT设备
     */
    void bindFromSf(AppUserDevice appUserDevice, DeviceBindBo deviceBindBo, boolean isIotDevice);

    /**
     * 判断设备是否已配对(不需要配对的当作已配对)
     *
     * @param appDeviceVo  设备相关信息
     * @return true已配对，false未配对
     */
    boolean isPaired(AppDeviceVo appDeviceVo);

    /**
     * 获取设备列表
     * @param lang        国家语言
     * @param appVersion  app版本
     * @return 设备列表
     */
    List<AppDeviceVo> listBoundDevices(String lang, String appVersion);

    /**
     * 获取用户绑定设备列表
     * 简化接口 只返回部分数据
     *
     * @return 设备列表
     */
    List<DeviceRpcVo> listBoundDevices();
    /**
     * 获取用户绑定设备列表
     * 针对不是当前登陆用户 数据查询
     *
     * @return 设备列表
     */
    List<DeviceRpcVo> listBoundDevices(UserVo userVo);

    /**
     * 通过ID获取设备详情
     *
     * @param deviceId 设备Id
     * @return 设备Vo
     */
    AppDeviceVo detail(String deviceId, HttpServletRequest request);

    /**
     * 通过SN获取设备详情
     *
     * @param sn 设备SN
     * @return 设备Vo
     */
    AppDeviceVo detailBySn(String sn, HttpServletRequest request);

    /**
     * 解绑用户与设备
     *
     * @param appUserDeviceUnbindDto 设备Id或SN
     */
    void unbind(AppUserDeviceUnbindDto appUserDeviceUnbindDto);

    /**
     * 解绑用户与设备
     *
     * @param deviceId   设备Id(非数据库ID)
     * @param userId     用户数据库ID
     * @param clearShare 是否删除设备分享
     */
    void unbind(String deviceId, Long userId, boolean clearShare);


    /**
     * 用户绑定设备排序
     *
     * @param deviceSortDto 设备排序Dto
     * @param appVersion    APP版本
     */
    void sort(DeviceSortDto deviceSortDto, String appVersion);

    /**
     * 编辑设备
     *
     * @param deviceEditDto 编辑设备Dto
     */
    void edit(DeviceEditDto deviceEditDto);

    /**
     * 删除用户和设备的关联信息
     *
     * @param userId 用户Id
     */
    void deleteUserDevice(Long userId);

    /**
     * 获取设备状态+设备多码状态
     * 在点击设备卡片的时候调用，如果设备正常且设备多码正常，则能进入控制面板
     *
     * @param appDeviceStatusDto 设备Id
     * @return 设备状态+设备多码状态
     */
    AppDeviceStatusVo getStatus(AppDeviceStatusDto appDeviceStatusDto);

    /**
     * 校验SN的合法性之后获取产品ID
     *
     * @param sn 设备SN
     * @return 产品ID
     */
    Long getProductIdBySn(String sn);

    /**
     * 绑定的调试设备列表
     *
     * @return 设备列表
     */
    List<AppDeviceVo> listBoundDebugDevices();

    /**
     * 根据deviceId查询主账户UserId
     * @param deviceId
     * @return
     */
    AppUserDevice masterUserByDeviceId(String deviceId);
}

