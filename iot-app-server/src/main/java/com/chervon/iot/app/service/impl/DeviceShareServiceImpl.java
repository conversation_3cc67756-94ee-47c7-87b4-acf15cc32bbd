package com.chervon.iot.app.service.impl;

import cn.dev33.satoken.stp.StpUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.chervon.common.core.constant.CommonConstant;
import com.chervon.common.core.prop.AwsProperties;
import com.chervon.common.core.utils.StringUtils;
import com.chervon.common.i18n.util.MessageTools;
import com.chervon.common.oss.uitl.UrlUtil;
import com.chervon.common.redis.utils.RedisUtils;
import com.chervon.configuration.api.service.RemoteMultiLanguageService;
import com.chervon.iot.app.api.enums.AppUserSourceCodeEnum;
import com.chervon.iot.app.api.exception.AppErrorCode;
import com.chervon.iot.app.config.ExceptionMessageUtil;
import com.chervon.iot.app.domain.dataobject.AppUserDevice;
import com.chervon.iot.app.domain.dataobject.DeviceShare;
import com.chervon.iot.app.domain.dataobject.UserSetting;
import com.chervon.iot.app.domain.dto.device.AppUserDeviceIdDto;
import com.chervon.iot.app.domain.dto.share.AppDeviceAcceptDto;
import com.chervon.iot.app.domain.dto.share.AppDeviceShareDto;
import com.chervon.iot.app.domain.dto.share.DeviceShareAddDto;
import com.chervon.iot.app.domain.dto.share.DeviceShareSubDto;
import com.chervon.iot.app.domain.enums.ShareOperateEnum;
import com.chervon.iot.app.domain.enums.ShareStatusEnum;
import com.chervon.iot.app.domain.enums.ShareTypeEnum;
import com.chervon.iot.app.domain.enums.StaticMultiLanguageEnum;
import com.chervon.iot.app.domain.vo.dict.DictNode;
import com.chervon.iot.app.domain.vo.dict.DictVo;
import com.chervon.iot.app.mapper.DeviceShareMapper;
import com.chervon.iot.app.service.AppDeviceService;
import com.chervon.iot.app.service.AppUserDeviceService;
import com.chervon.iot.app.service.DeviceShareService;
import com.chervon.iot.app.service.DictService;
import com.chervon.iot.app.service.UserSettingService;
import com.chervon.iot.app.util.EmailUtil;
import com.chervon.message.api.RemoteMessageService;
import com.chervon.message.api.dto.MessageDto;
import com.chervon.message.api.enums.MessageTypeEnum;
import com.chervon.message.api.enums.OsType;
import com.chervon.message.api.enums.PushTypeHandlerEnum;
import com.chervon.technology.api.RemoteDeviceManageService;
import com.chervon.technology.api.RemoteProductService;
import com.chervon.technology.api.enums.SwitchFlagEnum;
import com.chervon.technology.api.vo.DeviceRpcVo;
import com.chervon.technology.api.vo.ProductRpcVo;
import com.chervon.usercenter.api.dto.InviteRegisterDto;
import com.chervon.usercenter.api.service.EmailService;
import com.chervon.usercenter.api.service.RemoteAppUserService;
import com.chervon.usercenter.api.service.UserQueryService;
import com.chervon.usercenter.api.vo.AppUserVo;
import com.chervon.usercenter.api.vo.UserVo;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 设备分享服务
 *
 * <AUTHOR>
 * @date 2024/8/1
 **/
@Service
@Slf4j
public class DeviceShareServiceImpl extends ServiceImpl<DeviceShareMapper, DeviceShare> implements DeviceShareService {
    @Resource
    private AwsProperties awsProperties;
    @Resource
    private AppDeviceService appDeviceService;
    @Resource
    private AppUserDeviceService appUserDeviceService;
    @Resource
    private UserSettingService userSettingService;
    @Resource
    private ScheduledExecutorService scheduledExecutorService;
    @Resource
    private DictService dictService;
    @Resource
    private MessageTools messageTools;

    @DubboReference
    private RemoteDeviceManageService remoteDeviceManageService;
    @DubboReference
    private UserQueryService userQueryService;
    @DubboReference
    private RemoteAppUserService remoteAppUserService;
    @DubboReference
    private RemoteProductService remoteProductService;
    @DubboReference
    private RemoteMessageService messageService;
    @DubboReference
    private RemoteMultiLanguageService multiLanguageService;
    @DubboReference
    private EmailService emailService;


    @Value("${device.share.expire}")
    private Integer expiredDays;
    @Value("${device.share.lessVersion}")
    private String lessVersion;
    @Value("${device.share.upgradeUrl}")
    private String upgradeUrl;

    private static final String DEVICE_ID = "device_id";
    private static final String DEVICE_CACHE_REDIS_KEY = "deviceShare:device:cache";
    private static final String SHARE_PRODUCT_CACHE_REDIS_KEY = "deviceShare:product:cache";

    @Override
    public void share(DeviceShareAddDto shareAddDto) {
        EmailUtil.check(shareAddDto.getEmail());
        //检查子用户邮箱是否已注册
        Long subUserId = userQueryService.getUserIdByEmail(shareAddDto.getEmail());
        if (subUserId == null) {
            throw ExceptionMessageUtil.getException(AppErrorCode.APP_SHARE_EMAIL_ERROR, shareAddDto.getEmail());
        }
        addShare(shareAddDto, subUserId);
    }

    @Override
    public void shareAndInvite(DeviceShareAddDto shareAddDto) {
        EmailUtil.check(shareAddDto.getEmail());
        addShare(shareAddDto, null);
    }

    /**
     * 新增分享
     */
    private void addShare(DeviceShareAddDto shareAddDto, Long subUserId) {
        String deviceId = shareAddDto.getDeviceId();
        String email = shareAddDto.getEmail().trim();
        ProductRpcVo productRpcVo = checkProduct(deviceId);
        long userId = StpUtil.getLoginIdAsLong();
        checkIsMaster(deviceId, userId);
        checkAcceptedShare(deviceId, email);

        DeviceShare deviceShare = new DeviceShare();
        DeviceShare expiredOne = checkExpiredShare(deviceId, email, userId);
        if (expiredOne != null) {
            //已过期的分享，可以更新为待接受的分享
            deviceShare.setId(expiredOne.getId());
            deviceShare.setCreateTime(LocalDateTime.now());
        }
        deviceShare.setDeviceId(deviceId);
        deviceShare.setProductId(productRpcVo.getId());
        deviceShare.setMasterId(userId);
        UserVo masterUser = RedisUtils.getCacheObject(StpUtil.getTokenValue());
        deviceShare.setMasterEmail(masterUser.getEmail());
        deviceShare.setSubId(subUserId);
        deviceShare.setSubEmail(email);
        deviceShare.setStatus(ShareStatusEnum.PENDING.getValue());
        LocalDateTime expiredDate = LocalDateTime.now().plusDays(expiredDays)
                .withHour(0).withMinute(0).withSecond(0).minusSeconds(1);
        deviceShare.setExpiredDate(expiredDate);
        saveOrUpdate(deviceShare);

        //推送消息给子用户
        if (subUserId != null) {
            pushMsg(ShareOperateEnum.MASTER_SHARE, deviceShare, productRpcVo.getCommodityModel());
        } else {
            //推送邀请邮件给子用户
            sendInvite(email, masterUser, productRpcVo.getCommodityModel());
        }
    }

    /**
     * 校验产品是否支持设备分享
     */
    private ProductRpcVo checkProduct(String deviceId) {
        ProductRpcVo productRpcVo = RedisUtils.getCacheMapValue(
                SHARE_PRODUCT_CACHE_REDIS_KEY, deviceId);
        if (productRpcVo == null) {
            productRpcVo = remoteProductService.getAppProductVoByDeviceId(deviceId);
            RedisUtils.setCacheMapValueWithExpire(SHARE_PRODUCT_CACHE_REDIS_KEY, deviceId, productRpcVo, 10);
        }
        if (productRpcVo == null || !Boolean.TRUE.equals(productRpcVo.getIsSharingSupported())) {
            throw ExceptionMessageUtil.getException(AppErrorCode.APP_SHARE_PRODUCT_ERROR);
        }
        return productRpcVo;
    }

    /**
     * 检查APP用户是否为设备主用户
     */
    private void checkIsMaster(String deviceId, Long userId) {
        if (!appUserDeviceService.checkIsMaster(deviceId, userId)) {
            throw ExceptionMessageUtil.getException(AppErrorCode.APP_SHARE_AUTH_ERROR);
        }
    }

    /**
     * 检查已接受的分享记录
     */
    private void checkAcceptedShare(String deviceId, String subEmail) {
        long existShareCount = count(new LambdaQueryWrapper<DeviceShare>()
                .eq(DeviceShare::getDeviceId, deviceId)
                .eq(DeviceShare::getSubEmail, subEmail)
                .eq(DeviceShare::getStatus, ShareStatusEnum.ACCEPTED.getValue()));
        if (existShareCount > 0) {
            throw ExceptionMessageUtil.getException(AppErrorCode.APP_SHARE_EXIST_ERROR);
        }
    }

    /**
     * 检查已过期或待接受的分享
     */
    private DeviceShare checkExpiredShare(String deviceId, String subEmail, Long masterId) {
        return getOne(new LambdaQueryWrapper<DeviceShare>()
                .select(DeviceShare::getId)
                .eq(DeviceShare::getDeviceId, deviceId)
                .eq(DeviceShare::getSubEmail, subEmail)
                .eq(DeviceShare::getMasterId, masterId)
                .in(DeviceShare::getStatus, ShareStatusEnum.EXPIRED.getValue(), ShareStatusEnum.PENDING.getValue(),
                        ShareStatusEnum.DELETE_EXPIRED.getValue())
                .last("LIMIT 1"));
    }

    /**
     * 发送邀请邮件
     */
    private void sendInvite(String subEmail, UserVo masterUser, String productName) {
        InviteRegisterDto inviteDto = new InviteRegisterDto();
        inviteDto.setLanguage(LocaleContextHolder.getLocale().getLanguage());
        inviteDto.setFirstName(masterUser.getFirstName());
        inviteDto.setLastName(masterUser.getLastName());
        inviteDto.setEmail(subEmail);
        inviteDto.setProductName(productName);
        emailService.sendInviteRegister(inviteDto);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void accept(Long shareId) {
        DeviceShare deviceShare = getById(shareId);
        if (deviceShare == null) {
            throw ExceptionMessageUtil.getException(AppErrorCode.APP_SHARE_STOPPED);
        }
        //检查分享记录是否存在，且状态为待接受
        if (deviceShare.getStatus() != ShareStatusEnum.PENDING.getValue()) {
            throw ExceptionMessageUtil.getException(AppErrorCode.APP_SHARE_STATUS_ERROR);
        }
        //校验用户操作权限
        if (!deviceShare.getSubId().equals(StpUtil.getLoginIdAsLong())) {
            throw ExceptionMessageUtil.getException(AppErrorCode.APP_SHARE_REMOVE_AUTH_ERROR);
        }
        //更新该分享记录状态为已接受
        boolean updateShareStatus = this.updateShareStatus(
                deviceShare.getId(),
                ShareStatusEnum.ACCEPTED,
                ShareStatusEnum.PENDING);
        if(!updateShareStatus){
            throw ExceptionMessageUtil.getException(AppErrorCode.APP_SHARE_STATUS_ERROR);
        }
        AppUserDevice masterUserDevice = appUserDeviceService.findMasterUserDevice(deviceShare.getDeviceId());

        //添加子用户和设备的绑定关系
        AppUserDeviceIdDto userDeviceDto = new AppUserDeviceIdDto();
        userDeviceDto.setDeviceId(deviceShare.getDeviceId());
        userDeviceDto.setMac(masterUserDevice.getMac());
        userDeviceDto.setDeviceShare(true);
        appDeviceService.bindByDeviceId(userDeviceDto);

        //推送消息给主用户
        pushMsg(ShareOperateEnum.SUB_ACCEPT, deviceShare, masterUserDevice.getDeviceNickName());
    }

    @Override
    public List<AppDeviceShareDto> shareDeviceList() {
        long userId = StpUtil.getLoginIdAsLong();
        LambdaQueryWrapper<AppUserDevice> queryWrapper = new LambdaQueryWrapper<AppUserDevice>()
                .select(AppUserDevice::getDeviceNickName,
                        AppUserDevice::getDeviceId,
                        AppUserDevice::getSourceCode,
                        AppUserDevice::getMac)
                .eq(AppUserDevice::getUserId, userId)
                .eq(AppUserDevice::getShareType, ShareTypeEnum.MASTER.getValue())
                .orderByDesc(AppUserDevice::getSort);
        List<AppUserDevice> appUserDevices = appDeviceService.list(queryWrapper);
        if (CollectionUtils.isEmpty(appUserDevices)) {
            return Collections.emptyList();
        }

        List<String> deviceIds = appUserDevices.stream().map(AppUserDevice::getDeviceId).collect(Collectors.toList());
        Map<String, DeviceRpcVo> deviceRpcVoMap = getDeviceRpcInfo(deviceIds);
        Map<String, Integer> shareNumMap = countShareNum(userId);
        return appUserDevices.stream()
                .map(appUserDevice -> {
                    AppDeviceShareDto shareDto = new AppDeviceShareDto();
                    shareDto.setDeviceId(appUserDevice.getDeviceId());
                    shareDto.setNickName(appUserDevice.getDeviceNickName());
                    DeviceRpcVo deviceRpcVo = deviceRpcVoMap.get(appUserDevice.getDeviceId());
                    if (deviceRpcVo != null) {
                        shareDto.setCommodityModel(deviceRpcVo.getCommodityModel());
                        shareDto.setDeviceIcon(deviceRpcVo.getDeviceIcon());
                        shareDto.setSn(deviceRpcVo.getSn());
                    }
                    //该设备被创建的分享次数
                    shareDto.setSharedCount(shareNumMap.getOrDefault(appUserDevice.getDeviceId(), 0));
                    return shareDto;
                }).collect(Collectors.toList());
    }

    @Override
    public List<AppDeviceAcceptDto> acceptDeviceList() {
        long userId = StpUtil.getLoginIdAsLong();
        LambdaQueryWrapper<DeviceShare> queryWrapper = new LambdaQueryWrapper<DeviceShare>()
                .select(DeviceShare::getId, DeviceShare::getDeviceId, DeviceShare::getExpiredDate,
                        DeviceShare::getStatus, DeviceShare::getMasterEmail)
                .eq(DeviceShare::getSubId, userId).in(DeviceShare::getStatus, ShareStatusEnum.PENDING.getValue(),
                        ShareStatusEnum.ACCEPTED.getValue(),
                        ShareStatusEnum.EXPIRED.getValue())
                .orderByDesc(DeviceShare::getCreateTime);
        List<DeviceShare> deviceShareList = list(queryWrapper);
        if (CollectionUtils.isEmpty(deviceShareList)) {
            return Collections.emptyList();
        }

        List<String> deviceIds = deviceShareList.stream().map(DeviceShare::getDeviceId).collect(Collectors.toList());
        Map<String, DeviceRpcVo> deviceRpcVoMap = getDeviceRpcInfo(deviceIds);
        return deviceShareList.stream().map(deviceShare -> {
            AppDeviceAcceptDto acceptDto = new AppDeviceAcceptDto();
            acceptDto.setShareId(deviceShare.getId());
            acceptDto.setDeviceId(deviceShare.getDeviceId());
            acceptDto.setStatus(deviceShare.getStatus());
            acceptDto.setMasterEmail(deviceShare.getMasterEmail());
            if (deviceShare.getStatus() == ShareStatusEnum.PENDING.getValue()) {
                //查看DateUtil是否有重复的方法
                acceptDto.setExpiredDays(computeExpiredDays(deviceShare.getExpiredDate()));
            }
            DeviceRpcVo deviceRpcVo = deviceRpcVoMap.get(deviceShare.getDeviceId());
            if (deviceRpcVo != null) {
                acceptDto.setCommodityModel(deviceRpcVo.getCommodityModel());
                acceptDto.setDeviceIcon(deviceRpcVo.getDeviceIcon());
                acceptDto.setSn(deviceRpcVo.getSn());
            }
            return acceptDto;
        }).collect(Collectors.toList());
    }

    @Override
    public List<DeviceShareSubDto> subList(String deviceId) {
        LambdaQueryWrapper<DeviceShare> queryWrapper = new LambdaQueryWrapper<DeviceShare>()
                .select(DeviceShare::getId, DeviceShare::getSubId, DeviceShare::getStatus,
                        DeviceShare::getExpiredDate, DeviceShare::getSubEmail)
                .eq(DeviceShare::getDeviceId, deviceId)
                .eq(DeviceShare::getMasterId, StpUtil.getLoginIdAsLong())
                .orderByDesc(DeviceShare::getCreateTime);
        List<DeviceShare> deviceShareList = list(queryWrapper);
        if (CollectionUtils.isEmpty(deviceShareList)) {
            return Collections.emptyList();
        }
        List<Long> subUserList = deviceShareList.stream()
                .filter(r -> r.getSubId() != null)
                .map(r -> r.getSubId()).collect(Collectors.toList());
        final Map<Long, UserVo> subUserMap =
                CollectionUtils.isEmpty(subUserList) ?
                        Collections.emptyMap() : userQueryService.listUserMap(subUserList);
        return deviceShareList.stream().map(deviceShare -> {
            DeviceShareSubDto subDto = new DeviceShareSubDto();
            subDto.setShareId(deviceShare.getId());
            subDto.setStatus(deviceShare.getStatus());
            UserVo userInfo = subUserMap.get(deviceShare.getSubId());
            DeviceShareSubDto.SubUser subUser;
            if (userInfo == null) {
                subUser = new DeviceShareSubDto.SubUser();
                subUser.setEmail(deviceShare.getSubEmail());
            } else {
                subUser = new DeviceShareSubDto.SubUser(userInfo);
            }
            subDto.setSubUser(subUser);
            if (deviceShare.getStatus() == ShareStatusEnum.PENDING.getValue()) {
                subDto.setExpiredDays(computeExpiredDays(deviceShare.getExpiredDate()));
            }
            if (deviceShare.getStatus() == ShareStatusEnum.DELETE_EXPIRED.getValue()) {
                subDto.setStatus(ShareStatusEnum.EXPIRED.getValue());
            }
            return subDto;
        }).collect(Collectors.toList());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void masterRemoveShare(Long shareId) {
        DeviceShare deviceShare = getById(shareId);
        //校验分享记录是否存在
        if (deviceShare == null) {
            throw ExceptionMessageUtil.getException(AppErrorCode.APP_SHARE_NOT_EXIST);
        }
        //校验登陆用户是否为主账户
        if (!deviceShare.getMasterId().equals(StpUtil.getLoginIdAsLong())) {
            throw ExceptionMessageUtil.getException(AppErrorCode.APP_SHARE_REMOVE_AUTH_ERROR);
        }

        masterRemoveShare(deviceShare);
    }

    private void masterRemoveShare(DeviceShare deviceShare) {
        removeById(deviceShare.getId());
        if (ShareStatusEnum.ACCEPTED.getValue() == deviceShare.getStatus()) {
            //删除设备与子用户绑定关系
            appDeviceService.unbind(deviceShare.getDeviceId(), deviceShare.getSubId(), false);
            //推送消息给子用户
            ProductRpcVo productRpcVo = checkProduct(deviceShare.getDeviceId());
            pushMsg(ShareOperateEnum.MASTER_REMOVE, deviceShare, productRpcVo.getCommodityModel());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void subRemoveShare(Long shareId) {
        DeviceShare deviceShare = getById(shareId);
        //校验分享记录是否存在
        if (deviceShare == null) {
            throw ExceptionMessageUtil.getException(AppErrorCode.APP_SHARE_NOT_EXIST);
        }
        //校验登陆用户是否为子账户
        if (!deviceShare.getSubId().equals(StpUtil.getLoginIdAsLong())) {
            throw ExceptionMessageUtil.getException(AppErrorCode.APP_SHARE_REMOVE_AUTH_ERROR);
        }
        //如果是过期分享，则只移除记录
        if (ShareStatusEnum.EXPIRED.getValue() == deviceShare.getStatus()) {
            removeById(shareId);
            return;
        }

        //更新该分享记录状态为已过期
        updateShareStatus(shareId, ShareStatusEnum.DELETE_EXPIRED);
        if (ShareStatusEnum.ACCEPTED.getValue() == deviceShare.getStatus()) {
            //删除设备与子用户绑定关系
            appDeviceService.unbind(deviceShare.getDeviceId(), deviceShare.getSubId(), false);
            //推送消息给主用户
            AppUserDevice masterUserDevice = appUserDeviceService.findMasterUserDevice(deviceShare.getDeviceId());
            pushMsg(ShareOperateEnum.SUB_REMOVE_DEVICE, deviceShare, masterUserDevice.getDeviceNickName());
        }
    }

    @Override
    public void clearShare(AppUserDevice userDevice) {
        //无需分享设备，不作处理
        if (userDevice.getShareType() == null || userDevice.getShareType() == ShareTypeEnum.NONE.getValue()) {
            return;
        }
        //主账户解除分享，需要将子账户的绑定移除
        if (userDevice.getShareType() == ShareTypeEnum.MASTER.getValue()) {
            List<DeviceShare> list = list(new LambdaQueryWrapper<DeviceShare>()
                    .eq(DeviceShare::getDeviceId, userDevice.getDeviceId())
                    .eq(DeviceShare::getMasterId, userDevice.getUserId()));
            if (CollectionUtils.isEmpty(list)) {
                return;
            }
            for (DeviceShare deviceShare : list) {
                masterRemoveShare(deviceShare);
            }
            //子账户解除分享，只需将自己的分享记录移除
        } else if (userDevice.getShareType() == ShareTypeEnum.SUB.getValue()) {
            DeviceShare deviceShare = getOne(new LambdaQueryWrapper<DeviceShare>()
                    .eq(DeviceShare::getDeviceId, userDevice.getDeviceId())
                    .eq(DeviceShare::getSubId, userDevice.getUserId())
                    .eq(DeviceShare::getStatus, ShareStatusEnum.ACCEPTED.getValue())
                    .last("LIMIT 1"));
            if (deviceShare == null) {
                return;
            }
            //更新该分享记录状态为已过期
            updateShareStatus(deviceShare.getId(), ShareStatusEnum.DELETE_EXPIRED);
            AppUserDevice masterUserDevice = appUserDeviceService.findMasterUserDevice(deviceShare.getDeviceId());
            pushMsg(ShareOperateEnum.SUB_REMOVE_DEVICE, deviceShare, masterUserDevice.getDeviceNickName());
        }else {
            //do nothing
        }
    }

    @Override
    public void clearShareOfUser(Long userId) {
        remove(new LambdaQueryWrapper<DeviceShare>().eq(DeviceShare::getSubId, userId));
        List<DeviceShare> masterList = list(new LambdaQueryWrapper<DeviceShare>().eq(DeviceShare::getMasterId, userId));
        if (CollectionUtils.isEmpty(masterList)) {
            return;
        }
        for (DeviceShare deviceShare : masterList) {
            masterRemoveShare(deviceShare);
        }
    }

    @Override
    public long pendingCount() {
        return count(new LambdaQueryWrapper<DeviceShare>()
                .eq(DeviceShare::getSubId, StpUtil.getLoginIdAsLong())
                .eq(DeviceShare::getStatus, ShareStatusEnum.PENDING.getValue()));
    }

    @Override
    public void setUserDeviceShareType(AppUserDevice appUserDevice, boolean isPaired,Boolean isSharingSupported) {
        appUserDevice.setShareType(ShareTypeEnum.NONE.getValue());
        //如果是不需要分享的类型，定义为不可分享类型
        if (!Boolean.TRUE.equals(isSharingSupported)) {
            return;
        }
        //sourceCode增加一个枚举类型
        //如果是CRM同步过来用户,且未配对，需要配对则先定义为不可分享类型
        if (AppUserSourceCodeEnum.isCRM(appUserDevice.getSourceCode()) && !isPaired) {
            return;
        }
        appUserDevice.setShareType(ShareTypeEnum.MASTER.getValue());
    }

    @Override
    public void expire() {
        final int batchSize = 50;
        List<DeviceShare> list = findExpiredShare();
        List<Long> idList = Lists.newArrayListWithCapacity(batchSize);
        for (int i = 0; i < list.size(); i++) {
            DeviceShare deviceShare = list.get(i);
            idList.add(deviceShare.getId());
            //批量更新设备分享过期记录
            if (idList.size() >= batchSize || i == list.size() - 1) {
                update(new LambdaUpdateWrapper<DeviceShare>()
                        .set(DeviceShare::getStatus, ShareStatusEnum.EXPIRED.getValue())
                        .in(DeviceShare::getId, idList));
                idList = Lists.newArrayListWithCapacity(batchSize);
            }

            //推送消息给主用户
            AppUserDevice masterUserDevice = appUserDeviceService.findMasterUserDevice(deviceShare.getDeviceId());
            pushMsg(ShareOperateEnum.SUB_REMOVE_SHARE, deviceShare, masterUserDevice.getDeviceNickName());
        }
    }

    @Override
    public void updateAfterRegister(String email, Long userId) {
        //查询分享记录中，还是待接受的消息
        List<DeviceShare> list = list(new LambdaQueryWrapper<DeviceShare>()
                .select(DeviceShare::getId, DeviceShare::getStatus, DeviceShare::getSubId,
                        DeviceShare::getMasterEmail, DeviceShare::getDeviceId)
                .eq(DeviceShare::getSubEmail, email)
                .eq(DeviceShare::getStatus, ShareStatusEnum.PENDING.getValue()));
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        for (DeviceShare deviceShare : list) {
            //将用户id更新到分享记录中
            if (deviceShare.getSubId() == null) {
                update(new LambdaUpdateWrapper<DeviceShare>()
                        .set(DeviceShare::getSubId, userId)
                        .eq(DeviceShare::getId, deviceShare.getId()));
            }
            //对待接收的分享，推送分享消息给子用户
            scheduledExecutorService.schedule(() -> {
                ProductRpcVo productRpcVo = remoteProductService.getAppProductVoByDeviceId(deviceShare.getDeviceId());
                String modelOrName = productRpcVo == null ? "one device" : productRpcVo.getCommodityModel();
                deviceShare.setSubId(userId);
                pushMsg(ShareOperateEnum.MASTER_SHARE, deviceShare, modelOrName);
            }, CommonConstant.SEVEN, TimeUnit.SECONDS);
        }
    }

    /**
     * 查询设备icon、型号等信息
     *
     * @return map
     */
    private Map<String, DeviceRpcVo> getDeviceRpcInfo(List<String> deviceIds) {
        if (CollectionUtils.isEmpty(deviceIds)) {
            return new HashMap<>();
        }
        Map<String, DeviceRpcVo> deviceRpcVoMap = RedisUtils.getMultiCacheMapValue(
                DEVICE_CACHE_REDIS_KEY, Sets.newHashSet(deviceIds));
        List<String> noCacheDevice = new ArrayList<>();
        if (deviceRpcVoMap != null) {
            for (String deviceId : deviceIds) {
                if (!deviceRpcVoMap.containsKey(deviceId)) {
                    noCacheDevice.add(deviceId);
                }
            }
        } else {
            noCacheDevice = deviceIds;
            deviceRpcVoMap = new HashMap<>();
        }
        if (CollectionUtils.isNotEmpty(noCacheDevice)) {
            List<DeviceRpcVo> deviceRpcVoList = remoteDeviceManageService.listSimpleByDeviceId(deviceIds);
            if (CollectionUtils.isNotEmpty(deviceRpcVoList)) {
                for (DeviceRpcVo deviceRpcVo : deviceRpcVoList) {
                    deviceRpcVo.setDeviceIcon(getFileUrl(deviceRpcVo.getDeviceIcon()));
                    deviceRpcVoMap.put(deviceRpcVo.getDeviceId(), deviceRpcVo);
                    //设备基础信息缓存10s
                    RedisUtils.setCacheMapValueWithExpire(
                            DEVICE_CACHE_REDIS_KEY, deviceRpcVo.getDeviceId(), deviceRpcVo, 10);
                }
            }
        }
        return deviceRpcVoMap;
    }

    /**
     * 计算过期天数
     *
     * @return count
     */
    private int computeExpiredDays(LocalDateTime expiredDate) {
        long time1 = LocalDateTime.now().toEpochSecond(ZoneOffset.UTC);
        long time2 = expiredDate.toEpochSecond(ZoneOffset.UTC);
        if (time1 >= time2) {
            return 0;
        }
        return (int) ((time2 - time1) / 3600 / 24) + 1;
    }


    /**
     * 更新分享记录状态
     */
    private void updateShareStatus(Long shareId, ShareStatusEnum shareStatus) {
        update(new LambdaUpdateWrapper<DeviceShare>()
                .set(DeviceShare::getStatus, shareStatus.getValue())
                .eq(DeviceShare::getId, shareId));
    }

    private boolean updateShareStatus(Long shareId, ShareStatusEnum shareStatus,ShareStatusEnum preShareStatus) {
        return update(new LambdaUpdateWrapper<DeviceShare>()
                .set(DeviceShare::getStatus, shareStatus.getValue())
                .eq(DeviceShare::getId, shareId)
                .eq(DeviceShare::getStatus,preShareStatus.getValue())
        );
    }
    /**
     * 组织消息并推送
     *
     * @param operateEnum 分享操作类型
     * @param deviceShare 分享信息
     * @param modelOrName 商品型号或设备昵称
     */
    private void pushMsg(ShareOperateEnum operateEnum, DeviceShare deviceShare, String modelOrName) {
        Long targetUserId;
        String msgTemplateEmail;
        switch (operateEnum) {
            case MASTER_SHARE:
            case MASTER_REMOVE:
                msgTemplateEmail = deviceShare.getMasterEmail();
                targetUserId = deviceShare.getSubId();
                break;
            case SUB_REMOVE_DEVICE:
            case SUB_REMOVE_SHARE:
            case SUB_ACCEPT:
                msgTemplateEmail = deviceShare.getSubEmail();
                targetUserId = deviceShare.getMasterId();
                break;
            default:
                return;
        }
        //目标用户ID不存在，则返回
        if (targetUserId == null) {
            log.warn("targetUserId is null!");
            return;
        }
        //目标用户不存在，则返回
        AppUserVo appUser = remoteAppUserService.getAppUser(targetUserId);
        if (appUser == null) {
            log.warn("targetUser is null! userId:{}", targetUserId);
            return;
        }

        //如果子用户配置信息不存在，且推送消息为分享邀请，则认为账号未验证，不推送
        UserSetting userSetting = userSettingService.findByUserId(targetUserId);
        if (userSetting == null && ShareOperateEnum.MASTER_SHARE.equals(operateEnum)) {
            return;
        }

        MessageDto messageDto = new MessageDto();


        messageDto.setPushTypes(Lists.newArrayList(0, 2));
        messageDto.setUserId(targetUserId.toString());
        String token = null;
        String language = null;
        if (userSetting != null) {
            token = userSetting.getPushToken();
            language = userSetting.getLanguage();
            Integer systemMessageSwitch = userSetting.getSystemMessageSwitch();
            //如果系统消息配置未启用，则不推送分享消息
            if (systemMessageSwitch != null && !systemMessageSwitch.equals(SwitchFlagEnum.OPEN.getType())) {
                messageDto.setPushSwitch(SwitchFlagEnum.CLOSE.getType());
            }
        }
        messageDto.setToken(token);
        //获取推送标题多语言信息
        Map<String, String> titleLangMap =
                langMsgMap(StaticMultiLanguageEnum.DEVICE_SHARE_ENUM_MSG_TITLE.getCode(), language);
        String title = titleLangMap.get(operateEnum.getValue() + "");
        messageDto.setTitle(title == null ? operateEnum.getMsgTitle() : title);
        //获取推送内容多语言信息
        Map<String, String> contentLangMap =
                langMsgMap(StaticMultiLanguageEnum.DEVICE_SHARE_ENUM_MSG_CONTENT.getCode(), language);
        String msgTemplate = contentLangMap.get(operateEnum.getValue() + "");
        String pushContent = String.format(StringUtils.isEmpty(msgTemplate)
                ? operateEnum.getMsgContent() : msgTemplate, msgTemplateEmail, modelOrName);
        messageDto.setContent(pushContent);
        if (appUser.getAppTypeCode() == null) {
            messageDto.setDeviceType(OsType.ALL);
        } else {
            messageDto.setDeviceType(OsType.valueOf(appUser.getAppTypeCode().toUpperCase()));
        }
        //如果子账号app版本低于设备分享版本时，推送消息让用户升级app
        String appVersion = appUser.getAppVersion();
        if (StringUtils.isNotEmpty(appVersion)
                && appVersion.compareToIgnoreCase(lessVersion) < 0
                && ShareOperateEnum.MASTER_SHARE.equals(operateEnum)) {
            pushUpgradeMsg(messageDto.getUserId(), token,
                    messageDto.getDeviceType(),deviceShare.getId() + "",language);
            return;
        }
        Map<String, String> payloadData = Maps.newHashMap();
        payloadData.put("shareOperateType", operateEnum.getValue() + "");
        payloadData.put("deviceId", deviceShare.getDeviceId());
        payloadData.put("shareStatus", deviceShare.getStatus() + "");
        payloadData.put("rutePath", operateEnum.getRutePath());
        messageDto.setPayloadData(payloadData);
        messageDto.setMessageType(MessageTypeEnum.SHARE_MSG.getValue());

        messageDto.setSystemMessageId(deviceShare.getId() + "");
        messageService.pushMessage(Lists.newArrayList(messageDto));
    }

    private void pushUpgradeMsg(String userId, String token, OsType deviceType,String sysMsgId,String language) {
        language = StringUtils.isNotEmpty(language) ? language : LocaleContextHolder.getLocale().getLanguage();
        String title = messageTools.getCodeValue(
                StaticMultiLanguageEnum.DEVICE_SHARE_UPGRADE_MSG_TITLE.getCode()
                , language);
        String content = messageTools.getCodeValue(
                StaticMultiLanguageEnum.DEVICE_SHARE_UPGRADE_MSG_CONTENT.getCode()
                , language);
        MessageDto messageDto = new MessageDto();
        messageDto.setTitle(title);
        messageDto.setContent(content);

        messageDto.setUserId(userId);
        messageDto.setToken(token);
        Map<String, String> payloadDataMap = Maps.newHashMap();
        payloadDataMap.put("rutePath", upgradeUrl);
        //安卓老版本根据这个字段判断是否需要跳转
        payloadDataMap.put("suggestionExtra", upgradeUrl);
        messageDto.setPayloadData(payloadDataMap);
        messageDto.setMessageType(MessageTypeEnum.SYS_MSG.getValue());
        messageDto.setDeviceType(deviceType);
        List<Integer> pushTypeList = Lists.newArrayList(PushTypeHandlerEnum.BANNER.getPushTypes());
        if(OsType.IOS.equals(deviceType)){
            //如果是IOS类型则推送墓碑推送，安卓因为低版本存在闪退，故不推送墓碑
            pushTypeList.add(PushTypeHandlerEnum.TOMBSTONE.getPushTypes());
        }
        messageDto.setPushTypes(pushTypeList);
        messageDto.setSystemMessageId(sysMsgId);
        messageService.pushMessage(Lists.newArrayList(messageDto));
    }

    /**
     * 查询过期未接收的分享记录
     *
     * @return 分享记录
     */
    private List<DeviceShare> findExpiredShare() {
        return list(new LambdaQueryWrapper<DeviceShare>()
                .select(DeviceShare::getId,
                        DeviceShare::getDeviceId,
                        DeviceShare::getSubEmail,
                        DeviceShare::getMasterId)
                .le(DeviceShare::getExpiredDate, LocalDateTime.now())
                .eq(DeviceShare::getStatus, ShareStatusEnum.PENDING.getValue()));
    }

    private Map<String, Integer> countShareNum( Long masterId) {
        List<Map<String, Object>> maps = baseMapper.countShareNum( masterId);
        Map<String, Integer> result = new HashMap<>();
        if (CollectionUtils.isNotEmpty(maps)) {
            for (Map<String, Object> map : maps) {
                Object deviceId = map.get(DEVICE_ID);
                Object num = map.get("num");
                if (deviceId != null && num != null) {
                    result.put(deviceId.toString(), Integer.valueOf(num.toString()));
                }
            }
        }
        return result;
    }

    /**
     * 根据地址+图片上传类型获取图片地址
     *
     * @param url 图片url
     * @return url字符串
     */
    private String getFileUrl(String url) {
        if (StringUtils.isEmpty(url)) {
            return null;
        }
        return UrlUtil.completeUrl(awsProperties.getPictureBucket().getCdnHost(), url);
    }

    private Map<String, String> langMsgMap(String dictName, String language) {

        List<DictVo> dictList = dictService.listByDictName(
                StringUtils.isNotEmpty(language) ? language : LocaleContextHolder.getLocale().getLanguage(),
                Collections.singletonList(dictName));
        if (CollectionUtils.isEmpty(dictList)) {
            return Collections.emptyMap();
        }
        return dictList.stream().flatMap(dictVo -> dictVo.getNodes().stream())
                .collect(Collectors.toMap(DictNode::getLabel, DictNode::getDescription));
    }
}
