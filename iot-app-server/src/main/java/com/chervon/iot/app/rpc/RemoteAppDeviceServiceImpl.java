package com.chervon.iot.app.rpc;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.chervon.common.core.domain.PageResult;
import com.chervon.common.core.utils.ConvertUtil;
import com.chervon.iot.app.api.RemoteAppDeviceService;
import com.chervon.iot.app.api.dto.AppUserDeviceDTO;
import com.chervon.iot.app.api.dto.BoundDeviceQueryDto;
import com.chervon.iot.app.api.vo.BoundDeviceRpcVo;
import com.chervon.iot.app.api.vo.TargetUserVo;
import com.chervon.iot.app.api.vo.UserIdDeviceIdVo;
import com.chervon.iot.app.domain.dataobject.AppUserDevice;
import com.chervon.iot.app.domain.dataobject.DeviceAlarm;
import com.chervon.iot.app.mapper.AppUserDeviceMapper;
import com.chervon.iot.app.mapper.DevicePartsMapper;
import com.chervon.iot.app.service.AppDeviceService;
import com.chervon.iot.app.service.DeviceAlarmService;
import com.chervon.iot.app.service.DevicePartsService;
import com.chervon.technology.api.RemoteDeviceIdService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2022-07-13 16:57
 **/
@Slf4j
@DubboService
public class RemoteAppDeviceServiceImpl implements RemoteAppDeviceService {
    @Resource
    private AppDeviceService appDeviceService;
    @Resource
    private AppUserDeviceMapper appUserDeviceMapper;
    @DubboReference
    private RemoteDeviceIdService remoteDeviceIdService;
    @Resource
    private DevicePartsMapper devicePartsMapper;
    @Autowired
    private DevicePartsService devicePartsService;
    @Autowired
    private DeviceAlarmService deviceAlarmService;

    @Override
    public Map<String, List<Long>> getBoundUserIdsMap(List<String> deviceIds) {
        Map<String, List<Long>> target = new HashMap<>(deviceIds.size());
        for (String deviceId : deviceIds) {
            LambdaQueryWrapper<AppUserDevice> userDeviceWrapper = new LambdaQueryWrapper<AppUserDevice>()
                    .eq(AppUserDevice::getDeviceId, deviceId).orderByDesc(AppUserDevice::getCreateTime)
                    .select(AppUserDevice::getUserId);
            List<Long> userIds = appDeviceService.list(userDeviceWrapper).stream().map(AppUserDevice::getUserId)
                    .collect(Collectors.toList());
            target.put(deviceId, userIds);
        }
        return target;
    }

    @Override
    public List<Long> getBoundUserId(String deviceId) {
        LambdaQueryWrapper<AppUserDevice> userDeviceWrapper = new LambdaQueryWrapper<AppUserDevice>()
                .eq(AppUserDevice::getDeviceId, deviceId).orderByDesc(AppUserDevice::getCreateTime)
                .select(AppUserDevice::getUserId);
        return appDeviceService.list(userDeviceWrapper).stream().filter(Objects::nonNull).map(AppUserDevice::getUserId).filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    /**
     * 通过设备Id获取目标用户列表
     *
     * @param deviceId
     * @return
     */
    @Override
    public List<TargetUserVo> getDeviceAlarmUsers(String deviceId) {
        LambdaQueryWrapper<DeviceAlarm> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(DeviceAlarm::getDeviceId, deviceId)
                .isNotNull(DeviceAlarm::getTelNumber);
        List<DeviceAlarm> deviceAlarms = deviceAlarmService.list(wrapper);
        List<TargetUserVo> targetUserVos = ConvertUtil.convertList(deviceAlarms, TargetUserVo.class);
        return targetUserVos;
    }

    @Override
    public PageResult<BoundDeviceRpcVo> pageAppUserDeviceByUserId(BoundDeviceQueryDto boundDeviceQueryDto) {
        LambdaQueryWrapper<AppUserDevice> userDeviceLambdaQueryWrapper = new LambdaQueryWrapper<AppUserDevice>()
                .eq(AppUserDevice::getUserId, boundDeviceQueryDto.getUserId()).orderByDesc(AppUserDevice::getCreateTime)
                .select(AppUserDevice::getDeviceId);
        Page<AppUserDevice> page = appUserDeviceMapper.selectPage(new Page<>(boundDeviceQueryDto.getPageNum(),
                boundDeviceQueryDto.getPageSize()), userDeviceLambdaQueryWrapper);
        PageResult<BoundDeviceRpcVo> result = new PageResult<>(page.getCurrent(), page.getSize(), page.getTotal());
        result.setList(ConvertUtil.convertList(page.getRecords(), BoundDeviceRpcVo.class));
        result.getList().forEach(a -> a.setUserIdList(this.getBoundUserId(a.getDeviceId())));
        return result;
    }

    @Override
    public List<Long> listUserIdByUserIdAndDeviceIds(String userId, List<String> deviceIds) {
        List<AppUserDevice> appUserDevices = appUserDeviceMapper.selectList(new LambdaQueryWrapper<AppUserDevice>()
                .like(StringUtils.isNotBlank(userId), AppUserDevice::getUserId, userId)
                .in(!CollectionUtils.isEmpty(deviceIds), AppUserDevice::getDeviceId, deviceIds)
                .orderByDesc(AppUserDevice::getCreateTime)
                .select(AppUserDevice::getUserId));
        return appUserDevices.stream().map(AppUserDevice::getUserId).distinct().collect(Collectors.toList());
    }

    @Override
    public List<UserIdDeviceIdVo> listDeviceIdByUserIds(List<Long> userIds) {
        List<AppUserDevice> appUserDevices = appUserDeviceMapper.selectList(new LambdaQueryWrapper<AppUserDevice>()
                .in(!CollectionUtils.isEmpty(userIds), AppUserDevice::getUserId, userIds)
                .orderByDesc(AppUserDevice::getCreateTime)
                .select(AppUserDevice::getDeviceId, AppUserDevice::getUserId, AppUserDevice::getShareType));
        return appUserDevices.stream().map(e -> {
            UserIdDeviceIdVo vo = new UserIdDeviceIdVo();
            vo.setDeviceId(e.getDeviceId());
            vo.setUserId(e.getUserId());
            vo.setShareType(e.getShareType());
            return vo;
        }).collect(Collectors.toList());
    }

    @Override
    public List<String> listDeviceIdLikeUserId(String userId) {
        List<AppUserDevice> appUserDevices = appUserDeviceMapper.selectList(new LambdaQueryWrapper<AppUserDevice>()
                .like(StringUtils.isNotBlank(userId), AppUserDevice::getUserId, userId)
                .orderByDesc(AppUserDevice::getCreateTime)
                .select(AppUserDevice::getDeviceId));
        return appUserDevices.stream()
                .filter(Objects::nonNull)
                .map(AppUserDevice::getDeviceId)
                .filter(StringUtils::isNotBlank)
                .distinct()
                .collect(Collectors.toList());
    }

    @Override
    public Map<String, List<Long>> mapDeviceIdUserIds(List<String> deviceIds) {
        List<AppUserDevice> appUserDevices = appUserDeviceMapper.selectList(new LambdaQueryWrapper<AppUserDevice>().in(AppUserDevice::getDeviceId, deviceIds).orderByDesc(AppUserDevice::getCreateTime));
        appUserDevices.removeIf(e -> e.getUserId() == null);
        return appUserDevices.stream().collect(Collectors.groupingBy(AppUserDevice::getDeviceId,
                Collectors.mapping(AppUserDevice::getUserId, Collectors.toList())));
    }


    @Override
    public List<AppUserDeviceDTO> getAppUserDeviceInfoByDeviceId(String deviceId,Long userId) {

       return appDeviceService.list(Wrappers.<AppUserDevice>lambdaQuery()
                       .select(AppUserDevice::getUserId, AppUserDevice::getDeviceNickName)
                       .eq(AppUserDevice::getDeviceId, deviceId)
                       .eq(Objects.nonNull(userId), AppUserDevice::getUserId, userId)
                       .orderByDesc(AppUserDevice::getCreateTime)
               )
               .stream()
               .map(x->
                       AppUserDeviceDTO.builder()
                       .userId(x.getUserId())
                       .deviceNickName(x.getDeviceNickName())
                       .build()
               )
               .collect(Collectors.toList());
    }
}
