package com.chervon.iot.app.controller;

import com.chervon.operation.api.RemoteIntroductionService;
import com.chervon.operation.api.dto.IntroductionTypeDto;
import com.chervon.operation.api.vo.IntroductionVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 引导页相关接口
 *
 * <AUTHOR>
 * @since 2022-09-02 10:58
 **/
@Api(tags = "引导页相关接口")
@RestController
@RequestMapping("/introduction")
public class IntroductionController {

    @DubboReference
    private RemoteIntroductionService remoteIntroductionService;

    @ApiOperation("根据类型获取引导页列表")
    @PostMapping("/list/by/type")
    public List<IntroductionVo> list(@RequestBody IntroductionTypeDto req) {
        return remoteIntroductionService.listIntroductionByType(req);
    }
}
