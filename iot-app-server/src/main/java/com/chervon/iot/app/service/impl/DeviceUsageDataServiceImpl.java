package com.chervon.iot.app.service.impl;

import com.alibaba.csp.sentinel.util.function.Tuple2;
import com.chervon.common.core.constant.StringPool;
import com.chervon.common.core.utils.StringUtils;
import com.chervon.iot.app.config.DeviceUsageHisProperties;
import com.chervon.iot.app.domain.dto.device.DeviceUsageReqDto;
import com.chervon.iot.app.domain.dto.device.UsageHistoryReqDto;
import com.chervon.iot.app.domain.dto.usage.UsageTimeQuery;
import com.chervon.iot.app.domain.enums.QueryDateTypeEnum;
import com.chervon.iot.app.domain.vo.device.UsageHistoryVo;
import com.chervon.iot.app.service.DeviceUsageDataService;
import com.chervon.iot.app.util.DateUtils;
import com.chervon.iot.middle.api.dto.query.UsageShadowQuery;
import com.chervon.iot.middle.api.pojo.usage.UsageKeyValue;
import com.chervon.iot.middle.api.service.RemoteIotDataUsageService;
import io.lettuce.core.KeyValue;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 设备使用数据统计查询服务实现类
 *
 * <AUTHOR>
 * @date 2023/11/1 19:32
 */
@Service
@Slf4j
public class DeviceUsageDataServiceImpl implements DeviceUsageDataService {

    @Autowired
    private DeviceUsageHisProperties deviceUsageHisProperties;
    @DubboReference
    private RemoteIotDataUsageService remoteIotDataUsageService;

    private static final String IS_NOT_NULL = " is not null";
    private static final String IDF_PREFIX = "idf_";
    private static final String CAST_PREFIX = "cast(";
    private static final String CAST_SUFFIX = " as bigint)";
    private static final String AS = " as ";
    private static final String CEIL = "ceil(";
    private static final String OPEN_PARENTHESIS = StringPool.OPEN_PARENTHESIS;
    private static final String CLOSE_PARENTHESIS = StringPool.CLOSE_PARENTHESIS;
    private static final String DIVIDE = StringPool.DIVIDE;
    private static final String AND = StringPool.AND;
    private static final String REGEX_LINE = "\\|";
    private static final String COMMA = StringPool.COMMA;
    private static final String ASTERISK = StringPool.ASTERISK;
    private static final String STROKE = "`";


    private static UsageShadowQuery getUsageShadowQuery(String index, String deviceId, UsageTimeQuery timeQuery, int type) {
        // 组装查询参数
        UsageShadowQuery query = new UsageShadowQuery();
        String sql;
        String[] i0 = index.split(AND);
        // 拼装时间范围内的设备工况sql
        if (type == 0) {
            String[] i1 = i0[1].split(REGEX_LINE);
            // 如果数组i1为空，即没有配置该工况的单次物模型ID，则不进行sql的拼接和query的组装
            if (i1.length == 0) {
                return null;
            }
            sql = i1[0] + OPEN_PARENTHESIS + CAST_PREFIX + IDF_PREFIX + i1[1] + CAST_SUFFIX + CLOSE_PARENTHESIS + ASTERISK + i1[2] + " as dataValue";
            query.setWhere(IDF_PREFIX + i1[1] + IS_NOT_NULL);
            query.setSql(sql);
            query.setDeviceId(deviceId);
            query.setTimeStart(timeQuery.getTimeStart());
            query.setTimeEnd(timeQuery.getTimeEnd());
        } else {
            String[] i00 = i0[0].split(REGEX_LINE);
            // 如果数组i00为空，即没有配置该工况的累计物模型ID，则不进行sql的拼接和query的组装
            if (i00.length == 0) {
                return null;
            }
            // 拼装总设备工况sql
            sql = CAST_PREFIX + IDF_PREFIX + i00[0] + CAST_SUFFIX + ASTERISK + i00[1] + " as totalValue";
            query.setWhere(IDF_PREFIX + i00[0] + IS_NOT_NULL);
            query.setSql(sql);
            query.setDeviceId(deviceId);
        }
        return query;
    }

    @Override
    public UsageHistoryVo getUsageHistory(UsageHistoryReqDto requestDto) {
        // 组装返回VO
        UsageHistoryVo usageHistoryVo = new UsageHistoryVo();
        usageHistoryVo.setDeviceId(requestDto.getDeviceId());
        usageHistoryVo.setDateType(requestDto.getDateType());
        usageHistoryVo.setDateValue(requestDto.getDateValue());

        // 根据 dateValue、dateType、datePeriod 组装查询时间条件
        UsageTimeQuery timeQuery = DateUtils.getDayBetween(requestDto.getDateValue(), requestDto.getDateType(), requestDto.getDatePeriod());
        String deviceId = requestDto.getDeviceId();
        List<String> busTypes = requestDto.getBusType();
        Map<String, List<KeyValue<String, Long>>> dataList = new HashMap<>(busTypes.size());
        Map<String, Long> totalValueList = new HashMap<>(busTypes.size());

        List<DeviceUsageHisProperties.AttributesIndex> productConfig = deviceUsageHisProperties.getProduct();
        if (CollectionUtils.isEmpty(productConfig)) {
            return noConfigDefaultResult(dataList, usageHistoryVo, totalValueList, "No Config");
        }
        // 遍历处理busType
        for (String busType : busTypes) {
            String snCode = deviceId.substring(1, 5);
            Optional<DeviceUsageHisProperties.AttributesIndex> optionalProduct = productConfig.stream()
                    .filter(item -> item.getSnCode().equals(snCode))
                    .findFirst();

            if (!optionalProduct.isPresent()) {
                return noConfigDefaultResult(dataList, usageHistoryVo, totalValueList, "No SN Config");
            }
            Map<String, String> indexMap = optionalProduct.get().getUsageHistory();
            // 获取计算指标
            String index = indexMap.get(busType);
            // 组装查询条件及执行查询
            if (StringUtils.isNotEmpty(index)) {
                UsageShadowQuery query0 = getUsageShadowQuery(index, deviceId, timeQuery, 0);
                UsageShadowQuery query1 = getUsageShadowQuery(index, deviceId, timeQuery, 1);
                // 统计周期内数据
                List<UsageKeyValue> usageData = null;
                if (Objects.nonNull(query0)) {
                    usageData = QueryDateTypeEnum.DATE.getType() == requestDto.getDateType()
                            ? remoteIotDataUsageService.getDayHistory(query0, timeQuery.getDayBetweenList())
                            : remoteIotDataUsageService.getWMHistory(query0, timeQuery.getTimeQueryList());
                }
                // 计算设备工况历史总和值
                Long totalValue = null;
                if (Objects.nonNull(query1)) {
                    totalValue = remoteIotDataUsageService.getTotalHistory(query1);
                }
                if (Objects.nonNull(totalValue)) {
                    totalValueList.put(busType, totalValue);
                } else {
                    totalValueList.put(busType, 0L);
                }
                dataList.put(busType, timeDataTransfer(timeQuery, usageData));
            } else {
                noConfigDefaultResult(dataList, usageHistoryVo, totalValueList, busType + " Not Config");
            }
        }
        Long lastSyncedTime = remoteIotDataUsageService.getLastSyncedTime(requestDto.getDeviceId());
        usageHistoryVo.setLastSyncedTime(lastSyncedTime);
        usageHistoryVo.setDataList(dataList);
        usageHistoryVo.setTotalValue(totalValueList);

        return usageHistoryVo;
    }

    @NotNull
    private static UsageHistoryVo noConfigDefaultResult(Map<String, List<KeyValue<String, Long>>> dataList, UsageHistoryVo usageHistoryVo, Map<String, Long> totalValueList, String title) {
        log.warn("UsageHistory DeviceUsageHisProperties:{}", title);
        dataList.put(title, new ArrayList<>());
        usageHistoryVo.setDataList(dataList);
        totalValueList.put(title, 0L);
        usageHistoryVo.setTotalValue(totalValueList);
        return usageHistoryVo;
    }

    @Override
    public Object getTracksHistory(DeviceUsageReqDto requestDto) {
        String snCode = requestDto.getDeviceId().substring(1, 5);
        final Tuple2<Long, Long> dayBeginEnd = DateUtils.getDayBeginEnd(requestDto.getDate());

        UsageShadowQuery query1 = createUsageShadowQuery(requestDto, dayBeginEnd);
        UsageShadowQuery query2 = createUsageShadowQuery(requestDto, dayBeginEnd);

        // 获取product list
        List<DeviceUsageHisProperties.AttributesIndex> productList = deviceUsageHisProperties.getProduct();
        if (Objects.isNull(productList)) {
            log.warn("TracksHistory DeviceUsageHisProperties.Product list does not config");
            return null;
        }
        // 根据snCode查找product
        Optional<DeviceUsageHisProperties.AttributesIndex> optionalProduct = findProductBySnCode(productList, snCode);
        if (optionalProduct.isPresent()) {
            DeviceUsageHisProperties.AttributesIndex product = optionalProduct.get();
            buildTracksHistorySql(query1, product);
            buildPointStatusSql(query2, product);
        } else {
            log.warn("TracksHistory DeviceUsageHisProperties.Product snCode:{} does not config", snCode);
            return null;
        }

        return remoteIotDataUsageService.getUsageTrackHistory(query1, query2);
    }

    private UsageShadowQuery createUsageShadowQuery(DeviceUsageReqDto requestDto, Tuple2<Long, Long> dayBeginEnd) {
        UsageShadowQuery query = new UsageShadowQuery();
        query.setDeviceId(requestDto.getDeviceId());
        query.setDate(requestDto.getDate());
        query.setTimeStart(dayBeginEnd.r1);
        query.setTimeEnd(dayBeginEnd.r2);
        return query;
    }

    private Optional<DeviceUsageHisProperties.AttributesIndex> findProductBySnCode(List<DeviceUsageHisProperties.AttributesIndex> productList, String snCode) {
        return productList.stream()
                .filter(item -> item.getSnCode().equals(snCode))
                .findFirst();
    }

    /**
     * 构建查询轨迹历史工况sql
     * @param query UsageShadowQuery
     * @param product 产品配置参数
     */
    private void buildTracksHistorySql(UsageShadowQuery query, DeviceUsageHisProperties.AttributesIndex product) {
        Map<String, String> indexMap = product.getTracksHistory();
        StringBuilder sql1 = new StringBuilder();
        for (Map.Entry<String, String> entry : indexMap.entrySet()) {
            String key = entry.getKey();
            String value = entry.getValue();
            String[] parts = value.split(REGEX_LINE);
            if (parts.length > 1 && parts[1].contains(DIVIDE)) {
                String[] subParts = parts[1].split(DIVIDE);
                sql1.append(CEIL).append(parts[0]).append(OPEN_PARENTHESIS).append(CAST_PREFIX + IDF_PREFIX).append(subParts[0]).append(CAST_SUFFIX).append(CLOSE_PARENTHESIS).append(DIVIDE)
                        .append(parts[0]).append(OPEN_PARENTHESIS).append(CAST_PREFIX + IDF_PREFIX).append(subParts[1]).append(CAST_SUFFIX).append(CLOSE_PARENTHESIS).append(ASTERISK).append(parts[2]).append(CLOSE_PARENTHESIS).append(AS).append(STROKE).append(key).append(STROKE).append(COMMA);
            } else {
                sql1.append(CEIL).append(CAST_PREFIX).append(parts[0]).append(OPEN_PARENTHESIS).append(CAST_PREFIX + IDF_PREFIX).append(parts[1]).append(CAST_SUFFIX)
                        .append(CLOSE_PARENTHESIS).append(CAST_SUFFIX).append(ASTERISK).append(parts[2]).append(CLOSE_PARENTHESIS).append(AS).append(STROKE).append(key).append(STROKE).append(COMMA);
            }
        }
        query.setSql(sql1.substring(0, sql1.length() - 1));
        query.setIndexList(indexMap.keySet());
    }

    /**
     * 构建查询点位状态sql
     * @param query UsageShadowQuery
     * @param product 产品配置参数
     */
    private void buildPointStatusSql(UsageShadowQuery query, DeviceUsageHisProperties.AttributesIndex product) {
        Map<String, String> pointState = product.getPointStatus();
        String coordinate = pointState.get("coordinate");
        String status = pointState.get("status");
        String sql2 = IDF_PREFIX + coordinate + " as coordinate, " + CAST_PREFIX + IDF_PREFIX + status + CAST_SUFFIX + " as status";
        query.setSql(sql2);
        String where = IDF_PREFIX + coordinate + IS_NOT_NULL + " and " + IDF_PREFIX + status + IS_NOT_NULL;
        query.setWhere(where);
        query.setWorkState(product.getWorkState());
    }

    /**
     * 时间数据转换
     * @param timeQuery UsageTimeQuery
     * @param usageData List<UsageKeyValue>
     * @return List<KeyValue<String, Long>>
     */
    private List<KeyValue<String, Long>> timeDataTransfer(UsageTimeQuery timeQuery, List<UsageKeyValue> usageData) {
        List<KeyValue<String, Long>> list = new ArrayList<>();

        if (CollectionUtils.isEmpty(usageData)) {
            return list;
        }

        // 天场景
        if (timeQuery.getQueryDateType().equals(QueryDateTypeEnum.DATE.getType())) {
            final Map<String, Long> mapUsage = usageData.stream()
                    .collect(Collectors.toMap(
                            a -> a.getDataKey().substring(0, 10),
                            UsageKeyValue::getDataValue,
                            (oldValue, newValue) -> newValue
                    ));

            timeQuery.getDayBetweenList().forEach(day -> {
                final Long value = mapUsage.get(day);
                list.add(KeyValue.just(day, Objects.isNull(value) ? 0L : value));
            });

            return list;
        }

        // 周和月场景
        usageData.forEach(usageKeyValue ->
                list.add(KeyValue.just(usageKeyValue.getDataKey(), usageKeyValue.getDataValue()))
        );

        return list;
    }

}
