package com.chervon.iot.app.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.chervon.iot.app.domain.dataobject.DeviceAlarm;
import com.chervon.iot.app.domain.dto.device.DeviceAlarmDto;

/**
 * 设备警报服务
 * <AUTHOR>
 * @since 2024-02-26 10:19
 **/
public interface DeviceAlarmService extends IService<DeviceAlarm> {

    /**
     * 设备警报设置详情
     * @param deviceId 设备id
     * @param userId 用户id
     * @return DeviceAlarmDto详情
     */
    DeviceAlarmDto detail(String deviceId,Long userId);

    /**
     * 创建设备警报设置信息
     * @param requestDto 请求对象
     * @return Boolean
     */
    Boolean create(DeviceAlarmDto requestDto);

    /**
     * 编辑设备警报设置信息
     * @param requestDto
     * @return Boolean
     */
    Boolean edit(DeviceAlarmDto requestDto);
}
