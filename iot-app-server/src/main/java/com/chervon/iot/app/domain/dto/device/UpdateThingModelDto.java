package com.chervon.iot.app.domain.dto.device;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;


/**
 * 变更设备物模型
 *
 * <AUTHOR>
 * @date 2024年9月11日
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class UpdateThingModelDto implements Serializable {
   private static final long serialVersionUID = 1L;

   @ApiModelProperty("设备id")
   private String deviceId;

   @ApiModelProperty("物模型属性、事件、服务唯一标识")
   private String identifier;

   @ApiModelProperty("设备影子值-单参(属性类是单参)")
   private Object value;
   /**
    * 自定义topic：空表示使用默认topic
    */
   @ApiModelProperty("自定义topic")
   private String customTopic;
}
