package com.chervon.iot.app.service.impl;

import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.chervon.common.core.constant.CommonConstant;
import com.chervon.common.core.exception.base.BaseErrorCode;
import com.chervon.common.core.exception.base.BaseException;
import com.chervon.common.core.utils.ConvertUtil;
import com.chervon.common.core.utils.StringUtils;
import com.chervon.common.redis.constant.RedisConstant;
import com.chervon.common.redis.utils.RedisUtils;
import com.chervon.iot.app.api.enums.BusinessTypeEnum;
import com.chervon.iot.app.api.exception.AppErrorCode;
import com.chervon.iot.app.config.AppProperties;
import com.chervon.iot.app.config.ExceptionMessageUtil;
import com.chervon.iot.app.config.IotAppCommonConstant;
import com.chervon.iot.app.config.MultiLanguageUtil;
import com.chervon.iot.app.domain.consts.ConstString;
import com.chervon.iot.app.domain.dataobject.AppUserDevice;
import com.chervon.iot.app.domain.dto.device.*;
import com.chervon.iot.app.domain.enums.ProductTypeEnum;
import com.chervon.iot.app.domain.enums.ShareTypeEnum;
import com.chervon.iot.app.domain.enums.ShortCutShowEnum;
import com.chervon.iot.app.domain.vo.device.*;
import com.chervon.iot.app.mapper.AppUserDeviceMapper;
import com.chervon.iot.app.service.*;
import com.chervon.iot.app.util.EmailUtil;
import com.chervon.iot.middle.api.dto.IotThingQueryDto;
import com.chervon.iot.middle.api.dto.device.IotPublishDto;
import com.chervon.iot.middle.api.service.RemoteAppService;
import com.chervon.operation.api.RemoteAppPartsService;
import com.chervon.operation.api.RemoteOperationCacheService;
import com.chervon.operation.api.vo.cache.ProductCache;
import com.chervon.technology.api.*;
import com.chervon.technology.api.dto.DeviceEditDto;
import com.chervon.technology.api.dto.LastRnPackageDto;
import com.chervon.technology.api.dto.ProductReleaseEnum;
import com.chervon.technology.api.enums.DeviceOnlineStatusEnum;
import com.chervon.technology.api.enums.DeviceStatusEnum;
import com.chervon.technology.api.enums.ShareAuthorityTypeEnum;
import com.chervon.technology.api.exception.TechnologyErrorCode;
import com.chervon.technology.api.vo.*;
import com.chervon.usercenter.api.service.UserQueryService;
import com.chervon.usercenter.api.vo.UserVo;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.time.Duration;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2022-07-13 16:57
 **/
@Slf4j
@Service
public class AppDeviceServiceImpl extends ServiceImpl<AppUserDeviceMapper, AppUserDevice> implements AppDeviceService {
    @Resource
    private DeviceInfoService deviceInfoService;
    @Resource
    private UserSettingService userSettingService;
    @Resource
    private DeviceShareService deviceShareService;

    @DubboReference
    private RemoteDeviceCodeService remoteDeviceCodeService;
    @DubboReference
    private RemoteDeviceManageService remoteDeviceManageService;
    @DubboReference
    private RemoteProductService remoteProductService;
    @DubboReference
    private RemoteProductRnService remoteProductRnService;
    @DubboReference
    private RemoteComponentService remoteComponentService;
    @DubboReference
    private UserQueryService userQueryService;
    @Autowired
    private CommonService commonService;

    @DubboReference
    private RemoteDebugDeviceService remoteDebugDeviceService;

    @DubboReference
    private RemoteOperationCacheService remoteOperationCacheService;

    @Autowired
    private AppProperties appProperties;

    @DubboReference
    private com.chervon.iot.middle.api.service.RemoteDeviceShadowService remoteDeviceShadowService;

    @DubboReference
    private RemoteAppPartsService remoteAppPartsService;

    @DubboReference
    private RemoteAppService remoteAppService;

    @Autowired
    private DevicePartsMaintenanceService devicePartsMaintenanceService;

    @Autowired
    private DeviceInfoAsyncService deviceInfoAsyncService;

    private final String EN_LANGUAGE = "en";

    @Override
    public boolean saveOrUpdateBatch(Collection<AppUserDevice> entityList) {
        return super.saveOrUpdateBatch(entityList);
    }

    @Value("${sf.direction}")
    private String direction;

    /**
     * 获取设备昵称
     *
     * @param userId         用户ID
     * @param commodityModel 商品型号Model#
     * @return 设备昵称
     */
    private String getNickName(Long userId, String commodityModel) {
        // Redisson锁控制相同 UserId_Model 则不允许10秒内重复绑定
        RedissonClient client = RedisUtils.getClient();
        RLock lock = client.getLock(userId + "_" + commodityModel);
        if (!lock.isLocked()) {
            lock.lock(CommonConstant.TEN, TimeUnit.SECONDS);
            List<AppUserDevice> data = Optional.ofNullable(this.getBaseMapper().selectMaxOrder(userId, commodityModel)).orElse(new ArrayList<>());
            List<String> existNickNames = data.stream().map(AppUserDevice::getDeviceNickName).filter(Objects::nonNull).collect(Collectors.toList());
            String nickName;
            if (!existNickNames.contains(commodityModel)) {
                nickName = commodityModel;
            } else {
                existNickNames.removeIf(s->s.equals(commodityModel));
                if (existNickNames.isEmpty()) {
                    nickName = commodityModel + "(2)";
                } else {
                    Integer count = existNickNames.stream()
                            .map(e -> Integer.parseInt(e.split("\\(")[1].split("\\)")[0])).max(Integer::compareTo).orElse(0);
                    nickName = commodityModel + "(" + (count + 1) + ")";
                }
            }
            lock.unlock();
            return nickName;
        } else {
            throw ExceptionMessageUtil.getException(AppErrorCode.APP_DEVICE_BIND_LOCKED);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public AddUserDeviceBindVo bindByDeviceId(AppUserDeviceIdDto appUserDeviceIdDto) {
        // 1.RPC分别判断device_code,device,product三张表数据
        DeviceBindBo deviceBindBo = remoteDeviceManageService.bindByDeviceId(appUserDeviceIdDto.getDeviceId(), 1);
        if (deviceBindBo.getResult() == 1) {
            throw ExceptionMessageUtil.getException(AppErrorCode.APP_DEVICE_ALREADY_BOUND_OTHER_APP);
        }
        if (deviceBindBo.getResult() == 3) {
            throw ExceptionMessageUtil.getException(AppErrorCode.APP_DEVICE_DEACTIVATED);
        }
        AppUserDevice appUserDevice = ConvertUtil.convert(appUserDeviceIdDto, AppUserDevice.class);
        AddUserDeviceBindVo res = bind(appUserDevice, deviceBindBo, appUserDeviceIdDto.isDeviceShare());
        final Long thisBindUserId = ((UserVo) RedisUtils.getCacheObject(StpUtil.getTokenValue())).getId();
        afterBindIot(appUserDeviceIdDto.getDeviceId(), deviceBindBo.getProductId(),thisBindUserId);
        return res;
    }

    /**
     * IoT设备绑定完成后续操作
     */
    private void afterBindIot(String deviceId, Long productId,Long thisBindUserId) {
        // 部分wifi设备只能有一个使用用户
        if (appProperties != null && !CollectionUtils.isEmpty(appProperties.getWifiKickProductSn())
                && appProperties.getWifiKickProductSn().stream().anyMatch(e -> StringUtils.containsIgnoreCase(deviceId, e.trim()))) {
            // 查询当前设备已经绑定的用户

            List<AppUserDevice> appUserDevices = this.list(new LambdaQueryWrapper<AppUserDevice>()
                    .eq(AppUserDevice::getDeviceId, deviceId)
                    .ne(AppUserDevice::getUserId, thisBindUserId));
            appUserDevices.forEach(e -> {
                this.unbind(e.getDeviceId(), e.getUserId(), true);
                //推送互踢消息给app
                deviceKickNotice(e.getDeviceId(), e.getUserId());
            });
        }
        // 绑定设备后初始化设备配件的初始维保信息（72002）
        devicePartsMaintenanceService.initPartsMaintenance(productId, Collections.singletonList(deviceId));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public AddUserDeviceBindVo bindBySn(AppUserDeviceSnDto appUserDeviceSnDto) {
        //转换SN
        appUserDeviceSnDto.setSn(checkAndConvertSn(appUserDeviceSnDto.getSn()));
        // 1.RPC分别判断device_code,device,product三张表数据
        DeviceBindBo deviceBindBo = remoteDeviceManageService.bindBySn(appUserDeviceSnDto.getSn(), 1);
        if (deviceBindBo.getResult() == 1) {
            throw ExceptionMessageUtil.getException(AppErrorCode.APP_DEVICE_ALREADY_BOUND_OTHER_APP);
        }
        AppUserDevice appUserDevice = ConvertUtil.convert(appUserDeviceSnDto, AppUserDevice.class);
        appUserDevice.setDeviceId(deviceBindBo.getDeviceId());
        return bind(appUserDevice, deviceBindBo, false);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void bindFromSf(AppUserDevice appUserDevice, DeviceBindBo deviceBindBo, boolean isIotDevice) {
        bind(appUserDevice, deviceBindBo, false);
        if(isIotDevice) {
            afterBindIot(appUserDevice.getDeviceId(), deviceBindBo.getProductId(),appUserDevice.getUserId());
        }
    }

    /**
     * 校验及转换
     * @param sn
     * @return
     *
     */
    private String checkAndConvertSn(String sn) {
        if(StringUtils.isEmpty(sn)){
            throw ExceptionMessageUtil.getException(TechnologyErrorCode.TECHNOLOGY_DEVICE_CODE_SN_ERROR,sn);
        }
        //去空格，转大写
        sn=sn.trim().toUpperCase();
        if (!sn.matches(IotAppCommonConstant.DEVICE_CODE_CHECK) &&
                !sn.matches(IotAppCommonConstant.NO_IOT_DEVICE_CODE_CHECK)) {
            throw ExceptionMessageUtil.getException(TechnologyErrorCode.TECHNOLOGY_DEVICE_CODE_SN_ERROR, sn);
        }
        return sn;
    }

    /**
     * 用户绑定设备相关业务
     *
     * @param appUserDevice 用户以及设备信息
     * @param deviceBindBo 设备ID以及产品相关信息
     */
    private AddUserDeviceBindVo bind(AppUserDevice appUserDevice, DeviceBindBo deviceBindBo, boolean deviceShare) {
        boolean queryDeviceIsRegistered = (appUserDevice.getUserId() == null);
        UserVo userVo = getUserVo(appUserDevice.getUserId());
        if(userVo == null){
            throw ExceptionMessageUtil.getException(AppErrorCode.APP_GET_USER_FAIL);
        }
        Long userId = userVo.getId();
        appUserDevice.setUserId(userId);
        String productType = deviceBindBo.getProductType();
        if(deviceShare) {
            appUserDevice.setShareType(ShareTypeEnum.SUB.getValue());
        } else {
            String sn = deviceBindBo.getSn();
            final boolean paired = isPaired(sn, productType, appUserDevice.getMac(), deviceBindBo.getNetworkModes());
            ProductRpcVo productRpcVo = remoteProductService.getAppProductVoByDeviceId(appUserDevice.getDeviceId());
            Boolean isSharingSupported = null;
            Integer shareAuthorityType = null;
            if (productRpcVo != null ) {
                isSharingSupported = productRpcVo.getIsSharingSupported();
                shareAuthorityType = productRpcVo.getShareAuthorityType();
            }
            deviceShareService.setUserDeviceShareType(appUserDevice,paired,isSharingSupported);
            //查看是否已有主用户绑定
            masterShareCheck(appUserDevice.getDeviceId(),userId,shareAuthorityType,appUserDevice.getShareType());
        }

        //设备昵称
        String nickName = getNickName(userId, deviceBindBo.getCommodityModel());
        RedissonClient client = RedisUtils.getClient();
        RLock lock = client.getLock("device_bind_"+userId + "_" + appUserDevice.getDeviceId());
        boolean flag = false;
        try {
            flag = lock.tryLock(2, 5, TimeUnit.SECONDS);
            if (flag) {
                // 查看是否已绑定
                AppUserDevice existsOne = this.getOne(new LambdaQueryWrapper<AppUserDevice>()
                        .select(AppUserDevice::getId, AppUserDevice::getMac)
                        .eq(AppUserDevice::getDeviceId, appUserDevice.getDeviceId())
                        .eq(AppUserDevice::getUserId, userId));
                if(existsOne != null) {
                    return bindOfPairDevice(existsOne,appUserDevice,deviceBindBo);
                }
                appUserDevice.setDeviceNickName(nickName);
                saveSortAppUserDevice(userId, appUserDevice, deviceBindBo);
            } else {
                log.warn("device bind lock get fail:{},{}",userId,appUserDevice.getDeviceId());
            }
        }catch (InterruptedException e){
            throw ExceptionMessageUtil.getException(AppErrorCode.APP_USER_DEVICE_ALREADY_BOUND, userId, appUserDevice.getDeviceId());
        }finally {
            if (flag && lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
        // Async 回调更新device表昵称,激活用户,激活时间字段
        remoteDeviceManageService.callBackAfterEditDevice(nickName, deviceBindBo.getDeviceId(), userId);

        // 绑定后设置设备绑定关系
        remoteDeviceManageService.setCurrentBusinessTypeAfterBind(deviceBindBo.getDeviceId(), userId, null, 1);
        AddUserDeviceBindVo result = new AddUserDeviceBindVo();
        if(queryDeviceIsRegistered) {
            result.setIsDeviceInfoRegistered(deviceInfoService.deviceIsRegistered(deviceBindBo.getDeviceId()));
        }
        result.setDeviceId(deviceBindBo.getDeviceId());
        return result;
    }

    private UserVo getUserVo(Long userId){
        UserVo userVo = null;
        if(userId == null) {
            //app绑定设备参数没有userId
            userVo = RedisUtils.getCacheObject(StpUtil.getTokenValue());
        } else {
            String token = StpUtil.getTokenValueByLoginId(userId);
            if(StringUtils.isNotEmpty(token)) {
                userVo = RedisUtils.getCacheObject(token);
            }
            if(userVo == null) {
                userVo = userQueryService.getUserInfo(userId);
            }
        }
        return userVo;
    }
    /**
     * 排序保存(仅当Redis中数据失准时调用)
     *
     * @param userId         用户id
     * @param appUserDevice  添加的设备
     * @param isNotIotDevice 是否非IOT设备
     */
    public void saveSortAppUserDeviceOnlyByMybatis(Long userId, AppUserDevice appUserDevice, Boolean isNotIotDevice) {
        // 根据当前用户获得最小排序的iot设备
        LambdaQueryWrapper<AppUserDevice> appUserDeviceWrapper = new LambdaQueryWrapper<AppUserDevice>()
                .eq(AppUserDevice::getUserId, userId)
                .orderByAsc(AppUserDevice::getSort)
                .select(AppUserDevice::getDeviceId);
        List<AppUserDevice> appUserDeviceList = this.list(appUserDeviceWrapper);
        List<String> deviceIds = appUserDeviceList.stream().map(AppUserDevice::getDeviceId).collect(Collectors.toList());
        Integer minIotDeviceSort = remoteProductService.getMinIotDeviceSort(deviceIds);
        if (isNotIotDevice) {
            //添加非iot设备
            if (null != minIotDeviceSort) {
                //数据库中有iot
                AppUserNotIotDeviceDto appUserNotIotDeviceDto = new AppUserNotIotDeviceDto();
                appUserNotIotDeviceDto.setUserId(userId);
                appUserNotIotDeviceDto.setSort(minIotDeviceSort);
                //非iot集合小于iot最小排序号数据
                List<AppUserSortDeviceVo> appUserNotIotDeviceList = this.getBaseMapper().appUserNotIotDeviceList(appUserNotIotDeviceDto);
                if (!CollectionUtils.isEmpty(appUserNotIotDeviceList)) {
                    //数据库中非iot集合!=nulL 添加新的用户绑定设备数据
                    List<Long> ids = appUserNotIotDeviceList.stream().map(AppUserSortDeviceVo::getId)
                            .collect(Collectors.toList());
                    AppUserSortDeviceVo appUserNotIotDeviceVo = ConvertUtil.convert(appUserDevice, AppUserSortDeviceVo.class);
                    appUserNotIotDeviceVo.setIfNewAdd(1);
                    appUserNotIotDeviceList.add(appUserNotIotDeviceVo);
                    AppUserOtherDeviceDto appUserOtherDeviceDto = new AppUserOtherDeviceDto();
                    appUserOtherDeviceDto.setUserId(userId);
                    //List<AppUserSortDeviceVo> vocalist = this.getBaseMapper().appUserNotIotDeviceList(appUserNotIotDeviceDto);
//                    List<Long> ids = vocalist.stream().map(AppUserSortDeviceVo::getId)
//                            .collect(Collectors.toList());
                    appUserOtherDeviceDto.setIds(ids);
                    //数据库其他用户绑定设备集合（除了非iot集合正序）非iot集合 添加  查其他集合正序 not in（非iot集合）
                    List<AppUserSortDeviceVo> appUserOtherDeviceList = this.getBaseMapper().appUserOtherDeviceList(appUserOtherDeviceDto);
                    if (!CollectionUtils.isEmpty(appUserOtherDeviceList)) {
                        appUserNotIotDeviceList.addAll(appUserOtherDeviceList);
                    }
                    //重新排序
                    reorder(appUserNotIotDeviceList);
                } else {
                    //数据中非iot集合等于null
                    appUserDevice.setSort(minIotDeviceSort - CommonConstant.ONE);
                    this.save(appUserDevice);
                }
            } else {
                //数据库中没有iot
                Integer maxSort = this.getBaseMapper().getMaxSort(userId);
                appUserDevice.setSort(maxSort + CommonConstant.ONE);
                this.save(appUserDevice);
            }
        } else {
            //如果添加的是iot设备 最大排序号+1
            Integer maxSort = this.getBaseMapper().getMaxSort(userId);
            appUserDevice.setSort(maxSort + CommonConstant.ONE);
            this.save(appUserDevice);
        }
    }


    /**
     * 排序保存
     *
     * @param userId        用户id
     * @param appUserDevice 添加的设备
     * @param deviceBindBo  设备绑定Rpc Bo
     */
    public void saveSortAppUserDevice(Long userId, AppUserDevice appUserDevice, DeviceBindBo deviceBindBo) {
        List<DeviceShortInfo> deviceShortInfos = RedisUtils.getCacheList(RedisConstant.USER_BIND_DEVICES + userId);
        if (CollectionUtils.isEmpty(deviceShortInfos)) {
            // 如果Redis数据与MYSQL不同步,则仅使用Mybatis逻辑
            saveSortAppUserDeviceOnlyByMybatis(userId, appUserDevice, deviceBindBo.getProductType().equals(ProductTypeEnum.NOT_IOT_DEVICE.getValue()));
        }
        LambdaQueryWrapper<AppUserDevice> appUserDeviceWrapper = new LambdaQueryWrapper<AppUserDevice>()
                .eq(AppUserDevice::getUserId, userId)
                .orderByDesc(AppUserDevice::getSort);
        List<AppUserDevice> appUserDeviceList = this.list(appUserDeviceWrapper);
        Integer sort = null;
        int index = 0;
        if (CollectionUtils.isEmpty(deviceShortInfos)) {
            sort = 0;
        } else {
            if (Objects.equals(deviceBindBo.getProductType(), ProductTypeEnum.NOT_IOT_DEVICE.getValue())) {
                // 如果是非IOT设备插入到 最后一个IOT设备(sort=x) 后面 也就是设置sort值为x,大于x的都+1
                for (int i = deviceShortInfos.size() - 1; i >= 0; i--) {
                    if (!deviceShortInfos.get(i).getProductType().equals(ProductTypeEnum.NOT_IOT_DEVICE.getValue())) {
                        sort = deviceShortInfos.get(i).getSort();
                        index = i;
                        break;
                    }
                }
                if (null == sort) {
                    // 如果没有IOT设备则sort值为最小值-1
                    sort = deviceShortInfos.get(deviceShortInfos.size() - 1).getSort() - 1;
                } else {
                    // 如果有IOT设备则 设置sort值为x,大于x的都+1
                    sort = deviceShortInfos.get(index).getSort();
                    // 大于x的都+1
                    for (int i = 0; i <= index; i++) {
                        deviceShortInfos.get(i).setSort(deviceShortInfos.get(i).getSort() + 1);
                        appUserDeviceList.get(i).setSort(appUserDeviceList.get(i).getSort() + 1);
                    }
                }
                // 如果执行
            } else {
                // 如果是IOT设备插入到 第一个IOT设备(sort=x) 前面 也就是设置sort值为x+1, 大于x+1的都额外+1
                for (DeviceShortInfo deviceShortInfo : deviceShortInfos) {
                    if (!deviceShortInfo.getProductType().equals(ProductTypeEnum.NOT_IOT_DEVICE.getValue())) {
                        sort = deviceShortInfo.getSort();
                        break;
                    }
                    index++;
                }
                if (null == sort) {
                    // 如果没有IOT设备则sort值为最大值+1
                    sort = deviceShortInfos.get(0).getSort() + 1;
                } else {
                    // 如果有IOT设备则 设置sort值为x+1, 大于x+1的都额外+1
                    sort = deviceShortInfos.get(index).getSort() + 1;
                    // 大于x+1的都+1
                    for (int i = 0; i < index; i++) {
                        deviceShortInfos.get(i).setSort(deviceShortInfos.get(i).getSort() + 1);
                        appUserDeviceList.get(i).setSort(appUserDeviceList.get(i).getSort() + 1);
                    }
                }
            }
        }
        appUserDevice.setSort(sort);
        //如果数据库已存在该用户设备关系，则不添加
        if(!appUserDeviceList.stream().filter(x->Objects.equals(appUserDevice.getDeviceId(),x.getDeviceId())&&Objects.equals(appUserDevice.getUserId(),x.getUserId())).findAny().isPresent()){
            appUserDeviceList.add(appUserDevice);
        }
        // 更新数据库
        this.saveOrUpdateBatch(appUserDeviceList);
        // 更新Redis
        DeviceShortInfo deviceShortInfo = ConvertUtil.convert(appUserDevice, DeviceShortInfo.class);
        List<DeviceListInfoRpcVo> deviceRpcVoList = remoteDeviceManageService.listInfoByDeviceId(Collections.singletonList(appUserDevice.getDeviceId()));
        DeviceListInfoRpcVo deviceRpcVo = deviceRpcVoList.get(0);
        deviceShortInfo.setProductId(deviceBindBo.getProductId());
        deviceShortInfo.setSort(sort);
        deviceShortInfo.setProductType(deviceBindBo.getProductType());
        deviceShortInfo.setNickName(appUserDevice.getDeviceNickName());
        deviceShortInfo.setSn(deviceBindBo.getSn());
        deviceShortInfo.setCommodityModel(deviceRpcVo.getCommodityModel());
        deviceShortInfo.setModel(deviceRpcVo.getModel());
        deviceShortInfo.setCategoryId(deviceRpcVo.getCategoryId());
        deviceShortInfo.setCategoryName(deviceRpcVo.getCategoryName());
        deviceShortInfos.add(deviceShortInfo);
        List<DeviceShortInfo> sortedDeviceShortInfoList = deviceShortInfos.stream().sorted(
                (a, b) -> Integer.compare(b.getSort(), a.getSort())
        ).collect(Collectors.toList());
        RedisUtils.deleteObject(RedisConstant.USER_BIND_DEVICES + userId);
        RedisUtils.setCacheList(RedisConstant.USER_BIND_DEVICES + userId, sortedDeviceShortInfoList);
    }

    /**
     * 重新排序
     *
     * @param list App用户设备排序Vo列表
     */
    public void reorder(List<AppUserSortDeviceVo> list) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        final List<Long> ids = list.stream().map(a -> a.getId()).collect(Collectors.toList());
        final List<AppUserDevice> appUserDevices = this.listByIds(ids);
        Map<Long, AppUserDevice> appUserDevicesMap = appUserDevices.stream().collect(Collectors.toMap(AppUserDevice::getId, Function.identity()));
        List<AppUserDevice> updateList = new ArrayList<>();
        List<AppUserDevice> saveList = new ArrayList<>();
        for (int i = 0; i < list.size(); i++) {
            AppUserSortDeviceVo appUserSortDeviceVo = list.get(i);
            if (appUserSortDeviceVo.getIfNewAdd() == null) {
                AppUserDevice appUserDevice = appUserDevicesMap.get(appUserSortDeviceVo.getId());
                appUserDevice.setSort(i);
                updateList.add(appUserDevice);
            } else {
                //新增绑定设备
                AppUserDevice appUserDevice = ConvertUtil.convert(appUserSortDeviceVo, AppUserDevice.class);
                appUserDevice.setSort(i);
                saveList.add(appUserDevice);
            }
        }
        if (!CollectionUtils.isEmpty(updateList)) {
            updateBatchById(updateList);
        }
        if (!CollectionUtils.isEmpty(saveList)) {
            saveBatch(saveList);
        }
    }

    private List<AppUserDevice> listByUserIdOrderBySort(Long userId) {
        LambdaQueryWrapper<AppUserDevice> appUserDeviceWrapper = new LambdaQueryWrapper<AppUserDevice>()
                .eq(AppUserDevice::getUserId, userId)
                .orderByDesc(AppUserDevice::getSort);
        // 获取AppDeviceVo列表，包括用户Id，设备ID，MAC，排序
        return this.list(appUserDeviceWrapper);
    }

    @Override
    public boolean isPaired(AppDeviceVo appDeviceVo) {
        return isPaired(appDeviceVo.getSn(), appDeviceVo.getProductType(),
                appDeviceVo.getMac(), appDeviceVo.getCommunicateMode());
    }

    private boolean isPaired(String sn,String productType,String mac,String communicateMode) {
        String snCode = getSnCodeFromSnNoExcept(sn);
        //Z6设备显示在设备列表
        if(com.chervon.iot.app.common.CommonConstant.DEVICE_CODE_Z6.contains(snCode)) {
            return true;
        }
        //不是（直连设备|网关设备|网关子设备）显示在设备列表
        if(!ProductTypeEnum.DIRECT_CONNECTED_DEVICE.getValue().equals(productType)
                && !ProductTypeEnum.GATEWAY_DEVICE.getValue().equals(productType)
                && !ProductTypeEnum.GATEWAY_SUB_DEVICE.getValue().equals(productType)) {
            return true;
        }
        //mac地址存在的设备，显示设备列表
        if(StringUtils.isNotEmpty(mac)) {
            return true;
        }
        //网关子设备非蓝牙类型，显示设备列表
        return ProductTypeEnum.GATEWAY_SUB_DEVICE.getValue().equals(productType)
                && !"BLE".equalsIgnoreCase(communicateMode);
    }
    @Override
    public List<AppDeviceVo> listBoundDevices(String lang, String appVersion) {
        UserVo userVo = RedisUtils.getCacheObject(StpUtil.getTokenValue());
        List<AppUserDevice> appUserDevices = listByUserIdOrderBySort(userVo.getId());
        // 获取AppDeviceVo列表，包括用户Id，设备ID，MAC，排序
        List<AppDeviceVo> listAppDeviceVo = new ArrayList<>();
        // 如果appUserDevices为空，说明当前用户没有绑定任何设备，返回空列表
        if (null == appUserDevices || appUserDevices.size() == 0) {
            RedisUtils.deleteObject(RedisConstant.USER_BIND_DEVICES + userVo.getId());
            return listAppDeviceVo;
        }
        List<String> deviceIds = appUserDevices.stream().map(AppUserDevice::getDeviceId).collect(Collectors.toList());
        Set<String> crmDevices = Sets.newHashSet();
        for (AppUserDevice appUserDevice : appUserDevices) {
            //1.1.0以下的APP版本，不展示来自CRM的设备
            boolean na = IotAppCommonConstant.NA.equals(direction) && appVersion.compareToIgnoreCase("1.1.0") < 0;
            boolean eu = IotAppCommonConstant.EU.equals(direction) && appVersion.compareToIgnoreCase("1.1.0") <= 0;
            if((na || eu) && appUserDevice.getSourceCode()!=null && appUserDevice.getSourceCode()==1) {
                crmDevices.add(appUserDevice.getDeviceId());
            }
            AppDeviceVo appDeviceVo = ConvertUtil.convert(appUserDevice, AppDeviceVo.class);
            appDeviceVo.setBindTime(appUserDevice.getCreateTime());
            // 获取APP用户自己设置的昵称
            if (null != appUserDevice.getDeviceNickName()) {
                appDeviceVo.setNickName(appUserDevice.getDeviceNickName());
            }
            //72002设备默认显示快捷功能
            if (StringUtils.contains(appUserDevice.getDeviceId(), ConstString.PRODUCT_SN_72002)) {
                setShortCutValues(appUserDevice, appDeviceVo);
            }
            listAppDeviceVo.add(appDeviceVo);
        }
        // 获取DeviceRpcVo列表
        List<DeviceListInfoRpcVo> deviceRpcVoList = remoteDeviceManageService.listInfoByDeviceId(deviceIds);
        Map<String, DeviceListInfoRpcVo> deviceRpcVoMap = deviceRpcVoList.stream().collect(Collectors.toMap(DeviceListInfoRpcVo::getDeviceId, Function.identity(), (k1, k2) -> k2));
        List<AppDeviceVo> listAfterFilter = new ArrayList<>();
        listAppDeviceVo.forEach(m -> {
            DeviceListInfoRpcVo m2 = deviceRpcVoMap.get(m.getDeviceId());
            if (m2 != null) {
                m.setSn(m2.getSn());
                m.setDeviceName(m2.getDeviceName());
                m.setIsOnline(m2.getIsOnline() == DeviceOnlineStatusEnum.ONLINE ? 1 : 0);
                m.setDeviceIcon(m2.getDeviceIcon());
                m.setProductId(m2.getProductId());
                m.setProductType(m2.getProductType());
                m.setCommodityModel(m2.getCommodityModel());
                m.setCommunicateMode(m2.getCommunicateMode());
                m.setModel(m2.getModel());
                m.setCategoryId(m2.getCategoryId());
                m.setCategoryName(m2.getCategoryName());

                if(crmDevices.contains(m.getDeviceId())) {
                    if(isPaired(m)) {
                        listAfterFilter.add(m);
                    }
                } else {
                    listAfterFilter.add(m);
                }
            }
        });
        // 设置语言环境
        // userSettingService.setUserLanguage(userVo.getId(), lang);
        // 修复bug 2699 开始
        //target.sort(Comparator.comparing(AppDeviceVo::getBindTime, Comparator.reverseOrder()));
        // 修复bug 2699 结束
        // 把设备绑定信息存储在redis里面,因为里面含有产品信息，所有在每次获取列表时添加
        // 更新redis中的数据,添加异步锁
        setDeviceBindCache(userVo.getId(), listAfterFilter);
        return listAfterFilter;
    }

    private void setShortCutValues(AppUserDevice appUserDevice, AppDeviceVo appDeviceVo) {
        ShortCutVo shortCutVo = new ShortCutVo();
        //手动加热开关功能
        shortCutVo.setShadowId(ConstString.SHADOW_ID_HEATING_72002);
        shortCutVo.setShowShortCut(ShortCutShowEnum.SHOW.getValue());
        shortCutVo.setIconName(ConstString.HEATING_SWITCH_NAME);
        try {
            IotThingQueryDto iotThingQueryDto = new IotThingQueryDto();
            iotThingQueryDto.setDeviceId(appUserDevice.getDeviceId());
            iotThingQueryDto.setThingIds(Arrays.asList(shortCutVo.getShadowId()));
            final Map<String, Object> shadowStatusMap = remoteAppService.reportedData(iotThingQueryDto);
            shortCutVo.setShadowStatus(shadowStatusMap == null ? null : shadowStatusMap.get(shortCutVo.getShadowId()));
        } catch (Exception e) {
            log.error("read shortcut shadow status error,reportedData:{}", e);
        }
        appDeviceVo.setShortCutVos(Arrays.asList(shortCutVo));
    }

    void setDeviceBindCache(Long userId, List<AppDeviceVo> target) {
        RedissonClient client = RedisUtils.getClient();
        RLock lock = client.getLock("bind_device_" + userId);
        if (!lock.isLocked()) {
            lock.lock(CommonConstant.TWO, TimeUnit.SECONDS);
            RedisUtils.deleteObject(RedisConstant.USER_BIND_DEVICES + userId);
            RedisUtils.setCacheList(RedisConstant.USER_BIND_DEVICES + userId,
                    ConvertUtil.convertList(target, DeviceShortInfo.class));
            lock.unlock();
        }
        // 忽略产品信息在2s内的变化
    }

    @Override
    public List<DeviceRpcVo> listBoundDevices() {
        UserVo userVo = RedisUtils.getCacheObject(StpUtil.getTokenValue());
        return listBoundDevices(userVo);
    }
    @Override
    public List<DeviceRpcVo> listBoundDevices(UserVo userVo) {
        LambdaQueryWrapper<AppUserDevice> appUserDeviceWrapper = new LambdaQueryWrapper<AppUserDevice>()
                .eq(AppUserDevice::getUserId, userVo.getId());
        List<AppUserDevice> appUserDevices = this.list(appUserDeviceWrapper);
        if (CollectionUtil.isEmpty(appUserDevices)) {
            return Collections.emptyList();
        }
        List<String> deviceIds = appUserDevices.stream().map(AppUserDevice::getDeviceId).collect(Collectors.toList());
        // 获取DeviceRpcVo列表
        return remoteDeviceManageService.listSimpleByDeviceId(deviceIds);
    }

    @Override
    public AppDeviceVo detail(String deviceId, HttpServletRequest request) {
        // 设置最新bundleName
        UserVo userVo = RedisUtils.getCacheObject(StpUtil.getTokenValue());
        LastRnPackageDto dto = new LastRnPackageDto();
        dto.setAppType(request.getHeader("appType"));
        dto.setAppVersion(request.getHeader("appVersion"));
        // 设置rn适用app类型为ego
        dto.setBusinessType(BusinessTypeEnum.EGO_CONNECT.getType());
        DeviceDetailInfoRpcVo deviceRpcVo = remoteDeviceManageService.getDeviceDetailByDeviceId(deviceId, dto, userVo.getId());
        if (null == deviceRpcVo) {
            throw ExceptionMessageUtil.getException(AppErrorCode.APP_DEVICE_NOT_EXIST, deviceId);
        }
        AppDeviceVo target = ConvertUtil.convert(deviceRpcVo, AppDeviceVo.class);
        target.setStatus(deviceRpcVo.getStatus() ==
                DeviceStatusEnum.NORMAL ? CommonConstant.ONE : CommonConstant.ZERO);
        target.setIsOnline(deviceRpcVo.getIsOnline() ==
                DeviceOnlineStatusEnum.ONLINE ? CommonConstant.ONE : CommonConstant.ZERO);
        //设置离线天数
        setOfflineDays(target,deviceRpcVo);
        // 从设备列表中获取
        List<DeviceShortInfo> devicesInfo = RedisUtils.getCacheList(RedisConstant.USER_BIND_DEVICES + userVo.getId());
        if (!CollectionUtil.isEmpty(devicesInfo)) {
            devicesInfo.forEach(deviceShortInfo -> {
                if (deviceShortInfo.getDeviceId().equals(deviceRpcVo.getDeviceId())) {
                    target.setMac(deviceShortInfo.getMac());
                    target.setSort(deviceShortInfo.getSort());
                    target.setShareType(deviceShortInfo.getShareType());
                    if(target.getShareType() == null) {
                        target.setShareType(ShareTypeEnum.NONE.getValue());
                    }
                    if (null != deviceShortInfo.getNickName()) {
                        target.setNickName(deviceShortInfo.getNickName());
                    }
                }
            });
        }
        target.setInfoStatus(deviceInfoService.deviceIsRegistered(deviceId) ? CommonConstant.ONE : CommonConstant.ZERO);

        ProductCache productCache = remoteOperationCacheService.getProduct(target.getProductId());
        //设备详情返回增加产品属性
        setProductInfoToDeviceDetail(target,productCache);
        return target;
    }

    /**
     * 设备详情接口设置设备离线天数
     * @param target
     * @param deviceDetailInfoRpcVo
     */
    private void setOfflineDays(AppDeviceVo target, DeviceDetailInfoRpcVo deviceDetailInfoRpcVo) {
        Long offlineDays=0L;
        DeviceOnlineStatusEnum deviceOnlineStatusEnum=deviceDetailInfoRpcVo.getIsOnline();
        LocalDateTime lastLogoutTime=deviceDetailInfoRpcVo.getLastLogoutTime();
        //设备状态为离线且最后离线时间有值
        if(Objects.equals(deviceOnlineStatusEnum,DeviceOnlineStatusEnum.OFFLINE)&&Objects.nonNull(lastLogoutTime)){
            offlineDays =Duration.between(lastLogoutTime, LocalDateTime.now()).toDays();
        }
        target.setOfflineDays(offlineDays);
    }

    @Override
    public AppDeviceVo detailBySn(String sn, HttpServletRequest request) {
        //校验及转换sn
        sn=checkAndConvertSn(sn);
        // 设置最新bundleName
        LastRnPackageDto dto = new LastRnPackageDto();
        dto.setAppType(request.getHeader("appType"));
        dto.setAppVersion(request.getHeader("appVersion"));
        // 设置rn适用app类型为ego
        dto.setBusinessType(1);
        DeviceDetailInfoRpcVo deviceRpcVo = remoteDeviceManageService.getDeviceDetailBySn(sn, dto,
                StpUtil.getLoginIdAsLong());

        ProductCache productCache;
        if (Objects.isNull(deviceRpcVo)) {
            //查询产品信息
            String snCode = getSnCodeFromSn(sn);
            ProductRpcVo productRpcVo = remoteProductService.getProductIdBySnCode(snCode);
            if (null == productRpcVo) {
                throw ExceptionMessageUtil.getException(TechnologyErrorCode.TECHNOLOGY_PRODUCT_NOT_EXIST, snCode);
            }
            productCache = remoteOperationCacheService.getProduct(productRpcVo.getId());
            deviceRpcVo = new DeviceDetailInfoRpcVo();
            deviceRpcVo.setProductId(productRpcVo.getId());
            deviceRpcVo.setSn(sn);
            deviceRpcVo.setDeviceIcon(productCache.getUrl());


            if (Objects.isNull(productCache) || Objects.isNull(productCache.getId())) {
                throw ExceptionMessageUtil.getException(TechnologyErrorCode.TECHNOLOGY_PRODUCT_NOT_EXIST, productRpcVo.getId());
            }
            //判断多码关系是否存在
            DeviceCodeRpcVo deviceCodeRpcVo = remoteDeviceCodeService.deviceCodeDetailBySn(sn);
            if (Objects.isNull(deviceCodeRpcVo)) {
                //判断是否是非iot设备
                if (ProductTypeEnum.NOT_IOT_DEVICE.getValue().equals(productCache.getType())) {
                    //生成多码关系，返回device_id
                    deviceRpcVo.setDeviceId(remoteDeviceCodeService.createDeviceCodeBySn(sn, snCode));

                } else {
                    throw ExceptionMessageUtil.getException(TechnologyErrorCode.TECHNOLOGY_DEVICE_CODE_NOT_EXIST, sn);
                }
            } else {
                deviceRpcVo.setDeviceId(deviceCodeRpcVo.getDeviceId());
            }
        }else{
            productCache=remoteOperationCacheService.getProduct(deviceRpcVo.getProductId());
        }
        AppDeviceVo target = ConvertUtil.convert(deviceRpcVo, AppDeviceVo.class);
        target.setStatus(deviceRpcVo.getStatus() ==
                DeviceStatusEnum.NORMAL ? CommonConstant.ONE : CommonConstant.ZERO);
        target.setIsOnline(deviceRpcVo.getIsOnline() ==
                DeviceOnlineStatusEnum.ONLINE ? CommonConstant.ONE : CommonConstant.ZERO);
        // 获取绑定信息
        UserVo userVo = RedisUtils.getCacheObject(StpUtil.getTokenValue());
        LambdaQueryWrapper<AppUserDevice> appUserDeviceWrapper = new LambdaQueryWrapper<AppUserDevice>()
                .eq(AppUserDevice::getUserId, userVo.getId())
                .eq(AppUserDevice::getDeviceId, deviceRpcVo.getDeviceId());
        AppUserDevice appUserDevice = this.getOne(appUserDeviceWrapper);

        if (null != appUserDevice) {
            target.setMac(appUserDevice.getMac());
            target.setSort(appUserDevice.getSort());
            target.setShareType(appUserDevice.getShareType());
            if(target.getShareType() == null) {
                target.setShareType(ShareTypeEnum.NONE.getValue());
            }
            // 设置返回昵称为当前APP用户设置的昵称
            if (null != appUserDevice.getDeviceNickName()) {
                target.setNickName(appUserDevice.getDeviceNickName());
            }
        }
        target.setInfoStatus(deviceInfoService.deviceIsRegistered(deviceRpcVo.getDeviceId()) ? CommonConstant.ONE : CommonConstant.ZERO);
        //设备详情返回增加产品属性
        setProductInfoToDeviceDetail(target,productCache);
        return target;
    }

    /**
     * 设备详情返回增加产品属性
     * @param appDeviceVo
     * @param productCache
     */
    private void setProductInfoToDeviceDetail(AppDeviceVo appDeviceVo, ProductCache productCache) {
        // 设置商品型号
        appDeviceVo.setCommodityModel(productCache.getCommodityModel());
        //设置问卷模板
        appDeviceVo.setQuestionTemplate(productCache.getQuestionTemplate());
        //获取语言环境 设置设备的产品名称
        String language = LocaleContextHolder.getLocale().getLanguage();
        boolean isEn = StringUtils.equals(language, RemoteOperationCacheService.DEFAULT_LANGUAGE);
        if (isEn) {
            appDeviceVo.setProductName(productCache.getDefaultName());
        } else {
            appDeviceVo.setProductName(MultiLanguageUtil.getByLangCode(productCache.getNameLangCode(), language));
        }
    }

    /**
     * 从sn中截取snCode,兼容IOT设备以及非IOT设备
     *
     * @param sn SN
     * @return SnCode
     */
    private String getSnCodeFromSn(String sn) {
        if (sn.matches(IotAppCommonConstant.DEVICE_CODE_CHECK)) {
            // 15位多码
            return sn.substring(1, 5);
        } else if (sn.matches(IotAppCommonConstant.NO_IOT_DEVICE_CODE_CHECK)) {
            // 16位，R开头的非IOT设备多码
            return sn.substring(2, 6);
        } else {
            throw ExceptionMessageUtil.getException(TechnologyErrorCode.TECHNOLOGY_DEVICE_CODE_SN_ERROR, sn);
        }
    }

    private String getSnCodeFromSnNoExcept(String sn) {
        if(StringUtils.isNotEmpty(sn)){
            if (sn.matches(IotAppCommonConstant.DEVICE_CODE_CHECK)) {
                // 15位多码
                return sn.substring(1, 5);
            } else if (sn.matches(IotAppCommonConstant.NO_IOT_DEVICE_CODE_CHECK)) {
                // 16位，R开头的非IOT设备多码
                return sn.substring(2, 6);
            }else {
                return null;
            }
        }
        return null;
    }
    @Override
    public void unbind(AppUserDeviceUnbindDto appUserDeviceUnbindDto) {
        UserVo userVo = RedisUtils.getCacheObject(StpUtil.getTokenValue());
        this.unbind(appUserDeviceUnbindDto.getDeviceId(), userVo.getId(), true);
    }

    @Override
    public void unbind(String deviceId, Long userId, boolean clearShare) {
        //查询用户和设备关系
        List<AppUserDevice> appUserDevices = list(Wrappers.<AppUserDevice>lambdaQuery()
                .eq(AppUserDevice::getUserId, userId)
                .eq(AppUserDevice::getDeviceId, deviceId));
        if (CollectionUtils.isEmpty(appUserDevices)) {
            throw ExceptionMessageUtil.getException(AppErrorCode.APP_USER_DEVICE_NOT_BOUND);
        }
        //解绑用户和设备关系
        this.removeBatchByIds(appUserDevices);
        // 解绑后设置设备绑定关系
        remoteDeviceManageService.setCurrentBusinessTypeAfterUnbind(deviceId);
        // 更新Redis中用户绑定设备列表数据
        List<DeviceShortInfo> deviceShortInfos = RedisUtils.getCacheList(RedisConstant.USER_BIND_DEVICES + userId);
        if (!CollectionUtils.isEmpty(deviceShortInfos)) {
            deviceShortInfos.removeIf(x->x.getDeviceId().equals(deviceId));
            RedisUtils.deleteObject(RedisConstant.USER_BIND_DEVICES + userId);
            RedisUtils.setCacheList(RedisConstant.USER_BIND_DEVICES + userId, deviceShortInfos);
        }

        if(clearShare) {
            deviceShareService.clearShare(appUserDevices.get(0));
        }
    }

    @Override
    public void sort(DeviceSortDto deviceSortDto, String appVersion) {
        List<String> deviceIds = deviceSortDto.getDeviceIds();
        Collections.reverse(deviceIds);
        UserVo userVo = RedisUtils.getCacheObject(StpUtil.getTokenValue());
        LambdaQueryWrapper<AppUserDevice> appUserDeviceWrapper = new LambdaQueryWrapper<AppUserDevice>()
                .eq(AppUserDevice::getUserId, userVo.getId());
        List<AppUserDevice> appUserDeviceList = this.list(appUserDeviceWrapper);
        if (appUserDeviceList.size() != deviceIds.size()) {
            appVersionCheck(appVersion);
        }
        // 重新排序
        int i = 0;
        int j = 0;
        int size = appUserDeviceList.size();

        for (String deviceId : deviceIds) {
            for (AppUserDevice appUserDevice : appUserDeviceList) {
                if (Objects.equals(appUserDevice.getDeviceId(), deviceId)) {
                    appUserDevice.setSort(i);
                    i++;
                    break;
                }
                if (j == size - 1) {
                    throw ExceptionMessageUtil.getException(AppErrorCode.APP_DEVICE_NOT_EXIST, deviceId);
                }
                j++;
            }
            j = 0;
        }
        log.debug("appUserDeviceList: {}", appUserDeviceList);
        this.updateBatchById(appUserDeviceList);
    }

    /**
     * app版本校验提示
     * @param appVersion
     */
    private void appVersionCheck(String appVersion){
        boolean na = IotAppCommonConstant.NA.equals(direction) && appVersion.compareToIgnoreCase("1.1.0") < 0;
        boolean eu = IotAppCommonConstant.EU.equals(direction) && appVersion.compareToIgnoreCase("1.1.0") <= 0;
        if(na || eu) {
            throw ExceptionMessageUtil.getException(AppErrorCode.APP_VERSION_LOW_ERROR);
        } else {
            throw new BaseException(BaseErrorCode.PARAM_ERROR);
        }
    }
    @Override
    public void edit(DeviceEditDto deviceEditDto) {
        UserVo userVo = RedisUtils.getCacheObject(StpUtil.getTokenValue());
        LambdaQueryWrapper<AppUserDevice> wrapper = new LambdaQueryWrapper<AppUserDevice>()
                .eq(AppUserDevice::getDeviceId, deviceEditDto.getDeviceId())
                .eq(AppUserDevice::getUserId, userVo.getId());
        AppUserDevice appUserDevice = this.getOne(wrapper);
        if (null == appUserDevice) {
            throw ExceptionMessageUtil.getException(AppErrorCode.APP_USER_DEVICE_NOT_BOUND, userVo.getId(), deviceEditDto.getDeviceId());
        }
        // 如果EditDto的设备昵称不为空,更新APP端设备昵称
        if (null != deviceEditDto.getDeviceNickname()) {
            appUserDevice.setDeviceNickName(deviceEditDto.getDeviceNickname());
            this.updateById(appUserDevice);
            // 更新redis中的数据
            commonService.setDeviceBindInfo(userVo.getId(), deviceEditDto);
        }
        // 编辑Dto中传递到iot-platform中，修改技术平台的设备昵称,上传图片
        remoteDeviceManageService.editDevice(deviceEditDto);
    }

    @Override
    public void deleteUserDevice(Long userId) {
        log.debug("deleteUserDevice -> delete user bound device, userId:{}", userId);
        LambdaQueryWrapper<AppUserDevice> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(AppUserDevice::getUserId, userId);
        this.remove(wrapper);
    }


    @Override
    public AppDeviceStatusVo getStatus(AppDeviceStatusDto appDeviceStatusDto) {
        if (appDeviceStatusDto == null || StringUtils.isEmpty(appDeviceStatusDto.getDeviceId())) {
            throw ExceptionMessageUtil.getException(TechnologyErrorCode.TECHNOLOGY_DEVICE_CODE_NOT_EXIST, appDeviceStatusDto.getDeviceId());
        }
        AppDeviceStatusVo result = new AppDeviceStatusVo();

        DeviceRpcVo deviceRpcVo = remoteDeviceManageService.deviceDetail(appDeviceStatusDto.getDeviceId());
        result.setDeviceStatus(deviceRpcVo.getStatus() ==
                DeviceStatusEnum.NORMAL ? CommonConstant.ONE : CommonConstant.ZERO);
        result.setIsOnline(deviceRpcVo.getIsOnline() ==
                DeviceOnlineStatusEnum.ONLINE ? CommonConstant.ONE : CommonConstant.ZERO);

        // 默认给APP端返回deviceCodeStatus==1（后期考虑APP端代码也下掉根据deviceCodeStatus加载RN面板的逻辑）
        result.setDeviceCodeStatus(1);

        return result;
    }

    @Override
    public Long getProductIdBySn(String sn) {
        // 根据SN不同位数截取不同SNCODE，查找对应产品类型以及产品ID
        String snCode=getSnCodeFromSn(checkAndConvertSn(sn));
        ProductRpcVo productRpcVo = remoteProductService.getProductIdBySnCode(snCode);
        if (null == productRpcVo) {
            throw ExceptionMessageUtil.getException(TechnologyErrorCode.TECHNOLOGY_PRODUCT_NOT_EXIST, snCode);
        }
        ProductCache productCache = remoteOperationCacheService.getProduct(productRpcVo.getId());
        //下架产品抛异常，解决老版本app问题
        if (Objects.isNull(productCache) || Objects.isNull(productCache.getId())||
                ProductReleaseEnum.OFF_RELEASED.getValue().equals(productCache.getReleaseStatus())) {
            throw ExceptionMessageUtil.getException(TechnologyErrorCode.TECHNOLOGY_PRODUCT_NOT_EXIST, productRpcVo.getId());
        }
        return productRpcVo.getId();
    }

    @Override
    public List<AppDeviceVo> listBoundDebugDevices() {
        List<AppDeviceVo> target = new ArrayList<>();
        UserVo userVo = RedisUtils.getCacheObject(StpUtil.getTokenValue());
        Map<String, LocalDateTime> info = remoteDebugDeviceService.listDebugDeviceIds(userVo.getId());
        if (info.isEmpty()) {
            return new ArrayList<>();
        }
        info.forEach((k, v) -> {
            AppDeviceVo appDeviceVo = new AppDeviceVo();
            appDeviceVo.setDeviceId(k);
            appDeviceVo.setBindTime(v);
            target.add(appDeviceVo);
        });
        // 获取DeviceRpcVo列表
        List<DeviceListInfoRpcVo> deviceRpcVoList = remoteDeviceManageService.listInfoByDeviceId(new ArrayList<>(info.keySet()));
        Map<String, DeviceListInfoRpcVo> deviceRpcVoMap = deviceRpcVoList.stream().collect(Collectors.toMap(DeviceListInfoRpcVo::getDeviceId, Function.identity(), (k1, k2) -> k2));

        target.forEach(m -> {
            DeviceListInfoRpcVo m2 = deviceRpcVoMap.get(m.getDeviceId());
            if (m2 != null) {
                m.setSn(m2.getSn());
                m.setDeviceName(m2.getDeviceName());
                m.setIsOnline(m2.getIsOnline() == DeviceOnlineStatusEnum.ONLINE ? 1 : 0);
                m.setDeviceIcon(m2.getDeviceIcon());
                m.setProductId(m2.getProductId());
                m.setProductType(m2.getProductType());
                m.setCommodityModel(m2.getCommodityModel());
                m.setCommunicateMode(m2.getCommunicateMode());
            }
        });
        return target;
    }
    @Override
    public AppUserDevice masterUserByDeviceId(String deviceId){
        return this.getOne(new LambdaQueryWrapper<AppUserDevice>()
                .select(AppUserDevice::getId, AppUserDevice::getUserId
                        ,AppUserDevice::getShareType,AppUserDevice::getDeviceId)
                .eq(AppUserDevice::getDeviceId, deviceId)
                .eq(AppUserDevice::getShareType, ShareTypeEnum.MASTER.getValue()).last("LIMIT 1"));
    }

    /**
     * 设备配对操作处理
     * @param existsOne 已存在用户设备记录
     * @param appUserDevice 绑定传参记录
     * @param deviceBindBo 设备相关信息
     * @return
     */
    private AddUserDeviceBindVo bindOfPairDevice(AppUserDevice existsOne,
                                                 AppUserDevice appUserDevice, DeviceBindBo deviceBindBo){
        Long userId = appUserDevice.getUserId();
        final String mac = appUserDevice.getMac();
        if(StringUtils.isNotEmpty(existsOne.getMac()) || StringUtils.isEmpty(mac)) {
            throw ExceptionMessageUtil.getException(AppErrorCode.APP_USER_DEVICE_ALREADY_BOUND,
                    userId, appUserDevice.getDeviceId());
        }
        //若已经绑定，只更新mac地址
        AppUserDevice entity = new AppUserDevice();
        entity.setId(existsOne.getId());
        entity.setMac(mac);
        entity.setShareType(appUserDevice.getShareType());
        this.updateById(entity);
        //更新redis设备列表
        List<DeviceShortInfo> devicesInfo = RedisUtils.getCacheList(RedisConstant.USER_BIND_DEVICES + userId);
        List<DeviceListInfoRpcVo> deviceRpcVoList =
                remoteDeviceManageService.listInfoByDeviceId(Collections.singletonList(appUserDevice.getDeviceId()));
        DeviceListInfoRpcVo deviceRpcVo = deviceRpcVoList.get(0);
        if(!CollectionUtils.isEmpty(devicesInfo)) {
            for(DeviceShortInfo deviceShortInfo : devicesInfo) {
                if(deviceShortInfo.getDeviceId().equals(appUserDevice.getDeviceId())) {
                    deviceShortInfo.setMac(mac);
                    deviceShortInfo.setShareType(entity.getShareType());
                    deviceShortInfo.setCommodityModel(deviceRpcVo.getCommodityModel());
                    deviceShortInfo.setModel(deviceRpcVo.getModel());
                    deviceShortInfo.setCategoryId(deviceRpcVo.getCategoryId());
                    deviceShortInfo.setCategoryName(deviceRpcVo.getCategoryName());
                    break;
                }
            }
            RedisUtils.deleteObject(RedisConstant.USER_BIND_DEVICES + userId);
            RedisUtils.setCacheList(RedisConstant.USER_BIND_DEVICES + userId, devicesInfo);
        }
        AddUserDeviceBindVo result = new AddUserDeviceBindVo();
        result.setDeviceId(deviceBindBo.getDeviceId());
        result.setIsDeviceInfoRegistered(deviceInfoService.deviceIsRegistered(deviceBindBo.getDeviceId()));
        return result;
    }

    /**
     * 设备权限最高时，解除主账户配对及设备分享关系
     * @param masterUser
     */
    private void unbindDeviceShare(AppUserDevice masterUser){
        Long masterUserId = masterUser.getUserId();
        //将主账户mac地址清空，并将分享类型修改为不可分享
        AppUserDevice appUserDevice = new AppUserDevice();
        appUserDevice.setId(masterUser.getId());
        appUserDevice.setMac("");
        appUserDevice.setShareType(ShareTypeEnum.NONE.getValue());
        this.updateById(appUserDevice);
        deviceShareService.clearShare(masterUser);
        // 更新Redis中用户绑定设备列表数据
        List<DeviceShortInfo> deviceShortInfos = RedisUtils.getCacheList(RedisConstant.USER_BIND_DEVICES + masterUserId);
        if (!CollectionUtils.isEmpty(deviceShortInfos)) {
            for (DeviceShortInfo deviceShortInfo : deviceShortInfos) {
                //清除主用户数据的配对信息
                if(masterUser.getDeviceId().equals(deviceShortInfo.getDeviceId())){
                    deviceShortInfo.setMac("");
                    deviceShortInfo.setShareType(ShareTypeEnum.NONE.getValue());
                }
            }
            RedisUtils.deleteObject(RedisConstant.USER_BIND_DEVICES + masterUserId);
            RedisUtils.setCacheList(RedisConstant.USER_BIND_DEVICES + masterUserId, deviceShortInfos);
        }
        deviceKickNotice(masterUser.getDeviceId(), masterUserId);
    }

    /**
     * 主账户分享消息
     * @param deviceId 设备id
     * @param userId 用户id
     * @param shareAuthorityType 分享最高权限类型
     */
    private void masterShareCheck(String deviceId, Long userId, Integer shareAuthorityType, Integer shareType) {
        AppUserDevice masterUser = this.masterUserByDeviceId(deviceId);
        if (masterUser != null && !userId.equals(masterUser.getUserId())) {
            //如果为设备优先，需要清空主用户绑定信息，解除子用户绑定
            if (ShareAuthorityTypeEnum.isDevicePower(shareAuthorityType)) {
                //只有新用户是主账户时，才去踢除原主账户绑定关系
                if (ShareTypeEnum.MASTER.getValue() == shareType) {
                    unbindDeviceShare(masterUser);
                }
            } else {
                UserVo userInfo = userQueryService.getUserInfo(masterUser.getUserId());
                String hideEmail = EmailUtil.hideEmail(userInfo.getEmail());
                throw ExceptionMessageUtil.getException(AppErrorCode.APP_SHARE_MASTER_EXIST, hideEmail);
            }
        }
    }

    /**
     * 设备绑定互踢通知消息
     * @param deviceId 设备id
     * @param userId 通知用户id
     */
    private void deviceKickNotice(String deviceId,Long userId){
        try {
            IotPublishDto publish = new IotPublishDto();
            publish.setTopic(com.chervon.iot.app.common.CommonConstant.APP_KICK_TOPIC + userId);
            publish.setPayLoad("kick off:" + System.currentTimeMillis());
            remoteDeviceShadowService.publish(publish);
            log.info("设备绑定互踢发布mqtt消息通知app设备列表刷新，设备：{},踢用户：{}",deviceId, userId);
        } catch (Exception exception) {
            log.error("设备绑定互踢发布mqtt消息通知app设备列表刷新发生异常，设备：{}，踢用户：{},异常信息：{}", deviceId,userId ,exception);
        }
    }
}
