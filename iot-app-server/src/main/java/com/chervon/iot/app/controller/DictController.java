package com.chervon.iot.app.controller;

import com.chervon.common.core.domain.ListInfoReq;
import com.chervon.iot.app.domain.vo.dict.DictVo;
import com.chervon.iot.app.service.DictService;
import io.swagger.annotations.Api;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2022-08-31 17:32
 **/
@RestController
@RequestMapping("/dict")
@Api(tags = "字典相关接口")
public class DictController {

    @Autowired
    private DictService dictService;

    /**
     * 根据字典名称获取字典详情列表
     *
     * @param req 字典名称列表
     * @return 字典详情列表
     */
    @PostMapping("/list/by/dict/name")
    public List<DictVo> listByDictName(@Validated @RequestBody ListInfoReq<String> req) {
        return dictService.listByDictName(LocaleContextHolder.getLocale().getLanguage(), req.getInfo());
    }
}
