package com.chervon.iot.app.service;

import com.alibaba.fastjson.JSON;
import com.chervon.iot.app.domain.dataobject.DeviceInfoSyncFail;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.List;
import static org.junit.jupiter.api.Assertions.fail;
/**
 * <AUTHOR>
 * @date 2025/1/9
 * @description
 */
@SpringBootTest
class DeviceInfoSyncFailServiceIntegrationTest {
    @Autowired
    private DeviceInfoSyncFailService deviceInfoSyncFailService;

    /**
     * 查询没有处理的同步异常记录
     */
    @Test
    void testFindNotDealSyncFail(){
        int limit = 10;
        try {
            List<DeviceInfoSyncFail> notDealSyncFail = deviceInfoSyncFailService.findNotDealSyncFail(limit);
            System.out.println(JSON.toJSONString(notDealSyncFail));
            deviceInfoSyncFailService.dealSyncFailList(notDealSyncFail);
        }catch (Exception e){
            fail(e.getMessage());
        }

    }

    /**
     * 保存同步失败记录
     */
    @Test
    void testSaveSyncFailInfo(){
        String sn = "";
        String sfUserId = "";
        String msg = "";
        Integer dealStatus = 0;
        try {
            deviceInfoSyncFailService.saveSyncFailInfo(sn,sfUserId,msg,dealStatus);
        }catch (Exception e){
            fail(e.getMessage());
        }
    }
}
