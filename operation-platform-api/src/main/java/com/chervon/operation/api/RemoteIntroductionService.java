package com.chervon.operation.api;

import com.chervon.operation.api.dto.IntroductionTypeDto;
import com.chervon.operation.api.vo.IntroductionVo;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2022-09-02 10:18
 **/
public interface RemoteIntroductionService {

    /**
     * 通过类型获取引导页列表
     *
     * @param req  引导页类型
     * @return 引导页Vo列表
     */
    List<IntroductionVo> listIntroductionByType(IntroductionTypeDto req);
}