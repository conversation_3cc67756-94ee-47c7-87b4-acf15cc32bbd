package com.chervon.operation.api;

import com.chervon.operation.api.vo.RemoteSpinnerVo;
import com.chervon.operation.api.vo.RemoteSuggestionVo;
import com.chervon.operation.api.vo.SuggestionVo;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022/11/30 17:50
 */
public interface RemoteSuggestionService {

    /**
     * 根据标题搜索
     *
     * @param title 标题
     * @param lang  语言
     * @return 列表
     */
    List<RemoteSuggestionVo> search(String title, String lang);

    /**
     * 根据建议ID获取处理建议详情
     * @param suggestionId ID
     * @return 处理建议详情
     */
    RemoteSuggestionVo detail(Long suggestionId, String lang);

    /**
     * 获取处理建议下拉框
     * @param lang 多语言
     * @return 下拉列表Vo列表
     */
    List<RemoteSpinnerVo> listSpinner(String lang);

    /**
     * 详情
     *
     * @param suggestionId 处理建议 id
     * @return 详情
     */
    SuggestionVo detail(String lang, Long suggestionId);

    /**
     * 根据ID查询处理建议是否有效
     *
     * @param suggestionId 处理建议ID
     * @return 处理建议数据是否有效
     */
    Boolean check(Long suggestionId);

    /**
     * 根据处理建议查询建议详情
     * @param id
     * @return
     */
    SuggestionVo getBySuggestionId(Long id);

    /**
     * 批量根据处理建议查询详情
     * @param listId
     * @return
     */
    List<SuggestionVo> getSuggestionListByIds(List<Long> listId);

    /**
     * 根据处理建议获取处理内容（指定的所有语言）
     * @param listSuggestionId 处理建议集合列表
     * @param languages 语种列表
     * @return Map<SuggestionId,Map<language,content>>
     */
    Map<Long, Map<String, String>> listContentForLanguages(List<Long> listSuggestionId, List<String> languages);
}
