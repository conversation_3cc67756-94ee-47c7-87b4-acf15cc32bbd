package com.chervon.operation.api;

import com.chervon.common.core.domain.PageResult;
import com.chervon.operation.api.vo.HelpFaqAppVo;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/11/20 21:00
 */
public interface RemoteHelpFaqService {

    /**
     * 分页查询
     *
     * @param searchContent
     * @param pageNum
     * @param pageSize
     * @return
     */
    PageResult<HelpFaqAppVo> listPage(String searchContent, Integer pageNum, Integer pageSize);

    /**
     * 详情
     *
     * @param helpFaqId 帮助id
     * @return 详情数据
     */
    HelpFaqAppVo detail(Long helpFaqId);

    /**
     * 推荐列表
     *
     * @param helpFaqId 帮助id
     * @return 推荐列表数据
     */
    List<HelpFaqAppVo> recommend(Long helpFaqId);


    /**
     * 阅读量+1
     *
     * @param helpFaqId 帮助id
     */
    void increaseReadCount(Long helpFaqId);

    /**
     * 更新点赞量
     * @param helpFaqId 帮助id
     * @param praised 点赞：true 取消点赞:false
     */

    void updateHelpFaqPraiseCount(Long helpFaqId, boolean praised);
}
