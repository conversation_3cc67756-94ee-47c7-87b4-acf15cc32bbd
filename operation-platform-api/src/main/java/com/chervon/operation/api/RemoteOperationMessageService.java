package com.chervon.operation.api;

import com.chervon.operation.api.dto.MessagePushResultCountDto;
import org.springframework.scheduling.annotation.Async;

import java.util.List;

/**
 * 远程系统/营销消息服务
 *
 * <AUTHOR>
 * @since 2022-12-21 14:11
 **/
public interface RemoteOperationMessageService {

    /**
     * 每次APP启动后,检测是否有需要发送的系统/营销消息
     */
    @Async
    void checkSysAndMarketingMessage(Long userId);

    /**
     * 变更系统消息
     *
     */
    void updateSysMsgCount(List<MessagePushResultCountDto> countDtoList);

    /**
     * 变更营销
     */
    void updateMarketingMsgCount(List<MessagePushResultCountDto> countDtoList);
}
