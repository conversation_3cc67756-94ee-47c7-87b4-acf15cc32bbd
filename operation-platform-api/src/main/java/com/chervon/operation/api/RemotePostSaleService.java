package com.chervon.operation.api;

import com.chervon.operation.api.vo.ProductPostSaleVo;
import com.chervon.operation.api.vo.ProductWikiVo;

/**
 * <AUTHOR>
 * @date 2022/11/15 17:11
 */
public interface RemotePostSaleService {

    /**
     * 获取产品百科数据
     *
     * @param productId 产品id
     * @param language  多语言
     * @return 产品百科数据
     */
    ProductWikiVo productWiki(Long productId, String language);

    /**
     * 获取产品售后数据
     *
     * @param productId 产品id
     * @param language  语言
     * @return 售后数据
     */
    ProductPostSaleVo postSale(Long productId, String language);

    /**
     * 通过产品ID删除售后数据
     * @param productId
     */
    void deletePostSaleByProductId(Long productId);
}
