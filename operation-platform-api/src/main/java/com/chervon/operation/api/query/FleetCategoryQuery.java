package com.chervon.operation.api.query;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2023-08-11 11:29
 **/
@Data
@ApiModel("Fleet设备分类查询")
public class FleetCategoryQuery implements Serializable {
    @ApiModelProperty("品类编码")
    private String code;
    @ApiModelProperty("父级品类编码")
    private String parentCategoryCode;
    @ApiModelProperty("等级")
    private Integer categoryLevel;

    private List<String> codes;

    private String lang;
    /**
     * iot设备标记：0非iot设备  1是iot设备
     */
    private Integer iotFlag;

}
