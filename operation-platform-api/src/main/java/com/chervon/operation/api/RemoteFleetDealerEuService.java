package com.chervon.operation.api;

import com.chervon.operation.api.dto.AppDealerSearchDto;
import com.chervon.operation.api.vo.AppDealerEuVo;

import java.util.List;

public interface RemoteFleetDealerEuService {

    /**
     * 查询附近的经销商
     *
     * @param lang 语言
     * @param req  查询条件
     * @return 数据列表
     */
    List<AppDealerEuVo> search(String lang, AppDealerSearchDto req);

    /**
     * 查询详情
     * @param lang     语言
     * @param dealerId 经销商id
     * @return 详情数据
     */
    AppDealerEuVo detail(String lang, Long dealerId);

    /**
     * 统计数量
     * @param dealerIds
     * @return
     */
    Integer countByDealerIds(List<Long> dealerIds);
    /**
     * 批量查询
     *
     * @param lang          语言
     * @param dealerIds     经销商id集合
     * @param lat           纬度
     * @param lng           经度
     * @param searchContent 查询关键字
     * @return 数据集合
     */
    List<AppDealerEuVo> listByDealerIds(String lang, List<Long> dealerIds, Double lat, Double lng, String searchContent);
}
