package com.chervon.operation.api;

import com.chervon.operation.api.vo.AppAgreementConfigVo;
import com.chervon.operation.api.vo.AppAgreementVo;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022/9/6 14:25
 */
public interface RemoteAppAgreementService {

    /**
     * 查询新版本的app协议
     *
     * @param useType app类型 1 ego 2 fleet app 3 fleet web
     * @param type    类型
     * @return 最新协议
     */
    AppAgreementVo latest(int useType, String type);

    /**
     * 获取app协议配置
     *
     * @return 配置
     */
    AppAgreementConfigVo getConfig();

    /**
     * 根据版本信息获取app协议
     *
     * @param type        类型
     * @param agreementId 协议id
     * @param version     协议版本
     * @param language    语言类型
     * @return app协议
     */
    AppAgreementVo getOneByVersionAndType(String type, Long agreementId, String version, String language);

    /**
     * 类型对应的隐私版本
     *
     * @param useType app类型 1 ego 2 fleet app 3 fleet web
     * @return map
     */
    Map<String, String> getLatestVersion(int useType);

    /**
     * 获取指定协议id，指定版本的协议
     *
     * @param useType          app类型 1 ego 2 fleet app 3 fleet web
     * @param agreementVersion 协议版本
     * @param type             类型 user secret
     * @return
     */
    AppAgreementVo agreed(int useType, String agreementVersion, String type);
}
