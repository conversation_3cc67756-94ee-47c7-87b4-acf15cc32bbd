package com.chervon.operation.api;

import com.chervon.operation.api.query.FleetCategoryQuery;
import com.chervon.operation.api.vo.FleetCategoryListVo;
import com.chervon.operation.api.vo.FleetProductCategoryVo;

import java.util.List;

/**
 * 提供给Fleet服务的categoryRPC类
 *
 * <AUTHOR>
 * @since 2023-08-10 19:31
 **/
public interface RemoteFleetCategoryService {
    /**
     * 获取设备分类下拉框列表
     *
     * @param fleetCategoryQuery 查询条件
     * @return 设备分类列表
     */
    List<FleetCategoryListVo> list(FleetCategoryQuery fleetCategoryQuery);


    /**
     * 根据产品model找到一二级品类id
     *
     * @param productModels 产品model集合
     * @return 产品品类关系列表
     */
    List<FleetProductCategoryVo> listProductCategory(List<String> productModels);

    /**
     * 根据商品型号和语言获取fleet二级品类名称
     *
     * @param model 商品型号
     * @param lang  语言
     * @return 二级品类名称
     */
    String getFleetSecondCategoryName(String model, String lang);

}
