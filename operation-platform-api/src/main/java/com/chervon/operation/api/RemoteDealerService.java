package com.chervon.operation.api;

import com.chervon.operation.api.vo.AppDealerVo;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/11/19 17:39
 */
public interface RemoteDealerService {

    /**
     * 根据条件查询最近的供应商
     *
     * @param category 供应商分类
     * @param lat      经度
     * @param lng      纬度
     * @param lang     多语言
     * @return 列表数据
     */
    List<AppDealerVo> list(String category, double lat, double lng, String lang);

    /**
     * 根据条件分页查询最近的供应商
     *
     * @param category 供应商分类
     * @param lat      经度
     * @param lng      纬度
     * @param distance 距离
     * @return 列表数据
     */
    List<AppDealerVo> list(String category, double lat, double lng, double distance);

    /**
     * 根据经销商id查询列表
     *
     * @param dealerIds 经销商id集合
     * @param lang      多语言
     * @return 列表数据
     */
    List<AppDealerVo> listByIds(List<Long> dealerIds, String lang);
}
