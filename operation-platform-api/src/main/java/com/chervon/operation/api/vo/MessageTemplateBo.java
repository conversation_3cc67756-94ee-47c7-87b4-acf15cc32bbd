package com.chervon.operation.api.vo;

import com.chervon.common.core.domain.MultiLanguageVo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 给其他服务使用的消息模板输出类
 *
 * <AUTHOR>
 * @since 2022-09-15 14:10
 **/
@Data
public class MessageTemplateBo implements Serializable {
    /**
     * id
     */
    @ApiModelProperty("id")
    private Long id;
    /**
     * 模板名称(多语言)
     */
    @ApiModelProperty("模板名称(多语言)")
    private MultiLanguageVo name;
    /**
     * 模板标题(多语言)
     */
    @ApiModelProperty("模板标题(多语言)")
    private MultiLanguageVo title;
    /**
     * 模板内容(多语言)
     */
    @ApiModelProperty("模板内容(多语言)")
    private MultiLanguageVo content;
    /**
     * 消息展示类型： 1 text，2 voice
     */
    private Integer messageDisplayType;
    /**
     * 模板类型：0设备消息
     */
    @ApiModelProperty("模板类型：0设备消息")
    private Integer type;
    /**
     * 被引用数量
     */
    @ApiModelProperty("被引用数量")
    private Integer usedTimes;
}
