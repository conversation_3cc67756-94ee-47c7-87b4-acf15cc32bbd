package com.chervon.operation.api;

import com.chervon.common.core.domain.BaseRemoteReqDto;
import com.chervon.operation.api.vo.BrandVo;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2022-08-20 10:53
 **/
public interface RemoteBrandService {
    /**
     * 根据品牌Id获取品牌信息
     *
     * @param brandIds 品牌Id列表
     * @return 品牌信息Map
     */
    Map<Long, BrandVo> getMyMap(BaseRemoteReqDto<List<Long>> brandIds);

    /**
     * 根据Id获取品牌详情
     *
     * @param id 品牌Id
     * @return 品牌信息
     */
    BrandVo getDetail(BaseRemoteReqDto<Long> id);

    /**
     * 获取所有品类
     * @return 品类列表
     */
    List<BrandVo> allList();
}
