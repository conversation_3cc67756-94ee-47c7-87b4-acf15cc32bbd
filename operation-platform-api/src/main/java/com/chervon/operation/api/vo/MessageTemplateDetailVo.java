package com.chervon.operation.api.vo;

import com.chervon.common.core.domain.MultiLanguageVo;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @since 2022-09-07 16:59
 **/
@Data
public class MessageTemplateDetailVo implements Serializable {
    /**
     * 模板Id
     */
    private Long id;
    /**
     * 模板名称
     */
    private MultiLanguageVo name;
    /**
     * 模板标题
     */
    private MultiLanguageVo title;
    /**
     * 模板内容
     */
    private MultiLanguageVo content;
    /**
     * 模板类型：0设备消息
     */
    private Integer type;
    /**
     * 消息展示类型： 1 text，2 voice
     */
    private Integer messageDisplayType;
}
