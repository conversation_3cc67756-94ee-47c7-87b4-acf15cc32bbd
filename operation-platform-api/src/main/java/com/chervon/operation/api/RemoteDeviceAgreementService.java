package com.chervon.operation.api;

import com.chervon.operation.api.dto.AppDeviceAgreementDetailDto;
import com.chervon.operation.api.dto.AppDeviceAgreementListDto;
import com.chervon.operation.api.vo.AppDeviceAgreementVo;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2022-08-30 13:54
 **/
public interface RemoteDeviceAgreementService {
    /**
     * 通过协议Id和版本号获取详情，如果发现不是最新版本则会抛异常
     *
     * @param appDeviceAgreementDetailDto 协议Id和版本号
     * @return 协议详情Vo
     */
    AppDeviceAgreementVo detailByIdAndVersion(String lang, AppDeviceAgreementDetailDto appDeviceAgreementDetailDto);

    /**
     * 根据产品Id获取最新协议列表
     *
     * @param appDeviceAgreementListDto 产品Id
     * @return 最新协议Vo列表
     */
    List<AppDeviceAgreementVo> listNewestAgreementByProductId(String lang, AppDeviceAgreementListDto appDeviceAgreementListDto);
}
