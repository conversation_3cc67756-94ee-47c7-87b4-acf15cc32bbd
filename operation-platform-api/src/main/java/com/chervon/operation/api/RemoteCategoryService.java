package com.chervon.operation.api;

import com.chervon.common.core.domain.BaseRemoteReqDto;
import com.chervon.operation.api.vo.CategoryLevelBo;
import com.chervon.operation.api.vo.CategoryVo;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022-07-29
 */
public interface RemoteCategoryService {

    /**
     * 获取所有品类
     * @param dto 参数
     * @return 品类列表
     */
    List<CategoryVo> allList(BaseRemoteReqDto<String> dto);


    /**
     * 根据品类Id获取品类信息
     *
     * @param categoryIds 品牌Id
     * @return 品牌信息
     */
    Map<Long, CategoryVo> getMyMap(BaseRemoteReqDto<Map<Long, Long>> categoryIds);

    /**
     * 根据Id获取品类详情
     *
     * @param id 品类Id
     * @return 品类信息
     */
    CategoryVo getDetail(BaseRemoteReqDto<Long> id);

    /**
     * 查询指定类别id的类别信息，如果为空或null，查询全部
     *
     * @param categoryIds 类别id集合
     * @return 类别信息集合
     */
    List<CategoryVo> listByIds(BaseRemoteReqDto<List<Long>> categoryIds);

    /**
     * 获取设备分类分级中间类
     * @param categoryId 分类ID
     * @return 分类分级BO
     */
    CategoryLevelBo getCategoryLevel(Long categoryId);

    /**
     * 根据品类ID检查品类是否存在
     * @param categoryId
     */
    void checkExistByCategoryId(Long categoryId);
}
