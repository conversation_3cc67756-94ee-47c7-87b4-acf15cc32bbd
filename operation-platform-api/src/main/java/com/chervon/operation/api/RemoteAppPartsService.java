package com.chervon.operation.api;

import com.chervon.operation.api.vo.AppPartsListVo;
import com.chervon.operation.api.vo.AppPartsVo;
import com.chervon.operation.api.vo.AppProductPartsVo;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2022/9/6 14:25
 */
public interface RemoteAppPartsService {

    /**
     * 根据产品id查询配件信息
     *
     * @param productId              产品id
     * @param productPartsInstanceId 产品配件实体id
     * @param lang                   语音
     * @return 配件信息
     */
    default AppProductPartsVo findByProductId(Long productId, Long productPartsInstanceId, String lang) {
        return this.listByProductPartsInstanceIds(productId, new ArrayList<>(Collections.singletonList(productPartsInstanceId)), lang)
                .stream().filter(e -> {
                    if (Objects.nonNull(productPartsInstanceId)) {
                        return e.getProductPartsInstanceId().equals(productPartsInstanceId);
                    }
                    return true;
                }
                )
                .findFirst().orElse(new AppProductPartsVo());
    }

    /**
     * 根据产品id查询配件列表
     *
     * @param productId 产品id
     * @param language  语言
     * @return 配件列表
     */
    List<AppPartsListVo> listByProductId(Long productId, String language);

    /**
     * 根据产品id查询配件数量
     *
     * @param productId 产品id
     * @return 配件列表
     */
    Long countByProductId(Long productId);

    /**
     * 根据配件id查询配件详情
     *
     * @param partsId  配件id
     * @param language 语言
     * @return 配件详情
     */
    AppPartsVo detailByPartsId(Long partsId, String language);

    /**
     * 获取配件简单详情信息
     * @param partsId 配件id
     * @param language 语言
     * @return 配件简单详情信息
     */
    AppPartsVo getPartsInfoById(Long partsId, String language);
    /**
     * 批量查询配件信息
     *
     * @param productId               产品id
     * @param productPartsInstanceIds 产品引用配件实体id集合
     * @param language                语言
     * @return 配件列表
     */
    List<AppProductPartsVo> listByProductPartsInstanceIds(Long productId, List<Long> productPartsInstanceIds, String language);

    /**
     * 批量查询配件信息
     *
     * @param productId               产品id
     * @param instanceIds 产品引用配件实体id集合
     * @param language                语言
     * @return 配件列表
     */
    List<AppProductPartsVo> getProductPartsByParam(Long productId, List<Long> instanceIds, List<Long> listPartsId, String language);
    /**
     * 批量查询配件信息
     *
     * @param productId               产品id
     * @param productPartsInstanceIds 产品引用配件实体id集合
     * @param language                语言
     * @return 配件列表
     */
    List<AppProductPartsVo> getProductParts(Long productId, List<Long> productPartsInstanceIds, String language);
    /**
     * 获取配件列表的维保相关信息
     * @param productId
     * @return
     */
    List<AppProductPartsVo> getProductPartsMaintenance(Long productId);
}
