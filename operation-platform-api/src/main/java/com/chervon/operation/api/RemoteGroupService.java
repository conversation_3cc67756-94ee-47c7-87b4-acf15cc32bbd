package com.chervon.operation.api;

import com.chervon.common.core.domain.PageResult;
import com.chervon.operation.api.domain.dto.PageGroupUserDto;

import java.util.List;
import java.util.Map;

/**
 * 分组接口
 *
 * <AUTHOR>
 * @date 16:37 2022/8/10
 **/
public interface RemoteGroupService {

    /**
     * 获取分组arn列表
     *
     * @param groupNames:
     * @return java.util.List<java.lang.String>
     * <AUTHOR>
     * @date 16:39 2022/8/10
     **/
    List<String> listArns(List<String> groupNames);

    /**
     * 根据用户id获取分组名称列表
     *
     * @param userId:
     * @return com.chervon.common.core.domain.PageResult<java.lang.String>
     * <AUTHOR>
     * @date 10:59 2022/9/1
     **/
    List<String> listUserGroupName(Long userId);

    /**
     * 根据分组名称分页获取用户id列表
     *
     * @param pageRequest:
     * @return com.chervon.common.core.domain.PageResult<java.lang.Long>
     * <AUTHOR>
     * @date 11:06 2022/9/1
     **/
    PageResult<Long> pageUserIds(PageGroupUserDto pageRequest);

    /**
     * 根据分组名称获取用户id列表
     *
     * @param groupNames: 支持安逗号分隔
     * @return java.util.List<java.lang.Long>
     * <AUTHOR>
     * @date 11:07 2022/9/1
     **/
    List<Long> listUserId(String groupNames);

    /**
     * 判断分组是否存在
     *
     * @param groupNames 分组名称
     * @return long
     */
    Map<String, Long> countByNames(List<String> groupNames);
}
