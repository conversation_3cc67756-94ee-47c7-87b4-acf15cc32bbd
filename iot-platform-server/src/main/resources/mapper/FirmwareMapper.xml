<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chervon.technology.mapper.FirmwareMapper">
  <resultMap id = "versionMap" type = "com.chervon.technology.domain.vo.component.ComponentVersionVo" >
    <result property="componentNo" column="componentNo"/>
    <result property = "version" column = "version"/>
  </resultMap>

  <select id="mapTechnologyByJobId" resultMap="versionMap">
    select component_no componentNo, package_version version from
    firmware where job_id = #{jobId} and is_deleted = false
  </select>

  <select id="mapDisplayVersionByJobId" resultMap="versionMap">
    select component_no componentNo, display_version version from
      firmware where job_id = #{jobId} and is_deleted = false
  </select>
  <select id="listLatestVersionByJobIds" resultType="com.chervon.technology.domain.entity.Firmware">
    select * from firmware where (package_version, component_no) in
        (select MAX(package_version) as package_version, component_no FROM firmware
        where job_id in
        <foreach collection="jobIds" item="jobId" index="index" open="(" close=")" separator=",">
          #{jobId}
        </foreach>
        and is_deleted = 0 GROUP BY component_no)
    and job_id in
    <foreach collection="jobIds" item="jobId" index="index" open="(" close=")" separator=",">
      #{jobId}
    </foreach>
    and is_deleted = 0;
  </select>
  <select id="listWholePackage" resultType="com.chervon.technology.domain.entity.Firmware">
    select * from firmware where job_id in
    <foreach collection="jobIds" item="jobId" index="index" open="(" close=")" separator=",">
      #{jobId}
    </foreach>
    and component_no = #{componentNo}
    and package_version  <![CDATA[ > ]]>  #{currentVersion}
    and (isnull(minimum_version) or minimum_version <![CDATA[ <= ]]> #{currentVersion})
    and is_deleted = false order by package_version desc
  </select>

  <select id="listFirmwareByJob" resultType="com.chervon.technology.domain.entity.Firmware">
    SELECT
	 f.*
    FROM
	firmware f
	JOIN ota_job j ON f.job_id = j.id
    WHERE
	f.component_no =  #{componentNo}
	AND f.package_type =  'FULL_PACKAGE'
	AND f.is_deleted = 0
	AND j.is_deleted = 0
	AND j.release_status in ('TESTING','RELEASING','RELEASE_READY','RELEASED','OVER','STOPPED','NULLIFY_IN_REVIEW','NULLIFY_REJECTED')
	GROUP BY f.package_version
	ORDER BY f.create_time  desc
  </select>
</mapper>
