package com.chervon.technology.controller;

import com.chervon.authority.sdk.annotation.Log;
import com.chervon.authority.sdk.enums.BusinessType;
import com.chervon.common.core.domain.PageResult;
import com.chervon.common.core.domain.R;
import com.chervon.operation.api.RemoteMessageTemplateService;
import com.chervon.operation.api.dto.MessageTemplateIdDto;
import com.chervon.operation.api.dto.MessageTemplateListDto;
import com.chervon.operation.api.vo.MessageTemplateDetailVo;
import com.chervon.operation.api.vo.MessageTemplateListVo;
import com.chervon.operation.api.vo.RemoteSpinnerVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2022-12-08 10:36
 **/
@RestController
@RequestMapping("/message/template")
@Api(tags = "消息模板接口")
@Slf4j
public class MessageTemplateController {
    @DubboReference
    private RemoteMessageTemplateService remoteMessageTemplateService;

    /**
     * 获取消息模板下拉框
     *
     * @return 消息模板ID+标题的列表
     */
    @ApiOperation("获取消息模板下拉框")
    @PostMapping("/spinner/list")
    @Log(businessType = BusinessType.VIEW)
    public R<List<RemoteSpinnerVo>> listSpinner() {
        return R.ok(remoteMessageTemplateService.listSpinner(LocaleContextHolder.getLocale().getLanguage()));
    }

    /**
     * 分页获取消息模板列表
     *
     * @param messageTemplateListDto 搜索条件
     * @return 分页结果
     */
    @ApiOperation("分页获取消息模板列表")
    @PostMapping("/page")
    @Log(businessType = BusinessType.VIEW)
    public PageResult<MessageTemplateListVo> page(@RequestBody @Validated MessageTemplateListDto messageTemplateListDto) {
        return remoteMessageTemplateService.page(LocaleContextHolder.getLocale().getLanguage(), messageTemplateListDto);
    }

    /**
     * 获取消息模板详情
     *
     * @param messageTemplateIdDto 消息模板Id
     * @return 消息模板详情
     */
    @ApiOperation("获取消息模板详情")
    @PostMapping("/detail")
    @Log(businessType = BusinessType.VIEW)
    public MessageTemplateDetailVo detail(@RequestBody @Validated MessageTemplateIdDto messageTemplateIdDto) {
        return remoteMessageTemplateService.detail(LocaleContextHolder.getLocale().getLanguage(), messageTemplateIdDto);
    }
}
