package com.chervon.technology.controller.aws.ruleengine;

import com.chervon.common.core.constant.CommonConstant;
import com.chervon.common.core.utils.JsonUtils;
import com.chervon.technology.api.toruleengine.*;
import com.chervon.technology.service.OtaDeviceComponentResultService;
import com.chervon.technology.service.OtaJobService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.util.List;
import java.util.Objects;

@Api(tags = "AWS规则引擎接收-OTA相关")
@RestController
@RequestMapping("/rule")
@Slf4j
public class OtaController {

    @Autowired
    private OtaJobService otaJobService;

    @Autowired
    private OtaDeviceComponentResultService otaDeviceComponentResultService;

    /**
     * 检查升级更新-暂时保留，兼容旧固件包版本及旧版本app
     * 对应的topic：'aws/things/+/ota/check' (aws/things/{deviceId}/ota/check)
     * 规则引擎：*_ota_check_rule
     *
     * @param jsonObject:
     * <AUTHOR>
     * @date 16:34 2022/5/16
     * @return: void
     **/
    @PostMapping("/ota/check")
    @ApiOperation("检查升级更新")
    public void otaCheck(@RequestBody CheckJobDto jsonObject) throws IOException {
        log.info("url:/ota/check, deviceId:{}, CheckJobDto:{}", jsonObject.getDeviceId(),
                JsonUtils.toJsonString(jsonObject));
        otaJobService.otaCheck(jsonObject);
    }

    /**
     * 检查升级更新-v2版本
     * 对应的topic：'aws/things/+/+/ota/check' (aws/things/${clientType}/{deviceId}/ota/check)
     * 规则引擎：*_ota_check_rule
     *
     * @param checkJobDto:
     * <AUTHOR>
     * @date 16:34 2022/5/16
     * @return: void
     **/
    @PostMapping("/ota/check/v2")
    @ApiOperation("检查升级更新v2")
    public void otaCheckV2(@RequestHeader String topic, @RequestBody CheckJobDto checkJobDto) throws IOException {
        log.info("url:/ota/check/v2,topic:{}, CheckJobDto:{}", topic,
                JsonUtils.toJsonString(checkJobDto));
        //topic: aws/things/${clientType}/{deviceId}/ota/check
        String[] tempArray = topic.split("/");
        String clientType = tempArray[2];
        checkJobDto.setClientType(clientType);
        otaJobService.otaCheck(checkJobDto);
    }

    /**
     * 更新是否允许升级
     * 对应topic：aws/things/+/jobs/+/action'
     * 规则引擎：*_job_action
     *
     * @param topic:
     * <AUTHOR>
     * @date 16:34 2022/5/16
     * @return: void
     **/
    @PostMapping("/job/action")
    @ApiOperation("更新是否允许升级")
    public void reportJobAction(@RequestHeader String topic, @RequestBody JobActionVo action){
        // topic: aws/things/{deviceId}/jobs/{jobId}/action
        String deviceId = topic.substring(CommonConstant.ELEVEN, topic.indexOf("/jobs/"));
        int start = topic.indexOf("/jobs/") + CommonConstant.SIX;
        int end = topic.indexOf("/action");
        String jobId = topic.substring(start, end);
        log.info("url:/job/action, deviceId:{}, jobId:{}, action:{}", deviceId, jobId,
                JsonUtils.toJsonString(action));
        if(Objects.isNull(action)){
            log.error("url:/job/action, deviceId:{}, jobId:{}, action content is empty", deviceId, jobId);
            return;
        }
        otaJobService.reportJobAction(deviceId, Long.valueOf(jobId), action);
        otaJobService.getJobAction(deviceId, Long.valueOf(jobId));
    }

    /**
     * 获取是否允许升级
     * 对应的主题：aws/things/+/jobs/+/action/get
     * 规则引擎：*_job_action_get
     *
     * @param topic:
     * <AUTHOR>
     * @date 16:34 2022/5/16
     * @return: void
     **/
    @PostMapping("/job/action/get")
    @ApiOperation("获取是否允许升级")
    public void getJobAction(@RequestHeader String topic) {
        // topic: aws/things/{deviceId}/jobs/{jobId}/action/get
        String deviceId = topic.substring(CommonConstant.ELEVEN, topic.indexOf("/jobs/"));
        String jobId = topic.substring(topic.indexOf("/jobs/") + CommonConstant.SIX,
                topic.indexOf("/action/get"));
        log.info("url:/job/action/get, deviceId:{}, jobId:{}", deviceId, jobId);
        otaJobService.getJobAction(deviceId, Long.valueOf(jobId));
    }

    /**
     * 获取固件包最新的下载链接请求
     * 对应的topic: aws/things/+/url/get
     * 规则引擎：*_url_get
     * <p>
     * 新版本废弃，设备端/APP升级版本后删除
     *
     * @param packageKeys:
     * @param topic:
     * <AUTHOR>
     * @date 16:34 2022/5/16
     * @return: void
     **/
    @PostMapping("/url/get")
    @ApiOperation("获取固件包最新的下载链接请求")
    @Deprecated
    public void getUrl(@RequestBody List<String> packageKeys, @RequestHeader String topic)
            throws IOException {
        // topic: aws/things/{deviceId}/url/get
        String deviceId = topic.substring(CommonConstant.ELEVEN, topic.indexOf("/url/get"));
        log.info("url:/url/get, deviceId:{}, topic:{}, packageKeys:{}", deviceId, topic,
                JsonUtils.toJsonString(packageKeys));
        otaJobService.getUrl(deviceId, packageKeys);
    }

    /**
     * 更新总成升级状态
     * 对应的主题：aws/things/+/jobs/+/update
     * 规则引擎：*_update_component_status
     * <p>
     * <p>
     * 新版本废弃，设备端/APP升级版本后删除
     *
     * @param topic:
     * <AUTHOR>
     * @date 16:34 2022/5/16
     * @return: void
     **/
    @PostMapping("/job/package/status")
    @ApiOperation("更新总成升级状态")
    @Deprecated
    public void updatePackageStatus(@RequestHeader String topic,
                                    @RequestBody List<PackageResultDto> resultDtos) {
        // topic: 'aws/things/{deviceId}/jobs/+/update'
        String deviceId = topic.substring(CommonConstant.ELEVEN, topic.indexOf("/jobs/"));
        int jobIdStartIndex = topic.indexOf("/jobs/") + CommonConstant.SIX;
        int jobIdEndIndex = topic.indexOf("/", jobIdStartIndex);
        Long jobId = Long.valueOf(topic.substring(jobIdStartIndex, jobIdEndIndex));
        log.info("url:/job/package/status, deviceId:{}, jobId:{}, resultDto:{}", deviceId, jobId,
                JsonUtils.toJsonString(resultDtos));
        otaDeviceComponentResultService.updatePackageStatus(deviceId, jobId, resultDtos);
    }

    /**
     * 更新设备执行job状态
     * 对应的topic;'$aws/events/jobExecution/+/+'
     * 规则引擎： *_job_status
     * <p>
     * 新版本废弃，设备端/APP升级版本后删除
     *
     * @param jobStatusRuleDto:
     * <AUTHOR>
     * @date 16:34 2022/5/16
     * @return: void
     **/
    @PostMapping("/job/status")
    @ApiOperation("更新设备执行job状态")
    @Deprecated
    public void reportJobStatus(@RequestBody JobStatusRuleDto jobStatusRuleDto) {
        log.info("url:/job/status, jobId:{}, job status:{}, thingArn:{}", jobStatusRuleDto.getJobId(),
                jobStatusRuleDto.getStatus(), jobStatusRuleDto.getThingArn());
        otaJobService.reportJobStatus(jobStatusRuleDto);
    }

    /**
     * 更新设备执行状态及更新设备总成状态
     * 对应的主题：aws/thing/${deviceId}/jobs/${jobId}/update/v2
     * 合并 updatePackageStatus() 和 reportJobStatus()
     *
     * 规则引擎： *_ota_status
     */
    @PostMapping("/ota/status")
    @ApiOperation("更新设备执行job状态v2")
    public void updateOtaStatus(@RequestHeader String topic, @RequestBody OtaResultDto otaResultDto) {
        //aws/thing/${deviceId}/jobs/${jobId}/update/v2
        log.info("url:/ota/status,topic:{}, OtaResultDto:{}", topic,
                JsonUtils.toJsonString(otaResultDto));
        String[] tempArray = topic.split("/");
        String deviceId = tempArray[2];
        Long jobId = Long.valueOf(tempArray[4]);
        otaJobService.updateOtaStatus(jobId, deviceId, otaResultDto);
    }

}
