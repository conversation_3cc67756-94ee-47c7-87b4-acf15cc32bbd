package com.chervon.technology.mapper;

import com.chervon.technology.domain.entity.OtaJobGroup;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import java.util.List;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * 升级任务 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-08-08
 */
public interface OtaJobGroupMapper extends BaseMapper<OtaJobGroup> {

    /**
     * 根据分组名称列表获取已发布或者测试分组测试中的job列表
     * <AUTHOR>
     * @date 19:07 2022/8/24
     * @param groupNames:
     * @param deviceId:
     * @return java.util.List<java.lang.String>
     **/
    List<String> getReadyJobIds(@Param("groupNames") List<String> groupNames,
        @Param("deviceId") String deviceId);

    /**
     * 根据分组名称获取任务id列表
     * <AUTHOR>
     * @date 15:22 2022/9/27
     * @param groupName:
     * @return java.util.List<java.lang.String>
     **/
    List<Long> getJobIdsByGroupName(@Param("groupName") String groupName);
}
