package com.chervon.technology.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.chervon.technology.domain.entity.Firmware;
import com.chervon.technology.domain.vo.component.ComponentVersionVo;
import java.util.List;
import java.util.Map;
import org.apache.ibatis.annotations.MapKey;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * 固件管理 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-07-29
 */
public interface FirmwareMapper extends BaseMapper<Firmware> {

    /**
     * 获取固件版本map
     * <AUTHOR>
     * @date 18:41 2022/8/15
     * @param jobId:
     * @return java.util.Map<java.lang.String,com.chervon.technology.domain.vo.component.ComponentVersionVo>
     **/
    @MapKey("componentNo")
    Map<String, ComponentVersionVo> mapTechnologyByJobId(@Param("jobId") Long jobId);

    /**
     * 获取固件显示版本map
     * <AUTHOR>
     * @date 18:42 2022/8/15
     * @param jobId:
     * @return java.util.Map<java.lang.String,com.chervon.technology.domain.vo.component.ComponentVersionVo>
     **/
    @MapKey("componentNo")
    Map<String, ComponentVersionVo> mapDisplayVersionByJobId(@Param("jobId") Long jobId);

    /**
     * 根据jobId获取相同总成零件号最新的固件列表
     * <AUTHOR>
     * @date 19:42 2022/8/24
     * @param jobIds:
     * @return java.util.List<com.chervon.technology.domain.entity.Firmware>
     **/
    List<Firmware> listLatestVersionByJobIds(@Param("jobIds") List<String> jobIds);

    /**
     * 获取可用升级的整包列表
     * <AUTHOR>
     * @date 11:10 2022/8/25
     * @param jobIds:
     * @param componentNo:
     * @param currentVersion:
     * @return java.util.List<com.chervon.technology.domain.entity.Firmware>
     **/
    List<Firmware> listWholePackage(@Param("jobIds") List<String> jobIds,
        @Param("componentNo") String componentNo,
        @Param("currentVersion") String currentVersion);

    /**
     * 获取任务有效的固件
     *
     * @param componentNo:
     * @return java.util.List<com.chervon.technology.domain.entity.Firmware>
     * <AUTHOR>
     * @date 11:10 2022/8/25
     **/
    List<Firmware> listFirmwareByJob(@Param("componentNo") String componentNo);

}
