package com.chervon.technology.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.chervon.fleet.web.api.entity.dto.ProductFaultDto;
import com.chervon.technology.api.dto.FaultMessageResultCountDto;
import com.chervon.technology.domain.dataobject.FaultMessage;
import com.chervon.technology.domain.dto.fault.message.FaultInfoDto;
import com.chervon.technology.domain.dto.fault.message.FaultMessageAlarmLogDto;
import com.chervon.technology.domain.dto.fault.message.FaultMessageSearchDto;
import com.chervon.technology.domain.vo.fault.message.FaultMessageAlarmLogVo;
import com.chervon.technology.domain.vo.fault.message.FaultMessageVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2022-09-07 20:20
 **/
@Mapper
public interface FaultMessageMapper extends BaseMapper<FaultMessage> {
    /**
     * 告警消息列表分页
     *
     * @param page               分页参数
     * @param search             查询条件
     * @param messageTemplateIds 模板id集合
     * @param faultMessageIds    推送方式集合
     * @return 返回分页
     */
    Page<FaultMessageVo> selectByPage(@Param("page") Page page, @Param("search") FaultMessageSearchDto search,
                                      @Param("messageTemplateIds") List<Long> messageTemplateIds, @Param("faultMessageIds") List<Long> faultMessageIds);


    /**
     * 告警消息列表-不分页
     *
     * @param search             查询条件
     * @param messageTemplateIds 模板id集合
     * @param faultMessageIds    推送方式集合
     * @return 返回分页
     */
    List<FaultMessageVo> selectListBySearchDto(@Param("search") FaultMessageSearchDto search,
                                               @Param("messageTemplateIds") List<Long> messageTemplateIds, @Param("faultMessageIds") List<Long> faultMessageIds);

    /**
     * 告警记录
     *
     * @param page
     * @param search
     * @return
     */
    Page<FaultMessageAlarmLogVo> faultMessageAlarmLogPage(@Param("page") Page page, @Param("search") FaultMessageAlarmLogDto search, @Param("identifiers") List<String> identifiers);

    /**
     * 根据产品id和功能id，查看是否存在告警消息中
     *
     * @param productId  产品id
     * @param propertyId 功能id
     * @return
     */
    Integer propertyCount(@Param("productId") Long productId, @Param("propertyId") String propertyId);

    /**
     * 消息统计信息变更
     */
    int updateMsgCount(@Param("messagePushResultCountDto") FaultMessageResultCountDto messagePushResultCountDto);

    /**
     * 3表联查获取产品错误码关联信息
     *
     * @return
     */
    List<FaultInfoDto> listProductFault();


    List<Map<Long, Long>> selectMessageTemplateCount(@Param("listTemplateId") List<Long> listTemplateId);
}
