package com.chervon.technology.rpc;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.chervon.common.core.constant.CommonConstant;
import com.chervon.common.core.error.ErrorCode;
import com.chervon.common.core.domain.BaseRemoteReqDto;
import com.chervon.common.core.exception.ServiceException;
import com.chervon.common.core.prop.AwsProperties;
import com.chervon.common.core.utils.ConvertUtil;
import com.chervon.common.oss.uitl.UrlUtil;
import com.chervon.common.redis.constant.RedisConstant;
import com.chervon.common.redis.utils.RedisUtils;
import com.chervon.configuration.api.service.RemoteMultiLanguageService;
import com.chervon.operation.api.RemoteAppPartsService;
import com.chervon.operation.api.RemoteCategoryService;
import com.chervon.operation.api.RemoteFleetCategoryService;
import com.chervon.operation.api.RemoteOperationCacheService;
import com.chervon.operation.api.vo.CategoryVo;
import com.chervon.operation.api.vo.FleetProductCategoryVo;
import com.chervon.operation.api.vo.cache.ProductCache;
import com.chervon.technology.api.RemoteDeviceManageService;
import com.chervon.technology.api.dto.DeviceEditDto;
import com.chervon.technology.api.dto.DeviceRegisterDto;
import com.chervon.technology.api.dto.LastRnPackageDto;
import com.chervon.technology.api.dto.ProductReleaseEnum;
import com.chervon.technology.api.enums.DeviceStatusEnum;
import com.chervon.technology.api.enums.DeviceUsageStatusEnum;
import com.chervon.technology.api.exception.TechnologyErrorCode;
import com.chervon.technology.api.exception.TechnologyErrorCodeEnum;
import com.chervon.technology.api.vo.*;
import com.chervon.technology.config.ExceptionMessageUtil;
import com.chervon.technology.config.IotPlatformCommon;
import com.chervon.technology.config.MultiLanguageUtil;
import com.chervon.technology.domain.dataobject.Device;
import com.chervon.technology.domain.dataobject.DeviceCode;
import com.chervon.technology.domain.dataobject.DeviceUserRef;
import com.chervon.technology.domain.dataobject.Product;
import com.chervon.technology.mapper.DeviceMapper;
import com.chervon.technology.service.*;
import com.chervon.technology.util.ProductUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2022-07-22 17:54
 **/
@DubboService
@Service
@Slf4j
public class DeviceManageRpcSerivceImpl implements RemoteDeviceManageService {
    public static final String RN_BUNDLE_NAME_NULL = "rnBundleName-null";
    private static final String COMMUNICATE_MODE_LEY = "product:communicate:mode:";
    private static final String DEFAULT_LANGUAGE = "en";
    /**
     * 10分钟
     */
    private final Long TEN_MIN = CommonConstant.TEN * 60 * 60L;
    @Resource
    private AwsProperties awsProperties;
    @Resource
    private DeviceService deviceService;
    @Resource
    private ProductService productService;
    @Resource
    private DeviceCodeService deviceCodeService;
    @Resource
    private ProductNetworkModeService productNetworkModeService;
    @Resource
    private DeviceMapper deviceMapper;
    @Resource
    private ProductRnService productRnService;
    @DubboReference
    private RemoteAppPartsService remotePartsService;

    @Autowired
    private DeviceUserRefService deviceUserRefService;

    @DubboReference
    private RemoteOperationCacheService remoteOperationCacheService;

    @DubboReference
    private RemoteFleetCategoryService remoteFleetCategoryService;

    @DubboReference
    private RemoteCategoryService remoteCategoryService;

    @DubboReference
    private RemoteMultiLanguageService remoteMultiLanguageService;
    /**
     * 根据地址+图片上传类型获取图片地址
     *
     * @param url      图片url
     * @return url字符串
     */
    private String getFileUrl(String url) {
        if (StringUtils.isEmpty(url)) {
            return null;
        }
        return UrlUtil.completeUrl(awsProperties.getPictureBucket().getCdnHost(), url);
    }

    /**
     * 获取到设备DataObject之后，获取到设备RpcVo
     * {@link DeviceManageRpcSerivceImpl#deviceDetail(String)}
     * {@link DeviceManageRpcSerivceImpl#deviceDetailBySn(String)}
     *
     * @param device 设备DataObject
     * @return DeviceRpcVo
     */
    private DeviceRpcVo detailConvert(Device device) {
        // 获取产品中的属性
        DeviceRpcVo target = ConvertUtil.convert(device, DeviceRpcVo.class);
        target.setVersion(device.getCustomVersion() == null ? null : device.getCustomVersion());
        LambdaQueryWrapper<Product> productWrapper = new LambdaQueryWrapper<Product>()
                .eq(Product::getId, device.getProductId());
        target.setIsReal(device.getIsReal().getValue());
        Product product = productService.getOne(productWrapper);
        if (null != product) {
            target.setProductType(product.getProductType());
            List<String> communicateModes = productNetworkModeService.getNetworkCodeByPid(device.getProductId());
            target.setCommunicateMode(ProductUtil.dealNetworkMode(communicateModes));
            // 根据图标类型设置设备图标返回值
            target.setDeviceIcon(getFileUrl(product.getProductIcon()));
        }
        return target;
    }

    @Override
    public DeviceRpcVo deviceDetail(String deviceId) {
        Device device = deviceService.findByDeviceId(deviceId);
        if (null == device) {
            return null;
        }
        return detailConvert(device);
    }

    @Override
    public List<DeviceRpcVo> getSimpleDeviceInfo(List<String> listDeviceId) {
        LambdaQueryWrapper<Device> queryWrapper = new LambdaQueryWrapper<Device>()
                .in(Device::getDeviceId,listDeviceId)
                .select(Device::getDeviceId,Device::getProductId,
                        Device::getDeviceName,Device::getIsOnline,
                        Device::getActivationTime);
        List<Device> listDevice = deviceService.list(queryWrapper);
        final List<DeviceRpcVo> deviceRpcVos = ConvertUtil.convertList(listDevice, DeviceRpcVo.class);
        return deviceRpcVos;
    }

    private DeviceDetailInfoRpcVo getDeviceInfo(DeviceRpcVo deviceRpcVo,
                                                LastRnPackageDto lastRnPackageDto, Long userId) {
        DeviceDetailInfoRpcVo target = ConvertUtil.convert(deviceRpcVo, DeviceDetailInfoRpcVo.class);
        lastRnPackageDto.setProductId(deviceRpcVo.getProductId());
        //设置RN BUNDLE NAME
        target.setRnBundleName(productRnService.getLatestBundleName(lastRnPackageDto,userId));

        // 设备总成序列号,从缓存中获取
        String componentKey = RedisConstant.OTA_DEVICE_COMPONENT_MAP + deviceRpcVo.getDeviceId();
        Map<String, String> componentMap = RedisUtils.getCacheObject(componentKey);
        if (!CollectionUtils.isEmpty(componentMap)) {
            List<String> componentNoList=componentMap.keySet().stream()
                    .filter(a->StringUtils.isNotEmpty(a))
                    .collect(Collectors.toList());
            Collections.sort(componentNoList);
            target.setAssemblySnList(componentNoList);
        }
        Set<String> deviceCodeDiscard = RedisUtils.getCacheSet(RedisConstant.DEVICE_CODE_DISCARD);
        target.setDeviceCodeStatus((!CollectionUtils.isEmpty(deviceCodeDiscard) &&
                deviceCodeDiscard.contains(deviceRpcVo.getDeviceId())) ?
                CommonConstant.ZERO : CommonConstant.ONE);

        //获取配件个数
        target.setAccessoryQuantity(remotePartsService.countByProductId(deviceRpcVo.getProductId()));
        return target;
    }

    @Override
    public DeviceDetailInfoRpcVo getDeviceDetailByDeviceId(String deviceId,
                                                           LastRnPackageDto lastRnPackageDto,
                                                           Long userId) {
        DeviceRpcVo deviceRpcVo = deviceDetail(deviceId);
        if (null == deviceRpcVo) {
            throw ExceptionMessageUtil.getException(TechnologyErrorCode.TECHNOLOGY_DEVICE_NOT_EXIST, deviceId);
        }
        return getDeviceInfo(deviceRpcVo, lastRnPackageDto, userId);
    }

    @Override
    public DeviceRpcVo deviceDetailBySn(String sn) {
        Device device = deviceService.findBySn(sn);
        if (null == device) {
            return null;
        }
        return detailConvert(device);
    }

    @Override
    public DeviceDetailInfoRpcVo getDeviceDetailBySn(String sn, LastRnPackageDto lastRnPackageDto, Long userId) {
        DeviceRpcVo deviceRpcVo = deviceDetailBySn(sn);
        if (null == deviceRpcVo) {
            return null;
        }
        return getDeviceInfo(deviceRpcVo, lastRnPackageDto, userId);
    }

    @Override
    public List<DeviceListInfoRpcVo> listInfoByDeviceId(List<String> deviceIds) {
        if(CollectionUtils.isEmpty(deviceIds)){
            return Collections.emptyList();
        }
        //下面的方法输入参数deviceIds不可为空，提前判空返回
        List<DeviceRpcVo> target = deviceMapper.getDeviceRpcVoList(deviceIds);
        List<Long> categoryIds = target.stream().map(d -> Long.parseLong(d.getCategoryId())).collect(Collectors.toList());
        Map<Long, CategoryVo> categoryVos = remoteCategoryService.listByIds(new BaseRemoteReqDto<>(LocaleContextHolder.getLocale(), categoryIds))
                .stream().collect(Collectors.toMap(CategoryVo::getId, Function.identity()));
        for (DeviceRpcVo deviceRpcVo : target) {
            CategoryVo vo = categoryVos.get(Long.parseLong(deviceRpcVo.getCategoryId()));
            deviceRpcVo.setCategoryName(MultiLanguageUtil.getByLangCode(remoteMultiLanguageService.getById(vo.getCategoryLangId()).getLangCode(), DEFAULT_LANGUAGE));
            deviceRpcVo.setDeviceIcon(getFileUrl(deviceRpcVo.getDeviceIcon()));
            // 添加设备联网方式
            deviceRpcVo.setCommunicateMode(getCommunicateModes(deviceRpcVo.getProductId()));
        }
        return ConvertUtil.convertList(target, DeviceListInfoRpcVo.class);
    }

    private String getCommunicateModes(Long productId) {
        String mode = RedisUtils.getCacheObject(COMMUNICATE_MODE_LEY + productId);
        if (StringUtils.isNotBlank(mode)) {
            return mode;
        }
        RedissonClient client = RedisUtils.getClient();
        RLock lock = client.getLock("commodityModel" + "_" + productId);
        if (!lock.isLocked()) {
            lock.lock(CommonConstant.FIVE, TimeUnit.SECONDS);
            List<String> communicateModes = productNetworkModeService.getNetworkCodeByPid(productId);
            mode = ProductUtil.dealNetworkMode(communicateModes);
            RedisUtils.setCacheObject(COMMUNICATE_MODE_LEY + productId, mode);
            // 设置10分钟有效
            RedisUtils.expire(COMMUNICATE_MODE_LEY + productId, TEN_MIN);
            lock.unlock();
            return mode;
        } else {
            return getCommunicateModes(productId);
        }
    }

    @Override
    public List<DeviceRpcVo> listSimpleByDeviceId(List<String> deviceIds) {
        if(CollectionUtils.isEmpty(deviceIds)){
            return Collections.emptyList();
        }
        return deviceMapper.getSimpleDeviceRpcVoList(deviceIds);
    }

    @Override
    public void editDevice(DeviceEditDto deviceEditDto) {
        com.chervon.technology.domain.dto.device.DeviceEditDto dto = ConvertUtil.convert(
                deviceEditDto, com.chervon.technology.domain.dto.device.DeviceEditDto.class);
        deviceService.editDevice(dto);
    }

    @Override
    public List<Map<String, Object>> listMapByIds(List<String> deviceIds) {
        return deviceService.listMapByIds(deviceIds);
    }

    @Override
    public DeviceBindBo bindByDeviceId(String deviceId, int businessType, Long userId, Long companyId) {
        return bindByDeviceId(deviceId,businessType);
    }

    @Override
    public DeviceBindBo bindByDeviceId(String deviceId, int businessType) {
        DeviceBindBo result = new DeviceBindBo();

        // 1.根据deviceId获取snCode，截取deviceId的2-5位字符
        String snCode = deviceId.substring(1, 5);
        DeviceCode deviceCode = deviceCodeService.getByDeviceId(deviceId);
        final Integer appRef = checkAppRef(deviceId, businessType);
        if(appRef!=null){
            result.setResult(appRef);
            return result;
        }
        // 2.判断设备是否存在,如果不存在则调用注册逻辑
        Product product = productService.getOne(new LambdaQueryWrapper<Product>()
                .eq(Product::getProductSnCode, snCode)
                .like(Product::getBusinessType, businessType)
                .in(Product::getReleaseStatus, Arrays.asList(
                        ProductReleaseEnum.RELEASED.getValue())));
        if (null == product) {
            throw new ServiceException(TechnologyErrorCodeEnum.PRODUCT_NOT_EXIST);
        }
        String deviceNickName = product.getCommodityModel();
        if (businessType == 2) {
            String fleetSecondCategoryName =
                    remoteFleetCategoryService.getFleetSecondCategoryName(
                            product.getCommodityModel(), LocaleContextHolder.getLocale().getLanguage());
            if (fleetSecondCategoryName != null) {
                deviceNickName = fleetSecondCategoryName;
            }
        }

        Device device = deviceService.getOne(new LambdaQueryWrapper<Device>()
                .eq(Device::getDeviceId, deviceId));
        if (Objects.isNull(device)) {
            DeviceRegisterDto deviceRegisterDto = new DeviceRegisterDto();
            deviceRegisterDto.setDeviceId(deviceId);
            deviceRegisterDto.setDeviceNickName(deviceNickName);
            deviceRegisterDto.setProductId(product.getId());
            deviceService.syncDeviceRegister(deviceRegisterDto);
        }else if(device.getStatus()==DeviceStatusEnum.DISABLE){
            //设备已停用
            result.setResult(3);
            return result;
        }

        result.setDeviceId(deviceId);
        result.setSn(null != deviceCode ? deviceCode.getSn() : null);
        result.setProductId(product.getId());
        result.setProductType(product.getProductType());
        result.setCommodityModel(product.getCommodityModel());
        result.setDeviceNickName(deviceNickName);
        ProductCache productCache = remoteOperationCacheService.getProduct(product.getId());
        if (productCache != null) {
            result.setNetworkModes(productCache.getNetworkModes());
        }
        return result;
    }

    private void handleBindDeviceUserRef(String deviceId, int businessType, Long userId, Long companyId) {
        if(businessType!=1 && businessType!=2){
            throw ExceptionMessageUtil.getException(TechnologyErrorCode.TECHNOLOGY_GET_DATA_TYPE_ERROR, businessType);
        }
        DeviceUserRef deviceUserRef = deviceUserRefService.getOne(new LambdaQueryWrapper<DeviceUserRef>()
                .eq(DeviceUserRef::getDeviceId, deviceId));
        // 新增设备首次初始化绑定关系
        if (deviceUserRef == null) {
            firstBindInit(deviceId, businessType, userId, companyId);
            return;
        }
        //后续新绑定其他业务类型
        updateCurrentBinding(businessType, userId, companyId, deviceUserRef);
    }

    private void updateCurrentBinding(int businessType, Long userId, Long companyId, DeviceUserRef deviceUserRef) {
        DeviceUserRef newOne = new DeviceUserRef();
        newOne.setId(deviceUserRef.getId());
        newOne.setCurrentBindBusinessType(businessType);
        LocalDateTime now = LocalDateTime.now();
        if (businessType == 1) {
            if (deviceUserRef.getEgoFirstBindUserId() == null) {
                newOne.setEgoFirstBindUserId(userId)
                        .setEgoFirstBindTime(now);
            }
        } else if(businessType == 2){
            if (deviceUserRef.getFleetFirstBindCompanyId() == null || deviceUserRef.getFleetFirstBindUserId() == null) {
                newOne.setFleetFirstBindCompanyId(companyId)
                        .setFleetFirstBindUserId(userId)
                        .setFleetFirstBindTime(now);
            }
        }
        final boolean success = deviceUserRefService.updateById(newOne);
        if(!success){
            log.error("update deviceUserRef error:{} ",newOne);
            throw ExceptionMessageUtil.getException(TechnologyErrorCode.UPDATE_DB_ERROR, "[update deviceUserRef row count error]");
        }
    }

    private void firstBindInit(String deviceId, int businessType, Long userId, Long companyId) {
        // 首次绑定
        LocalDateTime now = LocalDateTime.now();
        DeviceUserRef newOne = new DeviceUserRef();
        newOne.setDeviceId(deviceId)
                .setCurrentBindBusinessType(businessType)
                .setFirstBindBusinessType(businessType)
                .setFirstBindTime(now);
        if (businessType == 1) {
            newOne.setEgoFirstBindUserId(userId)
                    .setEgoFirstBindTime(now)
                    .setFirstBindEgoUserId(userId);
        } else {
            newOne.setFleetFirstBindCompanyId(companyId)
                    .setFleetFirstBindUserId(userId)
                    .setFleetFirstBindTime(now)
                    .setFirstBindFleetCompanyId(companyId)
                    .setFirstBindFleetUserId(userId);
        }
        final boolean save = deviceUserRefService.save(newOne);
        if(!save) {
            log.error("save deviceUserRef error:{} ",newOne);
            throw ExceptionMessageUtil.getException(TechnologyErrorCode.UPDATE_DB_ERROR
                    , "[save deviceUserRef row count error]");
        }
    }

    /**
     * 从sn中截取snCode,兼容IOT设备以及非IOT设备
     *
     * @param sn SN
     * @return SnCode
     */
    private String getSnCodeFromSn(String sn) {
        if (sn.matches(IotPlatformCommon.DEVICE_CODE_CHECK)) {
            // 15位多码
            return sn.substring(1, 5);
        } else if (sn.matches(IotPlatformCommon.NO_IOT_DEVICE_CODE_CHECK)) {
            // 16位，R开头的非IOT设备多码
            return sn.substring(2, 6);
        } else {
            throw ExceptionMessageUtil.getException(TechnologyErrorCode.TECHNOLOGY_DEVICE_CODE_SN_ERROR, sn);
        }
    }
    @Override
    public DeviceBindBo bindBySn(String sn, int businessType, Long userId, Long companyId) {
        return bindBySn(sn,businessType);
    }
    @Override
    public DeviceBindBo bindBySn(String sn, int businessType) {
        DeviceBindBo result = new DeviceBindBo();
        String snCode = getSnCodeFromSn(sn);
        //产品型号已发布才允许被绑定
        Product product = productService.getOne(new LambdaQueryWrapper<Product>()
                .eq(Product::getProductSnCode, snCode)
                .like(Product::getBusinessType, businessType)
                .in(Product::getReleaseStatus, Arrays.asList(
                        ProductReleaseEnum.RELEASED.getValue())));
        if (null == product) {
            throw ExceptionMessageUtil.getException(TechnologyErrorCode.TECHNOLOGY_PRODUCT_NOT_EXIST, snCode);
        }
        //获取设备的昵称
        String deviceNickName = product.getCommodityModel();
        if (businessType == 2) {
            String fleetSecondCategoryName =
                    remoteFleetCategoryService.getFleetSecondCategoryName(
                            product.getCommodityModel(),
                            LocaleContextHolder.getLocale().getLanguage());
            if (fleetSecondCategoryName != null) {
                deviceNickName = fleetSecondCategoryName;
            }
        }

        String deviceId = findDeviceCodeBySn(sn,snCode);
        if (StringUtils.isNotBlank(deviceId)) {
            // 0、判断设备是否被其他app绑定
            final Integer appRef = checkAppRef(deviceId, businessType);
            if(appRef!=null){
                result.setResult(appRef);
                return result;
            }
        }
        // 判断设备是否存在,不存在的话注册设备
        Device device = deviceService.findBySn(sn);
        if (null == device) {
            DeviceRegisterDto deviceRegisterDto = new DeviceRegisterDto();
            deviceRegisterDto.setSn(sn);
            deviceRegisterDto.setDeviceId(deviceId);
            deviceRegisterDto.setDeviceNickName(deviceNickName);
            deviceRegisterDto.setProductId(product.getId());
            deviceService.deviceRegisterNoIot(deviceRegisterDto);
        }
        result.setDeviceId(deviceId);
        result.setSn(sn);
        result.setProductId(product.getId());
        result.setProductType(product.getProductType());
        result.setCommodityModel(product.getCommodityModel());
        result.setDeviceNickName(deviceNickName);
        ProductCache productCache = remoteOperationCacheService.getProduct(product.getId());
        if (productCache != null) {
            result.setNetworkModes(productCache.getNetworkModes());
        }
        return result;
    }

    @Override
    @Async
    public void callBackAfterEditDevice(String nickName, String deviceId, Long userId) {
        Device device = deviceService.findByDeviceId(deviceId);
        if (null == device) {
            return;
        }
        Device d = new Device();
        d.setId(device.getId());
        // 设置昵称逻辑
        d.setNickName(nickName);
        // 设置激活用户逻辑
        if (device.getActivationUserId() == null) {
            d.setActivationUserId(userId);
            d.setActivationTime(LocalDateTime.now());
            d.setUsageStatus(DeviceUsageStatusEnum.ACTIVE);
        }
        deviceService.updateById(d);
    }

    @Override
    public void setCurrentBusinessTypeAfterBind(String deviceId, Long userId, Long companyId, Integer businessType) {
        handleBindDeviceUserRef(deviceId, businessType, userId, companyId);
    }

    @Override
    public void setCurrentBusinessTypeAfterUnbind(String deviceId) {
        deviceUserRefService.update(new DeviceUserRef(), new LambdaUpdateWrapper<DeviceUserRef>()
                .set(DeviceUserRef::getCurrentBindBusinessType, 0)
                .eq(DeviceUserRef::getDeviceId, deviceId));
    }

    @Override
    public List<CommonDeviceVo> batchDevice(List<String> deviceIds) {
        if (CollectionUtils.isEmpty(deviceIds)) {
            return new ArrayList<>();
        }
        List<Device> list = deviceService.list(new LambdaQueryWrapper<Device>().in(Device::getDeviceId, deviceIds));
        return list.stream().map(e -> {
            CommonDeviceVo vo = new CommonDeviceVo();
            BeanUtils.copyProperties(e, vo);
            return vo;
        }).collect(Collectors.toList());
    }


    @Override
    public boolean updateSnByDeviceId(String deviceId, String sn) {
         return deviceService.update(Wrappers.<Device>lambdaUpdate()
                  .set(Device::getSn,sn)
                  .eq(Device::getDeviceId,deviceId)
          );
    }

    /**
     * 判断设备是否被其他app绑定
     * @param deviceId
     * @param businessType
     * @return null不做处理
     */
    private Integer checkAppRef(String deviceId,int businessType){
        DeviceUserRef deviceUserRef = deviceUserRefService.getOne(new LambdaQueryWrapper<DeviceUserRef>()
                .eq(DeviceUserRef::getDeviceId, deviceId));
        if (deviceUserRef != null) {
            Integer currentBindBusinessType = deviceUserRef.getCurrentBindBusinessType();
            if ((currentBindBusinessType == 1 && businessType == 2)) {
                // 当前绑定ego，现在要绑定其他，则无法绑定，返回1
                return 1;
            } else if (currentBindBusinessType == 2) {
                // 当前绑定fleet，如果继续绑定fleet，则返回2，如果绑定非fleet，则返回1
                return businessType == 1 ? 1 : 2;
            }
        }
        return null;
    }

    private String findDeviceCodeBySn(String sn,String snCode){
        String deviceId = null;
        DeviceCode deviceCode = null;
        //添加并发锁，防止多码关系重复生成
        RedissonClient client = RedisUtils.getClient();
        RLock lock = client.getLock("device_code_insert_lock:" + sn);
        try {
            lock.lock( 5, TimeUnit.SECONDS);
            deviceCode = deviceCodeService.getBySn(sn);
            if (null == deviceCode) {
                deviceId = deviceCodeService.createDeviceCodeBySn(sn, snCode);
            } else {
                deviceId = deviceCode.getDeviceId();
            }
        } catch (Exception e) {
            throw ExceptionMessageUtil.getException(TechnologyErrorCode.TECHNOLOGY_DEVICE_CODE_ALREADY_EXIST);
        } finally {
            lock.unlock();
        }
        if (deviceCode != null && deviceCode.getStatus().equals(0)) {
            //验证sn是否已作废
            throw ExceptionMessageUtil.getException(TechnologyErrorCode.TECHNOLOGY_DEVICE_CODE_SN_INVALID);
        }
        return deviceId;
    }
}
