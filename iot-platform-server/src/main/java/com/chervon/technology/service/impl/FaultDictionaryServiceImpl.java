package com.chervon.technology.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.chervon.technology.domain.dataobject.DeviceFault;
import com.chervon.technology.domain.dataobject.FaultDictionary;
import com.chervon.technology.mapper.FaultDictionaryMapper;
import com.chervon.technology.service.FaultDictionaryService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.KeyValue;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.text.MessageFormat;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 故障告警消息信息表（从iot平台同步过来给大数据看板查询使用）服务接口实现
 *
 * <AUTHOR>
 * @since 2024-03-13 13:56:34
 * @description 设备证书关系
 */
@Slf4j
@Service
public class FaultDictionaryServiceImpl extends ServiceImpl<FaultDictionaryMapper, FaultDictionary> implements FaultDictionaryService {
    public List<FaultDictionary> getFaultCodeByCode(String faultCode) {
        String language= LocaleContextHolder.getLocale().getLanguage();
        return baseMapper.selectList(new LambdaQueryWrapper<FaultDictionary>()
                .eq(FaultDictionary::getFaultCode, faultCode)
                .eq(FaultDictionary::getLanguage, language));
    }

    @Override
    public List<FaultDictionary> batchGetFaultCode(List<DeviceFault> listFaultCode) {
        if(CollectionUtils.isEmpty(listFaultCode)){
            return Collections.emptyList();
        }
        StringBuilder sb=new StringBuilder();
        for(DeviceFault deviceFault:listFaultCode){
            sb.append("(").append(deviceFault.getProductId()).append(",'").append(deviceFault.getFaultCode()).append("'),");
        }
        sb.deleteCharAt(sb.length() - 1);
        String inSql= MessageFormat.format("and (product_id, fault_code) IN ({0})",sb.toString());
        String language= LocaleContextHolder.getLocale().getLanguage();
        return baseMapper.selectList(new LambdaQueryWrapper<FaultDictionary>()
                .eq(FaultDictionary::getLanguage, language).last(inSql));
    }

    public Map<String, FaultDictionary> batchGetFaultCodeMap(List<DeviceFault> listFaultCode) {
        final List<FaultDictionary> faultDictionaries = batchGetFaultCode(listFaultCode);
        if(CollectionUtils.isEmpty(faultDictionaries)){
            return new HashMap<>();
        }
        return faultDictionaries.stream().collect(Collectors.toMap(a->a.getFaultCode()+"-"+a.getProductId(), Function.identity(), (k1, k2) -> k2));
    }


}