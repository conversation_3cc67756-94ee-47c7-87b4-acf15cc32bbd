package com.chervon.technology.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.csp.sentinel.util.function.Tuple2;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.chervon.common.core.constant.CommonConstant;
import com.chervon.common.core.domain.MultiLanguageVo;
import com.chervon.common.core.domain.PageResult;
import com.chervon.common.core.utils.ConvertUtil;
import com.chervon.configuration.api.core.MultiLanguageBo;
import com.chervon.configuration.api.service.RemoteMultiLanguageService;
import com.chervon.iot.middle.api.pojo.thingmodel.BaseThingModelItem;
import com.chervon.iot.middle.api.service.RemoteRuleService;
import com.chervon.iot.middle.api.service.RemoteThingModelService;
import com.chervon.iot.middle.api.vo.rule.IotRuleVo;
import com.chervon.message.api.enums.MessageTypeEnum;
import com.chervon.operation.api.RemoteMessageTemplateService;
import com.chervon.operation.api.RemoteSuggestionService;
import com.chervon.operation.api.dto.MessageTemplateDto;
import com.chervon.operation.api.vo.MessageTemplateBo;
import com.chervon.technology.api.dto.FaultMessageRecordSearchDto;
import com.chervon.technology.api.dto.FaultMessageResultCountDto;
import com.chervon.technology.api.enums.FaultMessageStatusEnum;
import com.chervon.technology.api.enums.FaultMessageSyncStatusEnum;
import com.chervon.technology.api.enums.PushMethodEnum;
import com.chervon.technology.api.exception.TechnologyErrorCode;
import com.chervon.technology.api.vo.FaultMessageRecordVo;
import com.chervon.technology.config.ExceptionMessageUtil;
import com.chervon.technology.config.IotPlatformCommon;
import com.chervon.technology.config.MultiLanguageUtil;
import com.chervon.technology.domain.bo.SmsChargingStoreBo;
import com.chervon.technology.domain.dataobject.*;
import com.chervon.technology.domain.dto.TriggerDto;
import com.chervon.technology.domain.dto.fault.message.*;
import com.chervon.technology.domain.dto.rule.engine.RuleEngineAddDto;
import com.chervon.technology.domain.dto.rule.engine.RuleEngineEditDto;
import com.chervon.technology.domain.vo.fault.message.FaultMessageAlarmLogVo;
import com.chervon.technology.domain.vo.fault.message.FaultMessageDetailVo;
import com.chervon.technology.domain.vo.fault.message.FaultMessageVo;
import com.chervon.technology.mapper.FaultMessageMapper;
import com.chervon.technology.service.*;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.chervon.technology.api.enums.PushMethodEnum.MESSAGE;

/**
 * <AUTHOR>
 * @since 2022-09-08 10:26
 **/
@SuppressWarnings("Duplicates")
@Slf4j
@Service
public class FaultMessageServiceImpl extends ServiceImpl<FaultMessageMapper, FaultMessage>
        implements FaultMessageService {

    @Lazy
    @Resource
    private ProductService productService;
    @Resource
    private FaultMessageMapper faultMessageMapper;
    @Resource
    private FaultMessageTriggerService faultMessageTriggerService;
    @Resource
    private FaultMessagePushMethodService faultMessagePushMethodService;
    @Resource
    private RuleEngineFaultMessageService ruleEngineFaultMessageService;
    @Resource
    private TriggerService triggerService;
    @Resource
    private FaultMessageAlarmLogService faultMessageAlarmLogService;
    @DubboReference
    private RemoteRuleService remoteRuleService;
    @DubboReference
    private RemoteMessageTemplateService remoteMessageTemplateService;
    @DubboReference
    private RemoteMultiLanguageService remoteMultiLanguageService;
    @DubboReference
    private RemoteSuggestionService remoteSuggestionService;
    @Value("${env.aws.ruleEngineUrl}")
    private String url;
    @Value("${env.aws.ruleEngineConfirmedUrl}")
    private String confirmedUrl;
    @Value("${env.aws.ruleEngineToken}")
    private String token;

    @Autowired
    private ChargingService chargingService;

    @DubboReference
    private RemoteThingModelService remoteThingModelService;


    @Override
    public void deleteRuleByFaultMessageTriggerList(List<FaultMessageTrigger> faultMessageTriggerList) {
        List<String> ruleNameList = new ArrayList<>();
        faultMessageTriggerList.forEach(faultMessageTrigger -> {
            String ruleName = faultMessageTrigger.getFaultMessageId() + IotPlatformCommon.UNDERLINE + faultMessageTrigger.getTriggerId();
            ruleNameList.add(ruleName);
        });
        remoteRuleService.deleteRuleList(ruleNameList);
    }

    @Override
    public void createRuleByFaultMessageTriggerList(Long faultMessageId,List<RuleTrigger> ruleTriggerList) {
        if (CollectionUtils.isEmpty(ruleTriggerList)) {
            log.warn(String.format(TechnologyErrorCode.TECHNOLOGY_FAULT_MESSAGE_NOT_EXIST.getErrorMessage(), faultMessageId));
            return;
        }
        List<IotRuleVo> iotRuleVoList = new ArrayList<>();
        ruleTriggerList.forEach(ruleTrigger -> {
            IotRuleVo iotRuleVo = new IotRuleVo();
            // IOT中规则名称为{规则引擎主键ID}_{触发器主键ID}
            String ruleName = faultMessageId + IotPlatformCommon.UNDERLINE + ruleTrigger.getId();
            iotRuleVo.setRuleName(ruleName);
            // 生成对应的SQL
            Product product = productService.getById(ruleTrigger.getProductId());
            if (StringUtils.isEmpty(product.getProductSnCode())) {
                throw ExceptionMessageUtil.getException(TechnologyErrorCode.TECHNOLOGY_RULE_PRODUCT_HAS_NO_SN_CODE, product.getId());
            }
            String sql = RuleTrigger.getQueryString(ruleTrigger, product.getProductSnCode());
            iotRuleVo.setSql(sql);
            iotRuleVo.setConfirmationUrl(confirmedUrl);
            iotRuleVo.setUrl(url);
            Map<String, String> headers = new HashMap<>(CommonConstant.TWO);
            headers.put(IotPlatformCommon.AUTHORIZATION, token);
            headers.put(IotPlatformCommon.RULE_ID, faultMessageId.toString());
            if (StringUtils.isNotEmpty(ruleTrigger.getGroupId())) {
                headers.put(IotPlatformCommon.GROUP_ID, ruleTrigger.getGroupId());
            }
            iotRuleVo.setHeaders(headers);
            iotRuleVoList.add(iotRuleVo);
        });
        remoteRuleService.createRuleList(iotRuleVoList);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void add(FaultMessageAddDto faultMessageAddDto) {
        //校验消息模板
        if (!remoteMessageTemplateService.check(faultMessageAddDto.getMessageTemplateId())) {
            throw ExceptionMessageUtil.getException(TechnologyErrorCode.TECHNOLOGY_MESSAGE_TEMPLATE_NOT_EXIST, faultMessageAddDto.getMessageTemplateId());
        }
        //校验处理建议
        if (null != faultMessageAddDto.getSuggestionId() && !remoteSuggestionService.check(faultMessageAddDto.getSuggestionId())) {
            throw ExceptionMessageUtil.getException(TechnologyErrorCode.TECHNOLOGY_SUGGESTION_NOT_EXIST, faultMessageAddDto.getSuggestionId());
        }
        Product product = productService.getById(faultMessageAddDto.getProductId());
        //适配APP类型校验
        checkFaultBusinessType(faultMessageAddDto.getPushMethodList(),product.getBusinessType());
        //组装参数
        FaultMessage faultMessage = ConvertUtil.convert(faultMessageAddDto, FaultMessage.class);
        faultMessage.setStatus(CommonConstant.ONE);
        // 不是通用规则引擎同步过来的数据
        faultMessage.setIsSync(FaultMessageSyncStatusEnum.FAULT_MESSAGE_ADD.getSource());
        faultMessage.setBusinessType(faultMessageAddDto.getPushMethodList().stream().map(PushMethodEnum::getBusinessType).distinct().sorted(Integer::compareTo).map(String::valueOf).collect(Collectors.joining(",")));
        faultMessage.setTargetUser(faultMessageAddDto.getPushUserTypes().stream().map(a->a.toString()).collect(Collectors.joining(",")));
        save(faultMessage);
        //创建短信计费
        saveOrUpdateSmsCharging(product.getId(),faultMessage.getId(), faultMessageAddDto.getPushMethodList(),faultMessageAddDto.getMessageTemplateId(),true);
        //触发器记录生成
        Tuple2<List<RuleTrigger>,List<FaultMessageTrigger>> ruleTriggerListTuple2=saveOrUpdateFaultTriggerAndTrigger(faultMessage.getId(),faultMessageAddDto.getTriggerDtoList(),true);
        //告警推送方式生成
        saveOrUpdateFaultMessagePushMethod(faultMessageAddDto.getProductId(),faultMessage.getId(),faultMessageAddDto.getPushMethodList(),true);
        //保存或更新aws规则引擎
        saveOrUpdateAwsRule(faultMessage.getId(),ruleTriggerListTuple2);
    }

    /**
     * 保存或更新aws规则引擎
     * @param faultMsgId
     * @param ruleTriggerListTuple2
     */
    private void saveOrUpdateAwsRule(Long faultMsgId, Tuple2<List<RuleTrigger>, List<FaultMessageTrigger>> ruleTriggerListTuple2) {
        //需要删除的规则引擎
        List<FaultMessageTrigger> faultMessageTriggerList =ruleTriggerListTuple2.r2;
        if(!CollectionUtils.isEmpty(faultMessageTriggerList)){
            deleteRuleByFaultMessageTriggerList(faultMessageTriggerList);
        }
         //需要添加的规则引擎
         List<RuleTrigger> ruleTriggerList = ruleTriggerListTuple2.r1;
         createRuleByFaultMessageTriggerList(faultMsgId,ruleTriggerList);
    }

    /**
     * 适配APP类型校验
     * @param pushMethodList
     * @param businessType
     */
    private void checkFaultBusinessType(List<PushMethodEnum> pushMethodList, String businessType) {
        //校验适配APP类型
        List<Integer> messageBusinessTypes = pushMethodList.stream().map(PushMethodEnum::getBusinessType).distinct().collect(Collectors.toList());
        List<Integer> productBusinessTypes = Stream.of(businessType.split(",")).filter(Objects::nonNull).map(Integer::valueOf).distinct().collect(Collectors.toList());
        if (!productBusinessTypes.containsAll(messageBusinessTypes)) {
            throw ExceptionMessageUtil.getException(TechnologyErrorCode.TECHNOLOGY_PUSH_METHOD_BUSINESS_TYPE_ERROR);
        }
    }

    /**
     * 告警推送方式生成
     * @param productId
     * @param faultMessageId
     * @param pushMethodList
     * @param addFlag 是否是添加还是编辑 true:添加  false:编辑
     */
    private void saveOrUpdateFaultMessagePushMethod(Long productId, Long faultMessageId, List<PushMethodEnum> pushMethodList,boolean addFlag) {
        if(!addFlag){
            //编辑删除老的告警推送方式
            LambdaQueryWrapper<FaultMessagePushMethod> faultMessagePushMethodWrapper = new LambdaQueryWrapper<FaultMessagePushMethod>()
                    .eq(FaultMessagePushMethod::getFaultMessageId, faultMessageId);
            faultMessagePushMethodService.remove(faultMessagePushMethodWrapper);
        }
        // 添加推送方式
        List<FaultMessagePushMethod> faultMessagePushMethodList = new ArrayList<>();
        for (PushMethodEnum pushMethod : pushMethodList) {
            FaultMessagePushMethod faultMessagePushMethod = new FaultMessagePushMethod();
            faultMessagePushMethod.setProductId(productId);
            faultMessagePushMethod.setFaultMessageId(faultMessageId);
            faultMessagePushMethod.setPushMethod(pushMethod);
            faultMessagePushMethodList.add(faultMessagePushMethod);
        }
        faultMessagePushMethodService.saveBatch(faultMessagePushMethodList);
    }

    /**
     *
     * @param faultMessageId
     * @param triggerDtoList
     * @param addFlag 是否是添加还是编辑 true:添加  false:编辑
     * @return
     */
    private Tuple2<List<RuleTrigger>,List<FaultMessageTrigger>> saveOrUpdateFaultTriggerAndTrigger(Long faultMessageId,List<TriggerDto> triggerDtoList,boolean addFlag) {
        List<RuleTrigger> ruleTriggerList = ConvertUtil.convertList(triggerDtoList, RuleTrigger.class);
        List<FaultMessageTrigger> addFaultMessageTriggerList = new ArrayList<>();
        List<FaultMessageTrigger> deleteFaultMessageTriggerList = new ArrayList<>();
        if (ruleTriggerList.isEmpty()) {
            throw ExceptionMessageUtil.getException(TechnologyErrorCode.TECHNOLOGY_RULE_ENGINE_TRIGGER_NOT_NULL);
        }
        triggerService.saveBatch(ruleTriggerList);

        if(!addFlag){
            // 编辑删除老的重新添加触发器及告警触发器关系
            deleteFaultMessageTriggerList=removeFaultMessageRuleTriggerByFaultMessageId(faultMessageId);
        }

        for (RuleTrigger ruleTrigger : ruleTriggerList) {
            FaultMessageTrigger faultMessageTrigger = new FaultMessageTrigger();
            faultMessageTrigger.setFaultMessageId(faultMessageId);
            faultMessageTrigger.setTriggerId(ruleTrigger.getId());
            addFaultMessageTriggerList.add(faultMessageTrigger);
        }
        faultMessageTriggerService.saveBatch(addFaultMessageTriggerList);

        return Tuple2.of(ruleTriggerList,deleteFaultMessageTriggerList);
    }

    /**
     * 构建SMS统计
     *
     * @param productId
     * @param faultMessageId
     * @param pushMethodList
     * @param messageTemplateId
     * @param addFlag 是否是添加还是编辑 true:添加 false编辑
     */

    private void saveOrUpdateSmsCharging(Long productId,Long faultMessageId,List<PushMethodEnum> pushMethodList,Long messageTemplateId,boolean addFlag) {
        List<Charging> sms=new ArrayList<>();
        if(!addFlag){
             sms = chargingService.list(Wrappers.<Charging>lambdaQuery().eq(Charging::getProductId, productId).eq(Charging::getType, "sms"));
        }
        if (pushMethodList != null && pushMethodList.contains(MESSAGE)) {
            Map<String, Object> titleInfo = remoteMessageTemplateService.fetchTitleInfo(messageTemplateId);
            SmsChargingStoreBo bo = new SmsChargingStoreBo(productId,
                    faultMessageId,
                    (Long) titleInfo.get("titleLangId"),
                    (String) titleInfo.get("titleLangCode"),
                     String.valueOf(MessageTypeEnum.DEVICE_MSG.getValue()));
            chargingService.storeSmsCharging(bo);
            sms.removeIf(e -> Objects.equals(e.getMsgId(), faultMessageId));
        }
        //编辑时候删除
        sms.forEach(e -> chargingService.removeSmsCharging(e.getMsgId()));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void edit(FaultMessageEditDto faultMessageEditDto) {

        if (!remoteMessageTemplateService.check(faultMessageEditDto.getMessageTemplateId())) {
            throw ExceptionMessageUtil.getException(TechnologyErrorCode.TECHNOLOGY_MESSAGE_TEMPLATE_NOT_EXIST, faultMessageEditDto.getMessageTemplateId());
        }
        if (null != faultMessageEditDto.getSuggestionId() && !remoteSuggestionService.check(faultMessageEditDto.getSuggestionId())) {
            throw ExceptionMessageUtil.getException(TechnologyErrorCode.TECHNOLOGY_SUGGESTION_NOT_EXIST, faultMessageEditDto.getSuggestionId());
        }

        Product product = productService.getById(faultMessageEditDto.getProductId());
        //适配APP类型校验
        checkFaultBusinessType(faultMessageEditDto.getPushMethodList(),product.getBusinessType());
        //校验告警消息是否存在
        FaultMessage faultMessage = this.getById(faultMessageEditDto.getId());
        if (null == faultMessage) {
            throw ExceptionMessageUtil.getException(TechnologyErrorCode.TECHNOLOGY_FAULT_MESSAGE_NOT_EXIST, faultMessageEditDto.getId());
        }
        //校验是否是规则引擎同步
        if (faultMessage.getIsSync().equals(FaultMessageSyncStatusEnum.RULE_ENGINE_ADD.getSource())) {
            throw ExceptionMessageUtil.getException(TechnologyErrorCode.TECHNOLOGY_FAULT_MESSAGE_NOT_EDITABLE, faultMessageEditDto.getId());
        }
        BeanUtils.copyProperties(faultMessageEditDto, faultMessage);
        faultMessage.setBusinessType(faultMessageEditDto.getPushMethodList().stream().map(PushMethodEnum::getBusinessType).distinct().sorted(Integer::compareTo).map(String::valueOf).collect(Collectors.joining(",")));
        faultMessage.setTargetUser(faultMessageEditDto.getPushUserTypes().stream().map(a -> a.toString()).collect(Collectors.joining(",")));
        this.updateById(faultMessage);
        if (null == faultMessageEditDto.getSuggestionId()) {
            this.update(new FaultMessage(), new LambdaUpdateWrapper<FaultMessage>()
                    .eq(FaultMessage::getId, faultMessageEditDto.getId())
                    .set(FaultMessage::getSuggestionId, null));
        }
        //短信计费更新
        saveOrUpdateSmsCharging(product.getId(),faultMessage.getId(), faultMessageEditDto.getPushMethodList(),faultMessageEditDto.getMessageTemplateId(),false);
        //触发器更新
        Tuple2<List<RuleTrigger>,List<FaultMessageTrigger>> ruleTriggerListTuple2=saveOrUpdateFaultTriggerAndTrigger(faultMessage.getId(),faultMessageEditDto.getTriggerDtoList(),false);
        //告警推送方式生成
        saveOrUpdateFaultMessagePushMethod(faultMessageEditDto.getProductId(),faultMessage.getId(),faultMessageEditDto.getPushMethodList(),false);
        //保存或更新aws规则引擎
        saveOrUpdateAwsRule(faultMessage.getId(),ruleTriggerListTuple2);

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delete(FaultMessageDeleteDto faultMessageDeleteDto) {

        //根据故障消息Id删除触发器和 告警触发器关系
        List<FaultMessageTrigger> faultMessageTriggerList =removeFaultMessageRuleTriggerByFaultMessageId(faultMessageDeleteDto.getId());
        //删除告警消息
        removeById(faultMessageDeleteDto.getId());
        //删除短信计费
        chargingService.removeSmsCharging(faultMessageDeleteDto.getId());
        //删除aws规则引擎
        deleteRuleByFaultMessageTriggerList(faultMessageTriggerList);
    }

    /**
     * 状态启用停用
     *
     * @param faultMessageStatusEditDto 参数dto
     */
    @Override
    public void editStatus(FaultMessageStatusEditDto faultMessageStatusEditDto) {
        FaultMessage faultMessage = getById(faultMessageStatusEditDto.getId());
        if (null != faultMessage) {
            faultMessage.setStatus(faultMessageStatusEditDto.getStatus());
            updateById(faultMessage);
            //查询告警和触发器关系
            List<FaultMessageTrigger> faultMessageTriggerList = faultMessageTriggerService.listByFaultMessageId(faultMessage.getId());
            if (!CollectionUtils.isEmpty(faultMessageTriggerList)) {
                List<String> ruleNames = faultMessageTriggerList.stream()
                        .map(faultMessageTrigger -> faultMessageTrigger.getFaultMessageId().toString() + IotPlatformCommon.UNDERLINE + faultMessageTrigger.getTriggerId().toString())
                        .collect(Collectors.toList());
                // 修改aws 规则引擎状态
                ruleNames.forEach(ruleName ->remoteRuleService.updateRuleStatus(ruleName, FaultMessageStatusEnum.ENABLED.getType().equals(faultMessageStatusEditDto.getStatus())));
            }
        }
    }

    /**
     * 根据故障消息Id删除触发器和 告警触发器关系
     *
     * @param faultMessageId
     */
    public List<FaultMessageTrigger> removeFaultMessageRuleTriggerByFaultMessageId(long faultMessageId) {
        List<FaultMessageTrigger> list = faultMessageTriggerService.listByFaultMessageId(faultMessageId);
        if (!CollectionUtils.isEmpty(list)) {
            // 删除触发器
            triggerService.removeBatchByIds(list.stream().map(FaultMessageTrigger::getTriggerId).collect(Collectors.toList()));
            //根据故障消息Id删除所有关联触发器
            faultMessageTriggerService.removeBatchByIds(list);
        }
        return list;
    }


    /**
     * 分页列表
     *
     * @param faultMessageSearchDto 参数
     * @return 列表
     */
    @Override
    public PageResult<FaultMessageVo> list(FaultMessageSearchDto faultMessageSearchDto) {
        // 查看原始数据
        List<Long> faultMessageIds = new ArrayList<>();
        //模板id集合
        List<Long> messageTemplateIds = new ArrayList<>();
        //根据标题筛选消息Id
        if (null != faultMessageSearchDto.getType() || StringUtils.isNotEmpty(faultMessageSearchDto.getTitle())) {
            MessageTemplateDto messageTemplateDto = new MessageTemplateDto();
            messageTemplateDto.setType(faultMessageSearchDto.getType());
            messageTemplateDto.setTitle(faultMessageSearchDto.getTitle());
            List<MessageTemplateBo> messageTemplateBoList = remoteMessageTemplateService.list(messageTemplateDto);
            List<MessageTemplateBo> newList = new ArrayList<>(messageTemplateBoList.stream()
                    .collect(Collectors.toMap(MessageTemplateBo::getId, Function.identity(), (oldValue, newValue) -> oldValue))
                    .values());
            messageTemplateIds = newList.stream().map(MessageTemplateBo::getId).collect(Collectors.toList());
            if (messageTemplateBoList.isEmpty()) {
                faultMessageSearchDto.setTypeTitle(0);
                faultMessageSearchDto.setType(CommonConstant.ZERO);
            } else {
                faultMessageSearchDto.setType(CommonConstant.ONE);
            }
        }
        //根据推送方式筛选
        if (null != faultMessageSearchDto.getPushMethod()) {
            LambdaQueryWrapper<FaultMessagePushMethod> wrapper = new LambdaQueryWrapper<FaultMessagePushMethod>()
                    .eq(FaultMessagePushMethod::getPushMethod, faultMessageSearchDto.getPushMethod())
                    .eq(FaultMessagePushMethod::getProductId, faultMessageSearchDto.getProductId())
                    .eq(FaultMessagePushMethod::getIsDeleted, 0);
            List<FaultMessagePushMethod> faultMessagePushMethodList = faultMessagePushMethodService.list(wrapper);

            if (faultMessagePushMethodList.isEmpty()) {
                faultMessageSearchDto.setTypeTitle(0);
                return new PageResult<>();
            } else {
                // 对符合搜索条件的告警消息根据ID去重
                List<FaultMessagePushMethod> newList = new ArrayList<>(faultMessagePushMethodList.stream()
                        .collect(Collectors.toMap(FaultMessagePushMethod::getFaultMessageId, Function.identity(), (oldValue, newValue) -> oldValue))
                        .values());
                faultMessageIds = newList.stream().map(FaultMessagePushMethod::getFaultMessageId).collect(Collectors.toList());
            }
        }

        //分页查询
        Page<FaultMessageVo> products = faultMessageMapper.selectByPage(
                new Page<FaultMessageVo>(faultMessageSearchDto.getPageNum(), faultMessageSearchDto.getPageSize()),
                faultMessageSearchDto, messageTemplateIds, faultMessageIds);

        PageResult<FaultMessageVo> res = new PageResult<>(products.getCurrent(),
                products.getSize(), products.getTotal());
        if (products.getTotal() < 1) {
            return res;
        }

        //组装返回参数
        List<FaultMessageVo> voList = products.getRecords();
        for (FaultMessageVo faultMessageVo : voList) {
            //设置消息标题和内容
            if (null != faultMessageVo.getMessageTemplateId()) {
                MessageTemplateBo messageTemplateBo = remoteMessageTemplateService.get(LocaleContextHolder.getLocale().getLanguage(), faultMessageVo.getMessageTemplateId());
                faultMessageVo.setTitle((messageTemplateBo == null || null == messageTemplateBo.getTitle().getMessage()) ? null : messageTemplateBo.getTitle().getMessage());
                faultMessageVo.setContent((messageTemplateBo == null || null == messageTemplateBo.getContent().getMessage()) ? null : messageTemplateBo.getContent().getMessage());
                faultMessageVo.setMessageDisplayType(messageTemplateBo.getMessageDisplayType());
            }
            //设置适用app
            if (StringUtils.isNotBlank(faultMessageVo.getBusinessTypeStr())) {
                faultMessageVo.setBusinessType(Stream.of(faultMessageVo.getBusinessTypeStr().split(",")).map(Integer::valueOf).collect(Collectors.toList()));
            }
            //设置推送方式
            faultMessageVo.setPushMethod(faultMessagePushMethodService.listPushMethodByFaultMessageId(faultMessageVo.getId()));
        }
        res.setList(voList);
        return res;
    }

    /**
     * 分页列表
     *
     * @return 列表
     */
    @Override
    public PageResult<FaultMessageRecordVo> pageRecord(FaultMessageRecordSearchDto faultMessageRecordSearchDto) {
        // 查看原始数据
        List<Long> faultMessageIds = new ArrayList<>();
        //模板id集合
        List<Long> messageTemplateIds = new ArrayList<>();
        //用于多语言搜索null
        if (StringUtils.isNotEmpty(faultMessageRecordSearchDto.getTitle())) {
            MessageTemplateDto messageTemplateDto = new MessageTemplateDto();
            messageTemplateDto.setTitle(faultMessageRecordSearchDto.getTitle());
            List<MessageTemplateBo> messageTemplateBoList = remoteMessageTemplateService.list(messageTemplateDto);
            List<MessageTemplateBo> newList = new ArrayList<>(messageTemplateBoList.stream()
                    .collect(Collectors.toMap(MessageTemplateBo::getId, Function.identity(), (oldValue, newValue) -> oldValue))
                    .values());
            messageTemplateIds = newList.stream().map(MessageTemplateBo::getId).collect(Collectors.toList());

        }
        //推送方式
        if (null != faultMessageRecordSearchDto.getPushMethod()) {
            LambdaQueryWrapper<FaultMessagePushMethod> wrapper = new LambdaQueryWrapper<FaultMessagePushMethod>()
                    .eq(FaultMessagePushMethod::getPushMethod, faultMessageRecordSearchDto.getPushMethod())
                    .eq(FaultMessagePushMethod::getIsDeleted, 0);
            List<FaultMessagePushMethod> faultMessagePushMethodList = faultMessagePushMethodService.list(wrapper);
            if (CollectionUtils.isEmpty(faultMessagePushMethodList)) {
                // 如果根据推送方式筛选没有
                return new PageResult<>();
            }
            if (!faultMessagePushMethodList.isEmpty()) {
                // 对告警消息ID进行去重
                List<FaultMessagePushMethod> newList = new ArrayList<>(faultMessagePushMethodList.stream()
                        .collect(Collectors.toMap(FaultMessagePushMethod::getFaultMessageId, Function.identity(), (oldValue, newValue) -> oldValue))
                        .values());
                faultMessageIds = newList.stream().map(FaultMessagePushMethod::getFaultMessageId).collect(Collectors.toList());
            }
        }
        FaultMessageSearchDto faultMessageSearchDto = new FaultMessageSearchDto();
        faultMessageSearchDto.setId(faultMessageRecordSearchDto.getMsgId());
        faultMessageSearchDto.setTitle(faultMessageRecordSearchDto.getTitle());
        faultMessageSearchDto.setModel(faultMessageRecordSearchDto.getModel());
        faultMessageSearchDto.setCommodityModel(faultMessageRecordSearchDto.getCommodityModel());

        if (StringUtils.isNotEmpty(faultMessageRecordSearchDto.getPushMethod())) {
            faultMessageSearchDto.setPushMethod(PushMethodEnum.valueOf(faultMessageRecordSearchDto.getPushMethod()));
        }
        Page<FaultMessageVo> products = this.getBaseMapper().selectByPage(new Page<FaultMessageVo>(faultMessageRecordSearchDto.getPageNum(), faultMessageRecordSearchDto.getPageSize()),
                faultMessageSearchDto, messageTemplateIds, faultMessageIds);
        PageResult<FaultMessageRecordVo> res = new PageResult<>(products.getCurrent(),
                products.getSize(), products.getTotal());
        if (products.getTotal() < 1) {
            return res;
        }
        List<FaultMessageVo> voList = products.getRecords();
        List<FaultMessageRecordVo> resultList = Lists.newArrayList();
        for (FaultMessageVo faultMessageVo : voList) {
            FaultMessageRecordVo faultMessage = ConvertUtil.convert(faultMessageVo, FaultMessageRecordVo.class);
            if (null != faultMessageVo.getMessageTemplateId()) {
                MessageTemplateBo messageTemplateBo = remoteMessageTemplateService.get(LocaleContextHolder.getLocale().getLanguage(), faultMessageVo.getMessageTemplateId());
                faultMessage.setTitle((messageTemplateBo == null || null == messageTemplateBo.getTitle().getMessage()) ? null
                        : messageTemplateBo.getTitle().getMessage());
                faultMessage.setContent((messageTemplateBo == null || null == messageTemplateBo.getContent().getMessage()) ? null
                        : messageTemplateBo.getContent().getMessage());
            }
            LambdaQueryWrapper<FaultMessagePushMethod> wrapper = new LambdaQueryWrapper<FaultMessagePushMethod>()
                    .eq(FaultMessagePushMethod::getFaultMessageId, faultMessageVo.getId());
            List<String> pushMethodList = faultMessagePushMethodService.list(wrapper).stream().map(a -> {
                if (a.getPushMethod() != null) {
                    return a.getPushMethod().getValue();
                }
                return null;
            }).collect(Collectors.toList());
            faultMessage.setPushTypeCodes(pushMethodList);
            faultMessage.setProductId(faultMessageVo.getProductId());
            faultMessage.setMsgId(faultMessageVo.getId());
            Integer pushSuccessNum = faultMessageVo.getPushSuccessNum() == null ? 0 : faultMessageVo.getPushSuccessNum();
            Integer pushFailNum = faultMessageVo.getPushFailNum() == null ? 0 : faultMessageVo.getPushFailNum();
            faultMessage.setPushAllNum(pushSuccessNum + pushFailNum);
            resultList.add(faultMessage);
        }
        res.setList(resultList);
        return res;

    }

    /**
     * 分页列表
     *
     * @return 列表
     */
    @Override
    public List<FaultMessageRecordVo> listRecord(FaultMessageRecordSearchDto faultMessageRecordSearchDto) {
        // 查看原始数据
        List<Long> faultMessageIds = new ArrayList<>();
        //模板id集合
        List<Long> messageTemplateIds = new ArrayList<>();
        //用于多语言搜索null
        if (StringUtils.isNotEmpty(faultMessageRecordSearchDto.getTitle())) {
            MessageTemplateDto messageTemplateDto = new MessageTemplateDto();
            messageTemplateDto.setTitle(faultMessageRecordSearchDto.getTitle());
            List<MessageTemplateBo> messageTemplateBoList = remoteMessageTemplateService.list(messageTemplateDto);
            List<MessageTemplateBo> newList = new ArrayList<>(messageTemplateBoList.stream()
                    .collect(Collectors.toMap(MessageTemplateBo::getId, Function.identity(), (oldValue, newValue) -> oldValue))
                    .values());
            messageTemplateIds = newList.stream().map(MessageTemplateBo::getId).collect(Collectors.toList());

        }
        //推送方式
        if (null != faultMessageRecordSearchDto.getPushMethod()) {
            LambdaQueryWrapper<FaultMessagePushMethod> wrapper = new LambdaQueryWrapper<FaultMessagePushMethod>()
                    .eq(FaultMessagePushMethod::getPushMethod, faultMessageRecordSearchDto.getPushMethod())
                    .eq(FaultMessagePushMethod::getIsDeleted, 0);
            List<FaultMessagePushMethod> faultMessagePushMethodList = faultMessagePushMethodService.list(wrapper);
            if (CollectionUtils.isEmpty(faultMessagePushMethodList)) {
                // 如果根据推送方式筛选没有
                return Lists.newArrayList();
            }
            if (!faultMessagePushMethodList.isEmpty()) {
                // 对告警消息ID进行去重
                List<FaultMessagePushMethod> newList = new ArrayList<>(faultMessagePushMethodList.stream()
                        .collect(Collectors.toMap(FaultMessagePushMethod::getFaultMessageId, Function.identity(), (oldValue, newValue) -> oldValue))
                        .values());
                faultMessageIds = newList.stream().map(FaultMessagePushMethod::getFaultMessageId).collect(Collectors.toList());
            }
        }
        FaultMessageSearchDto faultMessageSearchDto = new FaultMessageSearchDto();
        faultMessageSearchDto.setId(faultMessageRecordSearchDto.getMsgId());
        faultMessageSearchDto.setTitle(faultMessageRecordSearchDto.getTitle());
        faultMessageSearchDto.setModel(faultMessageRecordSearchDto.getModel());
        faultMessageSearchDto.setCommodityModel(faultMessageRecordSearchDto.getCommodityModel());

        if (StringUtils.isNotEmpty(faultMessageRecordSearchDto.getPushMethod())) {
            faultMessageSearchDto.setPushMethod(PushMethodEnum.valueOf(faultMessageRecordSearchDto.getPushMethod()));
        }
        List<FaultMessageVo> products = this.getBaseMapper().selectListBySearchDto(faultMessageSearchDto, messageTemplateIds, faultMessageIds);
        if (CollectionUtil.isEmpty(products)) {
            return Lists.newArrayList();

        }

        List<FaultMessageRecordVo> resultList = Lists.newArrayList();
        for (FaultMessageVo faultMessageVo : products) {
            FaultMessageRecordVo faultMessage = ConvertUtil.convert(faultMessageVo, FaultMessageRecordVo.class);
            if (null != faultMessageVo.getMessageTemplateId()) {
                MessageTemplateBo messageTemplateBo = remoteMessageTemplateService.get(LocaleContextHolder.getLocale().getLanguage(), faultMessageVo.getMessageTemplateId());
                faultMessage.setTitle((messageTemplateBo == null || null == messageTemplateBo.getTitle().getMessage()) ? null
                        : messageTemplateBo.getTitle().getMessage());
                faultMessage.setContent((messageTemplateBo == null || null == messageTemplateBo.getContent().getMessage()) ? null
                        : messageTemplateBo.getContent().getMessage());
            }
            LambdaQueryWrapper<FaultMessagePushMethod> wrapper = new LambdaQueryWrapper<FaultMessagePushMethod>()
                    .eq(FaultMessagePushMethod::getFaultMessageId, faultMessageVo.getId());
            List<String> pushMethodList = faultMessagePushMethodService.list(wrapper).stream().map(a -> {
                if (a.getPushMethod() != null) {
                    return a.getPushMethod().getValue();
                }
                return null;
            }).collect(Collectors.toList());
            faultMessage.setPushTypeCodes(pushMethodList);
            faultMessage.setProductId(faultMessageVo.getProductId());
            faultMessage.setMsgId(faultMessageVo.getId());
            Integer pushSuccessNum = faultMessageVo.getPushSuccessNum() == null ? 0 : faultMessageVo.getPushSuccessNum();
            Integer pushFailNum = faultMessageVo.getPushFailNum() == null ? 0 : faultMessageVo.getPushFailNum();
            faultMessage.setPushAllNum(pushSuccessNum + pushFailNum);
            resultList.add(faultMessage);
        }
        return resultList;

    }

    @Override
    public PageResult<FaultMessageAlarmLogVo> faultMessageAlarmLogList(FaultMessageAlarmLogDto faultMessageAlarmLogDto) {
        List<String> identifiers=new ArrayList<>();
        if (faultMessageAlarmLogDto.getFaultName() != null && StringUtils.isNotEmpty(faultMessageAlarmLogDto.getFaultName().getMessage())) {
            //根据名称查询该产品下的物模型Id
            identifiers=remoteThingModelService.
                    listThingModelIdentifiersLikeName(faultMessageAlarmLogDto.getProductId(),faultMessageAlarmLogDto.getFaultName().getMessage())
                    .stream().map(BaseThingModelItem::getIdentifier)
                    .collect(Collectors.toList());
        }
        Page<FaultMessageAlarmLogVo> faultMessageAlarmLogVoPage = this.getBaseMapper().faultMessageAlarmLogPage(new Page<FaultMessageAlarmLogVo>(faultMessageAlarmLogDto.getPageNum(), faultMessageAlarmLogDto.getPageSize()),
                faultMessageAlarmLogDto, identifiers);

        PageResult<FaultMessageAlarmLogVo> res = new PageResult<>(faultMessageAlarmLogVoPage.getCurrent(),
                faultMessageAlarmLogVoPage.getSize(), faultMessageAlarmLogVoPage.getTotal());
        if (faultMessageAlarmLogVoPage.getTotal() < 1) {
            return res;
        }
        List<FaultMessageAlarmLogVo> faultMessageAlarmLogVos = faultMessageAlarmLogVoPage.getRecords();
        for (FaultMessageAlarmLogVo faultMessageAlarmLogVo : faultMessageAlarmLogVos) {
            if (StringUtils.isNotEmpty(faultMessageAlarmLogVo.getPropertyId())) {
                BaseThingModelItem baseThingModelItem=remoteThingModelService.getThingModelByIdentifier(faultMessageAlarmLogDto.getProductId(),faultMessageAlarmLogVo.getPropertyId());
                faultMessageAlarmLogVo.setFaultName(new MultiLanguageVo(null,baseThingModelItem.getName()));
            }
        }
        res.setList(faultMessageAlarmLogVos);
        return res;
    }

    /**
     * 详情
     *
     * @param faultMessageIdDto 参数
     * @return 详情
     */
    @Override
    public FaultMessageDetailVo detail(FaultMessageIdDto faultMessageIdDto) {
        // 获取规则引擎表
        FaultMessage faultMessage = getById(faultMessageIdDto.getId());

        if (null == faultMessage) {
            throw ExceptionMessageUtil.getException(TechnologyErrorCode.TECHNOLOGY_FAULT_MESSAGE_NOT_EXIST, faultMessageIdDto.getId());
        }

        FaultMessageDetailVo faultMessageDetailVoResult = ConvertUtil.convert(faultMessage, FaultMessageDetailVo.class);

        String[] pushTypes = faultMessage.getTargetUser().split(",");
        List<Integer> pushTypeList = new ArrayList<>();
        for (String type : pushTypes) {
            pushTypeList.add(Integer.parseInt(type));
        }
        faultMessageDetailVoResult.setPushUserTypes(pushTypeList);

        //根据告警消息配置id查询告警触发器关系列表
        List<FaultMessageTrigger> faultMessageTriggerList =faultMessageTriggerService.listByFaultMessageId(faultMessageIdDto.getId());

        //获取触发器列表
        List<RuleTrigger> ruleTriggerList = triggerService.listByIds(
                faultMessageTriggerList.stream()
                        .map(FaultMessageTrigger::getTriggerId)
                        .collect(Collectors.toList())
        );
        //设置触发器
        faultMessageDetailVoResult.setTriggerDtoList(ConvertUtil.convertList(ruleTriggerList, TriggerDto.class));
        //设置推送方式
        faultMessageDetailVoResult.setPushMethodList(faultMessagePushMethodService.listPushMethodByFaultMessageId(faultMessageIdDto.getId()));
        //设置适配APP类型
        faultMessageDetailVoResult.setBusinessType(Stream.of(faultMessage.getBusinessType().split(",")).map(Integer::valueOf).collect(Collectors.toList()));
        return faultMessageDetailVoResult;
    }

    private Long addWhileAddOrEditRuleEngine(Long productId, Long ruleEngineId, List<Long> triggerIdList,
                                             FaultMessage faultMessage, List<PushMethodEnum> pushMethodList) {
        // 保存故障消息(没有update的情况)
        faultMessage.setStatus(CommonConstant.ONE);
        faultMessage.setIsSync(FaultMessageSyncStatusEnum.RULE_ENGINE_ADD.getSource());
        faultMessage.setProductId(productId);
        this.save(faultMessage);

        // 保存故障消息推送方式
        List<FaultMessagePushMethod> faultMessagePushMethodList = new ArrayList<>();
        for (PushMethodEnum pushMethod : pushMethodList) {
            FaultMessagePushMethod faultMessagePushMethod = new FaultMessagePushMethod();
            faultMessagePushMethod.setFaultMessageId(faultMessage.getId());
            faultMessagePushMethod.setPushMethod(pushMethod);
            faultMessagePushMethod.setProductId(productId);
            faultMessagePushMethodList.add(faultMessagePushMethod);
        }
        faultMessagePushMethodService.saveBatch(faultMessagePushMethodList);

        // 保存触发器关联
        List<FaultMessageTrigger> faultMessageTriggerList = new ArrayList<>();
        for (Long triggerId : triggerIdList) {
            FaultMessageTrigger faultMessageTrigger = new FaultMessageTrigger();
            faultMessageTrigger.setFaultMessageId(faultMessage.getId());
            faultMessageTrigger.setTriggerId(triggerId);
            faultMessageTriggerList.add(faultMessageTrigger);
        }
        faultMessageTriggerService.saveBatch(faultMessageTriggerList);

        // 保存故障消息与规则引擎的关联
        RuleEngineFaultMessage ruleEngineFaultMessage = new RuleEngineFaultMessage();
        ruleEngineFaultMessage.setFaultMessageId(faultMessage.getId());
        ruleEngineFaultMessage.setRuleEngineId(ruleEngineId);
        ruleEngineFaultMessageService.save(ruleEngineFaultMessage);

        // 保存消息路由-规则到iot-core 的相关逻辑见 RuleEngineServiceImpl#add
        return faultMessage.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long addWhileAddRuleEngine(Long productId, Long ruleEngineId, List<Long> triggerIdList, RuleEngineAddDto ruleEngineAddDto, String businessType) {
        // 保存故障消息
        FaultMessage target = ConvertUtil.convert(ruleEngineAddDto, FaultMessage.class);
        // 根据产品发布的使用app类型过滤
        Product product = productService.getById(productId);
        if (product == null || StringUtils.isBlank(product.getBusinessType())) {
            return null;
        }
        List<String> l1 = new ArrayList<>(Arrays.asList(product.getBusinessType().split(",")));
        List<String> l2 = new ArrayList<>(Arrays.asList(businessType.split(",")));
        l1.retainAll(l2);
        if (l1.isEmpty()) {
            return null;
        }
        target.setBusinessType(l1.stream().sorted(String::compareTo).collect(Collectors.joining(",")));
        // 过滤推送方式
        List<PushMethodEnum> pushMethodList = new ArrayList<>();
        ruleEngineAddDto.getPushMethodList().forEach(e -> {
            Integer type = e.getBusinessType();
            if (type != null && l1.contains(type + "")) {
                pushMethodList.add(e);
            }
        });
        if (pushMethodList.isEmpty()) {
            return null;
        }
        target.setId(null);
        return addWhileAddOrEditRuleEngine(productId, ruleEngineId, triggerIdList, target, pushMethodList);
    }

    @Override
    public Long addWhileEditRuleEngine(Long productId, Long ruleEngineId, List<Long> triggerIdList, RuleEngineEditDto ruleEngineEditDto, String businessType) {
        // 保存故障消息
        FaultMessage target = ConvertUtil.convert(ruleEngineEditDto, FaultMessage.class);
        target.setId(null);
        // 根据产品发布的使用app类型过滤
        Product product = productService.getById(productId);
        if (product == null || StringUtils.isBlank(product.getBusinessType())) {
            return null;
        }
        List<String> l1 = new ArrayList<>(Arrays.asList(product.getBusinessType().split(",")));
        List<String> l2 = new ArrayList<>(Arrays.asList(businessType.split(",")));
        l1.retainAll(l2);
        if (l1.isEmpty()) {
            return null;
        }
        target.setBusinessType(l1.stream().sorted(String::compareTo).collect(Collectors.joining(",")));
        // 过滤推送方式
        List<PushMethodEnum> pushMethodList = new ArrayList<>();
        ruleEngineEditDto.getPushMethodList().forEach(e -> {
            Integer type = e.getBusinessType();
            if (type != null && l1.contains(type + "")) {
                pushMethodList.add(e);
            }
        });
        if (pushMethodList.isEmpty()) {
            return null;
        }
        return addWhileAddOrEditRuleEngine(productId, ruleEngineId, triggerIdList, target, pushMethodList);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteWhileDeleteRuleEngine(List<Long> faultMessageIdList) {
        List<FaultMessageTrigger> faultMessageTriggerList=new ArrayList<>();
        faultMessageIdList.forEach(faultMessageId->{
            faultMessageTriggerList.addAll(removeFaultMessageRuleTriggerByFaultMessageId(faultMessageId));
        });
        removeBatchByIds(faultMessageIdList);
        //删除aws规则引擎
        deleteRuleByFaultMessageTriggerList(faultMessageTriggerList);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateWhileEditRuleEngine(Long productId, Long faultMessageId, List<RuleTrigger> ruleTriggerList, RuleEngineEditDto ruleEngineEditDto, Boolean triggerEditFlag, Boolean pushMethodEditFlag, String businessType) {
        // 1.告警消息表
        FaultMessage faultMessage = ConvertUtil.convert(ruleEngineEditDto, FaultMessage.class);
        faultMessage.setId(faultMessageId);
        faultMessage.setProductId(productId);
        faultMessage.setStatus(CommonConstant.ONE);
        faultMessage.setIsSync(FaultMessageSyncStatusEnum.RULE_ENGINE_ADD.getSource());
        // 根据产品发布的使用app类型过滤
        Product product = productService.getById(productId);
        if (product == null || StringUtils.isBlank(product.getBusinessType())) {
            //删除故障消息
            this.deleteWhileEditRuleEngine(faultMessageId);
            return;
        }
        List<String> l1 = new ArrayList<>(Arrays.asList(product.getBusinessType().split(",")));
        List<String> l2 = new ArrayList<>(Arrays.asList(businessType.split(",")));
        l1.retainAll(l2);
        if (l1.isEmpty()) {
            //删除故障消息
            this.deleteWhileEditRuleEngine(faultMessageId);
            return;
        }
        faultMessage.setBusinessType(businessType);
        faultMessage.setBusinessType(l1.stream().sorted(String::compareTo).collect(Collectors.joining(",")));
        // 过滤推送方式
        List<PushMethodEnum> pushMethodList = new ArrayList<>();
        ruleEngineEditDto.getPushMethodList().forEach(e -> {
            Integer type = e.getBusinessType();
            if (type != null && l1.contains(type + "")) {
                pushMethodList.add(e);
            }
        });
        if (pushMethodList.isEmpty()) {
            //删除故障消息
            this.deleteWhileEditRuleEngine(faultMessageId);
            return;
        }

        this.updateById(faultMessage);
        // 处理建议置空
        if (null == ruleEngineEditDto.getSuggestionId()) {
            this.update(new FaultMessage(), new LambdaUpdateWrapper<FaultMessage>()
                    .eq(FaultMessage::getId, faultMessage.getId())
                    .set(FaultMessage::getSuggestionId, null));
        }
        // 2.触发器关联表
        if (triggerEditFlag) {
            LambdaQueryWrapper<FaultMessageTrigger> wrapper = new LambdaQueryWrapper<FaultMessageTrigger>()
                    .eq(FaultMessageTrigger::getFaultMessageId, faultMessageId);
            List<FaultMessageTrigger> faultMessageTriggerInDbList = faultMessageTriggerService.list(wrapper);
            faultMessageTriggerService.remove(wrapper);

            List<FaultMessageTrigger> faultMessageTriggerFromEditList = new ArrayList<>();
            for (RuleTrigger ruleTrigger : ruleTriggerList) {
                FaultMessageTrigger faultMessageTrigger = new FaultMessageTrigger();
                faultMessageTrigger.setFaultMessageId(faultMessageId);
                faultMessageTrigger.setTriggerId(ruleTrigger.getId());
                faultMessageTriggerFromEditList.add(faultMessageTrigger);
            }
            faultMessageTriggerService.saveBatch(faultMessageTriggerFromEditList);
            // 同步到AWS
            deleteRuleByFaultMessageTriggerList(faultMessageTriggerInDbList);
            createRuleByFaultMessageTriggerList(faultMessageId,ruleTriggerList);
        }
        // 3.推送方式关联表
        if (pushMethodEditFlag) {
            LambdaQueryWrapper<FaultMessagePushMethod> faultMessagePushMethodWrapper = new
                    LambdaQueryWrapper<FaultMessagePushMethod>().eq(FaultMessagePushMethod::getFaultMessageId, faultMessageId);
            faultMessagePushMethodService.remove(faultMessagePushMethodWrapper);
            // 保存故障消息推送方式
            List<FaultMessagePushMethod> faultMessagePushMethodList = new ArrayList<>();
            for (PushMethodEnum pushMethod : pushMethodList) {
                FaultMessagePushMethod faultMessagePushMethod = new FaultMessagePushMethod();
                faultMessagePushMethod.setProductId(productId);
                faultMessagePushMethod.setFaultMessageId(faultMessageId);
                faultMessagePushMethod.setPushMethod(pushMethod);
                faultMessagePushMethodList.add(faultMessagePushMethod);
            }
            faultMessagePushMethodService.saveBatch(faultMessagePushMethodList);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteWhileEditRuleEngine(Long faultMessageId) {
        //删除触发器关联
        LambdaQueryWrapper<FaultMessageTrigger> wrapper = new LambdaQueryWrapper<FaultMessageTrigger>()
                .eq(FaultMessageTrigger::getFaultMessageId, faultMessageId);
        List<FaultMessageTrigger> faultMessageTriggerList = faultMessageTriggerService.list(wrapper);
        //根据故障消息Id删除所有关联触发器
        faultMessageTriggerService.remove(wrapper);
        //删除推送方式
        LambdaQueryWrapper<FaultMessagePushMethod> faultMessagePushMethodWrapper = new
                LambdaQueryWrapper<FaultMessagePushMethod>().eq(FaultMessagePushMethod::getFaultMessageId, faultMessageId);
        faultMessagePushMethodService.remove(faultMessagePushMethodWrapper);
        //删除故障消息和规则引擎关联表
        LambdaQueryWrapper<RuleEngineFaultMessage> ruleEngineFaultMessageWrapper = new
                LambdaQueryWrapper<RuleEngineFaultMessage>().eq(RuleEngineFaultMessage::getFaultMessageId, faultMessageId);
        ruleEngineFaultMessageService.remove(ruleEngineFaultMessageWrapper);
        //删除故障消息
        this.removeById(faultMessageId);
        //删除AWS消息路由规则
        deleteRuleByFaultMessageTriggerList(faultMessageTriggerList);
    }


    @Override
    public Integer propertyCount(Long productId, String propertyId) {
        return faultMessageMapper.propertyCount(productId, propertyId);
    }

    @Override
    public void updateMsgCount(List<FaultMessageResultCountDto> countDtos) {
        countDtos.forEach(countDto -> {
            if (countDto.getSystemMessageId().matches("\\d+")) {
                this.getBaseMapper().updateMsgCount(countDto);
            }
        });
    }

    /**
     * 根据消息模板分组统计引用数量
     * @param listTemplateId 消息模板id
     * @return
     */
    @Override
    public Map<Long, Long> selectMessageTemplateCount(List<Long> listTemplateId){
        final List<Map<Long, Long>> mapList = faultMessageMapper.selectMessageTemplateCount(listTemplateId);
        Map<Long, Long> map=new HashMap<>();
        if(CollectionUtils.isEmpty(mapList)){
            return map;
        }
        for(Map<Long, Long> itemMap:mapList){
            Object id = itemMap.get("id");
            Object value=itemMap.get("rowCount");
            if(!Objects.isNull(id) && !Objects.isNull(value)){
                map.put((Long)id,(Long)value);
            }
        }
        return map;
    }
}
