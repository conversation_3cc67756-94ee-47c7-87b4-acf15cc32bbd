package com.chervon.technology.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.chervon.common.core.domain.MultiLanguageVo;
import com.chervon.common.core.domain.PageResult;
import com.chervon.common.core.exception.base.BaseException;
import com.chervon.common.core.utils.ConvertUtil;
import com.chervon.common.core.utils.StringUtils;
import com.chervon.configuration.api.core.MultiLanguageBo;
import com.chervon.configuration.api.service.RemoteMultiLanguageService;
import com.chervon.iot.middle.api.service.RemoteRuleService;
import com.chervon.message.api.enums.MessageTypeEnum;
import com.chervon.operation.api.RemoteMessageTemplateService;
import com.chervon.operation.api.RemoteSuggestionService;
import com.chervon.operation.api.dto.MessageTemplateDto;
import com.chervon.operation.api.vo.MessageTemplateBo;
import com.chervon.technology.api.RemoteTechProductOperationService;
import com.chervon.technology.api.enums.PushMethodEnum;
import com.chervon.technology.api.exception.TechnologyErrorCode;
import com.chervon.technology.config.ExceptionMessageUtil;
import com.chervon.technology.config.IotPlatformCommon;
import com.chervon.technology.config.MultiLanguageUtil;
import com.chervon.technology.domain.bo.SmsChargingStoreBo;
import com.chervon.technology.domain.dataobject.*;
import com.chervon.technology.domain.dto.TriggerDto;
import com.chervon.technology.domain.dto.rule.engine.*;
import com.chervon.technology.domain.vo.rule.engine.RuleEngineDetailVo;
import com.chervon.technology.domain.vo.rule.engine.RuleEngineFaultMessageVo;
import com.chervon.technology.domain.vo.rule.engine.RuleEnginePageVo;
import com.chervon.technology.mapper.RuleEngineMapper;
import com.chervon.technology.service.*;
import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @since 2022-09-08 10:25
 **/
@Slf4j
@Service
public class RuleEngineServiceImpl extends ServiceImpl<RuleEngineMapper, RuleEngine> implements RuleEngineService {
    @Resource
    private TriggerService triggerService;
    @Resource
    private RuleEnginePushMethodService ruleEnginePushMethodService;
    @Resource
    private RuleEngineTriggerService ruleEngineTriggerService;
    @Resource
    private FaultMessageService faultMessageService;
    @Resource
    private RuleEngineFaultMessageService ruleEngineFaultMessageService;
    @DubboReference(timeout = 120000, retries = 0)
    private RemoteMultiLanguageService remoteMultiLanguageService;
    @DubboReference(timeout = 120000, retries = 0)
    private RemoteRuleService remoteRuleService;
    @DubboReference(timeout = 120000, retries = 0)
    private RemoteMessageTemplateService remoteMessageTemplateService;
    @DubboReference(timeout = 120000, retries = 0)
    private RemoteSuggestionService remoteSuggestionService;
    @DubboReference(timeout = 120000, retries = 0)
    private RemoteTechProductOperationService remoteTechProductOperationService;
    @Autowired
    private ProductService productService;

    @Autowired
    private ChargingService chargingService;

    /**
     * 检测消息模板,处理建议
     *
     * @param templateId   消息模板ID
     * @param suggestionId 处理建议ID(非必填)
     */
    private void checkTemplateAndSuggestion(Long templateId, Long suggestionId) {
        if (!remoteMessageTemplateService.check(templateId)) {
            throw ExceptionMessageUtil.getException(TechnologyErrorCode.TECHNOLOGY_MESSAGE_TEMPLATE_NOT_EXIST, templateId);
        }
        if (null != suggestionId && !remoteSuggestionService.check(suggestionId)) {
            throw ExceptionMessageUtil.getException(TechnologyErrorCode.TECHNOLOGY_SUGGESTION_NOT_EXIST);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void add(RuleEngineAddDto ruleEngineAddDto) {
        checkSnCode(ruleEngineAddDto.getTriggerDtoList());
        // 校验推送方式是否为空
        if (CollectionUtils.isEmpty(ruleEngineAddDto.getPushMethodList())) {
            throw ExceptionMessageUtil.getException(TechnologyErrorCode.TECHNOLOGY_RULE_ENGINE_PUSH_METHOD_EMPTY);
        }
        if (!ruleEngineAddDto.getPushMethodList().contains(PushMethodEnum.MESSAGE_REMOVE)) {
            checkTemplateAndSuggestion(ruleEngineAddDto.getMessageTemplateId(), ruleEngineAddDto.getSuggestionId());
        }
        if(CollectionUtils.isEmpty(ruleEngineAddDto.getPushUserTypes())){
            throw ExceptionMessageUtil.getException(TechnologyErrorCode.PARAMETER_NOT_PROVIDED,"pushUserTypes");
        }
        RuleEngine ruleEngine = ConvertUtil.convert(ruleEngineAddDto, RuleEngine.class);
        // 规则名称多语言
        MultiLanguageBo nameMultiLanguageBo = remoteMultiLanguageService.simpleCreateMultiLanguage(
                IotPlatformCommon.APPLICATION_NAME, ruleEngineAddDto.getName(),
                LocaleContextHolder.getLocale().getLanguage());
        ruleEngine.setName(nameMultiLanguageBo.getLangId());
        ruleEngine.setTargetUser(ruleEngineAddDto.getPushUserTypes().stream().map(a->a.toString()).collect(Collectors.joining(",")));
        List<Integer> messageBusinessTypes = ruleEngineAddDto.getPushMethodList().stream().map(PushMethodEnum::getBusinessType).distinct().collect(Collectors.toList());
        // 根据PushMethodEnum列表判断目标APP类型有几种，生成字符串
        String businessType = messageBusinessTypes.stream().sorted(Integer::compareTo).map(String::valueOf).collect(Collectors.joining(","));
        ruleEngine.setBusinessType(businessType);
        //推送方式对应业务类型与产品业务类型匹配检测
        businessTypeCheckProduct(ruleEngineAddDto.getTriggerDtoList(), messageBusinessTypes);

        this.save(ruleEngine);

        // 触发器
        List<RuleTrigger> ruleTriggerList = ConvertUtil.convertList(ruleEngineAddDto.getTriggerDtoList(), RuleTrigger.class);
        List<RuleEngineTrigger> ruleEngineTriggerList = new ArrayList<>();
        if (ruleTriggerList.isEmpty()) {
            throw ExceptionMessageUtil.getException(TechnologyErrorCode.TECHNOLOGY_RULE_ENGINE_TRIGGER_NOT_NULL);
        }
        triggerService.saveBatch(ruleTriggerList);
        for (RuleTrigger ruleTrigger : ruleTriggerList) {
            RuleEngineTrigger ruleEngineTrigger = new RuleEngineTrigger();
            ruleEngineTrigger.setRuleEngineId(ruleEngine.getId());
            ruleEngineTrigger.setTriggerId(ruleTrigger.getId());
            ruleEngineTriggerList.add(ruleEngineTrigger);

        }
        ruleEngineTriggerService.saveBatch(ruleEngineTriggerList);

        // 推送方式
        List<RuleEnginePushMethod> ruleEnginePushMethodList = new ArrayList<>();
        for (PushMethodEnum pushMethod : ruleEngineAddDto.getPushMethodList()) {
            RuleEnginePushMethod ruleEnginePushMethod = new RuleEnginePushMethod();
            ruleEnginePushMethod.setRuleEngineId(ruleEngine.getId());
            ruleEnginePushMethod.setPushMethod(pushMethod);
            ruleEnginePushMethodList.add(ruleEnginePushMethod);
        }
        ruleEnginePushMethodService.saveBatch(ruleEnginePushMethodList);

        HashMap<Long, List<RuleTrigger>> hashMap = new HashMap<>();
        for (RuleTrigger ruleTrigger : ruleTriggerList) {
            if (hashMap.get(ruleTrigger.getProductId()) == null) {
                List<RuleTrigger> list = new ArrayList<>();
                list.add(ruleTrigger);
                hashMap.put(ruleTrigger.getProductId(), list);
            } else {
                hashMap.get(ruleTrigger.getProductId()).add(ruleTrigger);
            }
        }
        hashMap.forEach((keyProductId, valueRuleTriggers) -> {
            // 获取Id列表，将相同产品id的触发器，存储为一个故障消息
            List<Long> ruleTriggerIdList = valueRuleTriggers.stream().map(RuleTrigger::getId).collect(Collectors.toList());
            Long faultMessageId = faultMessageService.addWhileAddRuleEngine(keyProductId, ruleEngine.getId(), ruleTriggerIdList, ruleEngineAddDto, businessType);
            if (faultMessageId != null) {
                // 将故障消息Id与触发器一起存储到iot-core
                faultMessageService.createRuleByFaultMessageTriggerList(faultMessageId,valueRuleTriggers);
            }
        });

        // 新增短信计费
        handleSmsCharging(ruleEngine.getId(),
                ruleEngineAddDto.getPushMethodList() != null && ruleEngineAddDto.getPushMethodList().contains(PushMethodEnum.MESSAGE),
                ruleEngineAddDto.getMessageTemplateId());
    }

    private void checkSnCode(List<TriggerDto> triggerDtoList) {
        if (CollectionUtils.isEmpty(triggerDtoList)) {
            throw ExceptionMessageUtil.getException(TechnologyErrorCode.TECHNOLOGY_RULE_ENGINE_TRIGGER_NOT_NULL);
        }
        List<Long> productIds = triggerDtoList.stream().map(TriggerDto::getProductId).collect(Collectors.toList());
        Map<Long, String> snCodeMap = productService.getSnCodeMap(productIds);
        for(Long productId : snCodeMap.keySet()) {
            if (snCodeMap.get(productId) == null) {
                throw ExceptionMessageUtil.getException(TechnologyErrorCode.TECHNOLOGY_RULE_PRODUCT_HAS_NO_SN_CODE, productId);
            }
        }
    }

    private void businessTypeCheckProduct(List<TriggerDto> triggerDtoList, List<Integer> messageBusinessTypes) {
        if(!CollectionUtils.isEmpty(triggerDtoList)){
            List<Long> productIds = triggerDtoList.stream().map(TriggerDto::getProductId).distinct().collect(Collectors.toList());
            final List<Integer> listProductBusinessType = remoteTechProductOperationService.batchGetBusinessType(productIds);
            if(!messageBusinessTypes.stream().anyMatch(listProductBusinessType::contains)){
                throw ExceptionMessageUtil.getException(TechnologyErrorCode.TECHNOLOGY_RULE_ENGINE_PUSH_NOT_MATCH_ANY_PRODUCT);
            }
            if(!CollectionUtils.isEmpty(listProductBusinessType) && !CollectionUtils.isEmpty(messageBusinessTypes) &&
                    listProductBusinessType.size()==1 && messageBusinessTypes.size()==2){
                final Integer businessType = listProductBusinessType.get(0);
                if(businessType==1){
                    throw ExceptionMessageUtil.getException(TechnologyErrorCode.TECHNOLOGY_RULE_ENGINE_PUSH_PART_MATCH_PRODUCT,"EGO","FLEET");
                }
                else{
                    throw ExceptionMessageUtil.getException(TechnologyErrorCode.TECHNOLOGY_RULE_ENGINE_PUSH_PART_MATCH_PRODUCT,"FLEET","EGO");
                }
            }
        }
    }

    private void handleSmsCharging(Long ruleEngineId, boolean flag, Long messageTemplateId) {
        // 创建短信计费
        List<Charging> sms = chargingService.list(new LambdaQueryWrapper<Charging>().eq(Charging::getRuleEngineId, ruleEngineId).eq(Charging::getType, "sms"));
        if (flag) {
            List<RuleEngineFaultMessage> list = ruleEngineFaultMessageService.list(new LambdaQueryWrapper<RuleEngineFaultMessage>().eq(RuleEngineFaultMessage::getRuleEngineId, ruleEngineId));
            if (!list.isEmpty()) {
                List<FaultMessage> faultMessages = faultMessageService.listByIds(list.stream().map(RuleEngineFaultMessage::getFaultMessageId).collect(Collectors.toList()));
                if (!faultMessages.isEmpty()) {
                    Map<String, Object> titleInfo = remoteMessageTemplateService.fetchTitleInfo(messageTemplateId);
                    faultMessages.forEach(e -> {
                        SmsChargingStoreBo bo = new SmsChargingStoreBo(e.getProductId(),
                                e.getId(),
                                (Long) titleInfo.get("titleLangId"),
                                (String) titleInfo.get("titleLangCode"),
                                MessageTypeEnum.DEVICE_MSG.getValue() + "");
                        bo.setRuleEngineId(ruleEngineId);
                        chargingService.storeSmsCharging(bo);
                        sms.removeIf(i -> Objects.equals(i.getMsgId(), e.getId()));
                    });
                }
            }
        }
        sms.forEach(e -> chargingService.removeSmsCharging(e.getMsgId()));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void edit(RuleEngineEditDto ruleEngineEditDto) {
        RuleEngine ruleEngine = this.getById(ruleEngineEditDto.getId());
        if (null == ruleEngine) {
            throw ExceptionMessageUtil.getException(TechnologyErrorCode.TECHNOLOGY_RULE_ENGINE_NOT_EXIST, ruleEngineEditDto.getId());
        }
        // 校验推送方式是否为空
        if (CollectionUtils.isEmpty(ruleEngineEditDto.getPushMethodList())) {
            throw ExceptionMessageUtil.getException(TechnologyErrorCode.TECHNOLOGY_RULE_ENGINE_PUSH_METHOD_EMPTY);
        }
        if (!ruleEngineEditDto.getPushMethodList().contains(PushMethodEnum.MESSAGE_REMOVE)) {
            checkTemplateAndSuggestion(ruleEngineEditDto.getMessageTemplateId(), ruleEngineEditDto.getSuggestionId());
        }
        RuleEngine one = new RuleEngine();
        one.setId(ruleEngineEditDto.getId());
        List<Integer> messageBusinessTypes = ruleEngineEditDto.getPushMethodList().stream().map(PushMethodEnum::getBusinessType).distinct().collect(Collectors.toList());
        String businessType = messageBusinessTypes.stream().sorted(Integer::compareTo).map(String::valueOf).collect(Collectors.joining(","));
        one.setBusinessType(businessType);
        if(!CollectionUtils.isEmpty(ruleEngineEditDto.getPushUserTypes())){
            one.setTargetUser(ruleEngineEditDto.getPushUserTypes().stream().map(a->a.toString()).collect(Collectors.joining(",")));
        }
        //验证推送方式对应businessType与产品businessType是否一致
        businessTypeCheckProduct(ruleEngineEditDto.getTriggerDtoList(),messageBusinessTypes);
        this.updateById(one);

        // 规则引擎是否被修改，修改过才会触发同步
        boolean ruleEngineEditFlag = !ruleEngineEditDto.isSameAsRuleEngine(ruleEngine);
        MultiLanguageBo multiLanguageBo = remoteMultiLanguageService.getById(ruleEngineEditDto.getName().getLangId().toString());
        if (!Objects.equals(MultiLanguageUtil.getByLangCode(multiLanguageBo.getLangCode(), LocaleContextHolder.getLocale().getLanguage()), ruleEngineEditDto.getName().getMessage())) {
            // 更新规则名称多语言(没有草稿直接更新,且名称更新不涉及同步逻辑)
            remoteMultiLanguageService.simpleUpdateMultiLanguage(IotPlatformCommon.APPLICATION_NAME,
                    ruleEngineEditDto.getName().getLangId(), ruleEngineEditDto.getName().getMessage(),
                    LocaleContextHolder.getLocale().getLanguage());
            ruleEngineEditFlag = true;
        }

        // 触发器
        LambdaQueryWrapper<RuleEngineTrigger> ruleEngineTriggerWrapper = new LambdaQueryWrapper<RuleEngineTrigger>()
                .eq(RuleEngineTrigger::getRuleEngineId, ruleEngineEditDto.getId());
        List<RuleEngineTrigger> ruleEngineTriggerInDbList = ruleEngineTriggerService.list(ruleEngineTriggerWrapper);
        List<Long> ruleTriggerIdList = ruleEngineTriggerInDbList.stream().map(RuleEngineTrigger::getTriggerId)
                .collect(Collectors.toList());
        List<RuleTrigger> ruleTriggerInDbList = triggerService.listByIds(ruleTriggerIdList);
        List<RuleTrigger> ruleTriggerFromDtoList = ConvertUtil.convertList(ruleEngineEditDto.getTriggerDtoList(), RuleTrigger.class);
        // 判断是否编辑了触发器
        // boolean triggerEditFlag = !ruleEngineEditDto.isSameAsRuleTrigger(ruleTriggerInDbList);
        // 每次groupId都会变，对应的触发器就会变
        boolean triggerEditFlag = true;
        if (triggerEditFlag) {
            // 触发器1:删除原本绑定的触发器
            triggerService.remove(new LambdaQueryWrapper<RuleTrigger>().in(RuleTrigger::getId, ruleTriggerIdList));
            ruleEngineTriggerService.remove(ruleEngineTriggerWrapper);
            // 触发器2:创建Dto中的触发器
            List<RuleEngineTrigger> ruleEngineTriggerList = new ArrayList<>();
            if (ruleTriggerFromDtoList.isEmpty()) {
                throw ExceptionMessageUtil.getException(TechnologyErrorCode.TECHNOLOGY_RULE_ENGINE_TRIGGER_NOT_NULL);
            }
            triggerService.saveBatch(ruleTriggerFromDtoList);
            log.info("ruleTriggerFromDtoList: {}", ruleTriggerFromDtoList);
            for (RuleTrigger ruleTrigger : ruleTriggerFromDtoList) {
                RuleEngineTrigger ruleEngineTrigger = new RuleEngineTrigger();
                ruleEngineTrigger.setRuleEngineId(ruleEngine.getId());
                ruleEngineTrigger.setTriggerId(ruleTrigger.getId());
                ruleEngineTriggerList.add(ruleEngineTrigger);
            }
            ruleEngineTriggerService.saveBatch(ruleEngineTriggerList);
        } else {
            // 如果没有编辑触发器,同步编辑逻辑时需要根据ID进行判断
            ruleTriggerFromDtoList = ruleTriggerInDbList;
        }

        // 推送方式
        boolean pushMethodEditFlag = ruleEnginePushMethodService.updateByRuleEngineEditDto(ruleEngineEditDto);

        // 如果判断为有效编辑,则需要同步更新到告警消息表
        if (ruleEngineEditFlag || triggerEditFlag || pushMethodEditFlag) {
            this.updateById(ConvertUtil.convert(ruleEngineEditDto, RuleEngine.class));
            // 处理建议置空
            if (null == ruleEngineEditDto.getSuggestionId()) {
                this.update(new RuleEngine(), new LambdaUpdateWrapper<RuleEngine>()
                        .eq(RuleEngine::getId, ruleEngineEditDto.getId())
                        .set(RuleEngine::getSuggestionId, null));
            }
            HashMap<Long, List<RuleTrigger>> triggerAfterEditMap = new HashMap<>();
            for (RuleTrigger ruleTrigger : ruleTriggerFromDtoList) {
                if (triggerAfterEditMap.get(ruleTrigger.getProductId()) == null) {
                    List<RuleTrigger> list = new ArrayList<>();
                    list.add(ruleTrigger);
                    triggerAfterEditMap.put(ruleTrigger.getProductId(), list);
                } else {
                    triggerAfterEditMap.get(ruleTrigger.getProductId()).add(ruleTrigger);
                }
            }
            HashMap<Long, List<RuleTrigger>> triggerProductIdInDbMap = new HashMap<>();
            RuleEngineFaultMessageDto ruleEngineFaultMessageDto = new RuleEngineFaultMessageDto();
            ruleEngineFaultMessageDto.setRuleEngineId(ruleEngineEditDto.getId());
            List<RuleEngineFaultMessageVo> ruleEngineFaultMessageList = this.getBaseMapper().ruleEngineFaultMessageList(ruleEngineFaultMessageDto);
            if (!CollectionUtils.isEmpty(ruleEngineFaultMessageList)) {
                for (RuleEngineFaultMessageVo ruleEngineFaultMessageVo : ruleEngineFaultMessageList) {
                    triggerProductIdInDbMap.put(ruleEngineFaultMessageVo.getProductId(), new ArrayList<>());
                }
                // fault_message表business_type字段
                synFaultMessage(ruleEngineEditDto.getId(), triggerAfterEditMap, triggerProductIdInDbMap, ruleEngineEditDto, triggerEditFlag, pushMethodEditFlag, businessType);
            }
        }

        // 编辑短信计费
        handleSmsCharging(ruleEngine.getId(),
                ruleEngineEditDto.getPushMethodList() != null && ruleEngineEditDto.getPushMethodList().contains(PushMethodEnum.MESSAGE),
                ruleEngineEditDto.getMessageTemplateId());
    }

    /**
     * 同步操作故障消息
     *
     * @param ruleEngineId            规则引擎id
     * @param triggerAfterEditMap     编辑之后的触发器Map       &lt;产品ID,触发器DataObject>
     * @param triggerProductIdInDbMap 数据库已经存的产品id的Map  &lt;产品ID,空数组>
     * @param ruleEngineEditDto       dto,主要传递给faultMessageService新建更新逻辑
     * @param triggerEditFlag         触发器是否被编辑了
     * @param pushMethodEditFlag      推送方式是否变化
     * @param businessType            目标APP
     */
    public void synFaultMessage(Long ruleEngineId,
                                HashMap<Long, List<RuleTrigger>> triggerAfterEditMap,
                                HashMap<Long, List<RuleTrigger>> triggerProductIdInDbMap,
                                RuleEngineEditDto ruleEngineEditDto,
                                Boolean triggerEditFlag,
                                Boolean pushMethodEditFlag,
                                String businessType) {
        //新添加的触发器和数据库对比相同的产品id和不同的产品id
        //相同的产品id集合
        ArrayList<Long> productIdList = new ArrayList<>();
        //不相同的产品id集合
        ArrayList<Long> productIdNotList = new ArrayList<>();
        if (!triggerAfterEditMap.isEmpty()) {
            for (Map.Entry<Long, List<RuleTrigger>> obj : triggerAfterEditMap.entrySet()) {
                if (triggerProductIdInDbMap.containsKey(obj.getKey())) {
                    productIdList.add(obj.getKey());
                } else {
                    productIdNotList.add(obj.getKey());
                }
            }
            // 1.编辑前后都有的PID(更新)
            if (!CollectionUtils.isEmpty(productIdList)) {
                RuleEngineFaultMessageDto ruleEngineFaultMessageDto = new RuleEngineFaultMessageDto();
                ruleEngineFaultMessageDto.setRuleEngineId(ruleEngineId);
                ruleEngineFaultMessageDto.setProductIds(productIdList);
                List<RuleEngineFaultMessageVo> ruleEngineFaultMessageList = this.getBaseMapper().ruleEngineFaultMessageList(ruleEngineFaultMessageDto);
                if (!CollectionUtils.isEmpty(ruleEngineFaultMessageList)) {
                    for (RuleEngineFaultMessageVo ruleEngineFaultMessageVo : ruleEngineFaultMessageList) {
                        // fault_message表business_type字段
                        faultMessageService.updateWhileEditRuleEngine(ruleEngineFaultMessageVo.getProductId(), ruleEngineFaultMessageVo.getFaultMessageId(), triggerAfterEditMap.get(ruleEngineFaultMessageVo.getProductId()), ruleEngineEditDto, triggerEditFlag, pushMethodEditFlag, businessType);
                    }
                }
            }
            // 2.编辑前有但是编辑后没有的PID(删除)
            RuleEngineFaultMessageDto notRuleEngineFaultMessageDto = new RuleEngineFaultMessageDto();
            notRuleEngineFaultMessageDto.setRuleEngineId(ruleEngineId);
            notRuleEngineFaultMessageDto.setNotProductIds(productIdList);
            List<RuleEngineFaultMessageVo> notRuleEngineFaultMessageList = this.getBaseMapper().ruleEngineFaultMessageList(notRuleEngineFaultMessageDto);
            if (!CollectionUtils.isEmpty(notRuleEngineFaultMessageList)) {
                for (RuleEngineFaultMessageVo vo : notRuleEngineFaultMessageList) {
                    faultMessageService.deleteWhileEditRuleEngine(vo.getFaultMessageId());
                }
            }
            // 3.编辑前没有但是编辑后有的PID(新增)
            if (!CollectionUtils.isEmpty(productIdNotList)) {
                for (Long productId : productIdNotList) {
                    List<RuleTrigger> ruleTriggerList = triggerAfterEditMap.get(productId);
                    List<Long> triggerIdList = ruleTriggerList.stream().map(RuleTrigger::getId).collect(Collectors.toList());
                    // fault_message表business_type字段
                    Long faultMessageId = faultMessageService.addWhileEditRuleEngine(productId, ruleEngineId, triggerIdList, ruleEngineEditDto, businessType);
                    if (faultMessageId != null) {
                        // 将故障消息Id与触发器一起存储到iot-core
                        faultMessageService.createRuleByFaultMessageTriggerList(faultMessageId,ruleTriggerList);
                    }
                }
            }
        } else {
            //如果新增规则是空的，就同步到数据库
            RuleEngineFaultMessageDto ruleEngineFaultMessageDto = new RuleEngineFaultMessageDto();
            ruleEngineFaultMessageDto.setRuleEngineId(ruleEngineEditDto.getId());
            List<RuleEngineFaultMessageVo> ruleEngineFaultMessageList = this.getBaseMapper().ruleEngineFaultMessageList(ruleEngineFaultMessageDto);
            if (!CollectionUtils.isEmpty(ruleEngineFaultMessageList)) {
                for (RuleEngineFaultMessageVo ruleEngineFaultMessageVo : ruleEngineFaultMessageList) {
                    faultMessageService.deleteWhileEditRuleEngine(ruleEngineFaultMessageVo.getFaultMessageId());
                }
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delete(RuleEngineIdDto ruleEngineIdDto) {
        RuleEngine ruleEngine = this.getById(ruleEngineIdDto.getId());
        if (null == ruleEngine) {
            throw ExceptionMessageUtil.getException(TechnologyErrorCode.TECHNOLOGY_RULE_ENGINE_NOT_EXIST, ruleEngineIdDto.getId());
        } else if (ruleEngine.getName() != null){
            // 清理多语言
            remoteMultiLanguageService.deleteByLangIds(Arrays.asList(ruleEngine.getName()));
        }
        this.removeById(ruleEngine);
        // 删除原本绑定的触发器
        LambdaQueryWrapper<RuleEngineTrigger> ruleEngineTriggerWrapper = new LambdaQueryWrapper<RuleEngineTrigger>()
                .eq(RuleEngineTrigger::getRuleEngineId, ruleEngineIdDto.getId());
        List<RuleEngineTrigger> ruleEngineTriggerInDbList = ruleEngineTriggerService.list(ruleEngineTriggerWrapper);
        ruleEngineTriggerService.remove(ruleEngineTriggerWrapper);

        List<Long> ruleTriggerIdList = ruleEngineTriggerInDbList.stream().map(RuleEngineTrigger::getTriggerId)
                .collect(Collectors.toList());
        triggerService.remove(new LambdaQueryWrapper<RuleTrigger>().in(RuleTrigger::getId, ruleTriggerIdList));

        // 推送方式
        ruleEnginePushMethodService.remove(new LambdaQueryWrapper<RuleEnginePushMethod>()
                .eq(RuleEnginePushMethod::getRuleEngineId, ruleEngineIdDto.getId()));

        // 同步删除故障消息isSync=1
        LambdaQueryWrapper<RuleEngineFaultMessage> ruleEngineFaultMessageWrapper = new LambdaQueryWrapper<RuleEngineFaultMessage>()
                .eq(RuleEngineFaultMessage::getRuleEngineId, ruleEngine.getId());
        List<RuleEngineFaultMessage> list = ruleEngineFaultMessageService.list(ruleEngineFaultMessageWrapper);
        ruleEngineFaultMessageService.removeBatchByIds(list);
        List<Long> faultMessageIdList = list.stream().map(RuleEngineFaultMessage::getFaultMessageId).collect(Collectors.toList());
        faultMessageService.deleteWhileDeleteRuleEngine(faultMessageIdList);

        // 删除短信计费
        List<Charging> sms = chargingService.list(new LambdaQueryWrapper<Charging>().eq(Charging::getRuleEngineId, ruleEngineIdDto.getId()).eq(Charging::getType, "sms"));
        sms.forEach(e -> chargingService.removeSmsCharging(e.getMsgId()));
    }

    @Override
    public RuleEngineDetailVo detail(RuleEngineIdDto ruleEngineIdDto) {
        // 获取规则引擎表
        RuleEngine ruleEngine = this.getOne(new LambdaQueryWrapper<RuleEngine>()
                .eq(RuleEngine::getId, ruleEngineIdDto.getId()));
        if (null == ruleEngine) {
            throw ExceptionMessageUtil.getException(TechnologyErrorCode.TECHNOLOGY_RULE_ENGINE_NOT_EXIST, ruleEngineIdDto.getId());
        }
        RuleEngineDetailVo result = ConvertUtil.convert(ruleEngine, RuleEngineDetailVo.class);
        // 规则名称多语言
        MultiLanguageBo nameBo = remoteMultiLanguageService.getById(ruleEngine.getName().toString());
        MultiLanguageVo nameVo = new MultiLanguageVo(nameBo.getLangId(),
                com.chervon.technology.config.MultiLanguageUtil.getByLangCode(nameBo.getLangCode(), LocaleContextHolder.getLocale().getLanguage()));
        result.setName(nameVo);
        // 获取触发器
        List<RuleEngineTrigger> ruleEngineTriggerList = ruleEngineTriggerService.list(
                new LambdaQueryWrapper<RuleEngineTrigger>().eq(RuleEngineTrigger::getRuleEngineId, ruleEngineIdDto.getId()));
        List<Long> ruleTriggerIdList = ruleEngineTriggerList.stream().map(RuleEngineTrigger::getTriggerId)
                .collect(Collectors.toList());
        List<RuleTrigger> ruleTriggerList = triggerService
                .list(new LambdaQueryWrapper<RuleTrigger>().in(RuleTrigger::getId, ruleTriggerIdList));
        List<TriggerDto> triggerDtoList = ConvertUtil.convertList(ruleTriggerList, TriggerDto.class);
        // 获取产品的商品型号信息
        final List<Long> productIds = triggerDtoList.stream().map(TriggerDto::getProductId).collect(Collectors.toList());
        if(!CollectionUtils.isEmpty(productIds)){
            final LambdaQueryWrapper<Product> productQuery = new LambdaQueryWrapper<Product>().in(Product::getId, productIds)
                    .select(Product::getId, Product::getCommodityModel);
            final List<Product> productList = productService.list(productQuery);
            triggerDtoList.forEach(triggerDto -> {
                final Product product = productList.stream().filter(item -> item.getId().equals(triggerDto.getProductId())).findFirst().orElse(null);
                if (null != product) {
                    triggerDto.setCommodityModel(product.getCommodityModel());
                }
            });
        }

        result.setTriggerDtoList(triggerDtoList);
        // 获取推送方式
        result.setPushMethodList(ruleEnginePushMethodService.list(new LambdaQueryWrapper<RuleEnginePushMethod>()
                        .eq(RuleEnginePushMethod::getRuleEngineId, ruleEngineIdDto.getId())
                        .select(RuleEnginePushMethod::getPushMethod))
                .stream().map(RuleEnginePushMethod::getPushMethod).collect(Collectors.toList()));
        // 获取推送用户类型
        if(StringUtil.isNotEmpty(ruleEngine.getTargetUser())) {
            result.setPushUserTypes(Arrays.asList(ruleEngine.getTargetUser().split(",")).stream().map(Integer::parseInt).collect(Collectors.toList()));
        }else{
            result.setPushUserTypes(new ArrayList<>());
        }
        return result;
    }

    @Override
    public PageResult<RuleEnginePageVo> page(RuleEnginePageDto pageDto) {
        PageResult<RuleEnginePageVo> result;
        try {
            List<MessageTemplateBo> messageTemplateBoList = new ArrayList<>();
            //用于多语言搜索null
            Integer typeTitle = null;
            // 如果消息模板相关的查询条件不为空
            if (null != pageDto.getType() || StringUtil.isNotEmpty(pageDto.getTitle())) {
                MessageTemplateDto messageTemplateDto = new MessageTemplateDto();
                messageTemplateDto.setType(pageDto.getType());
                messageTemplateDto.setTitle(pageDto.getTitle());
                messageTemplateBoList = remoteMessageTemplateService.list(messageTemplateDto);
                log.info("messageTemplateBoList" + messageTemplateBoList.toString());
                if (messageTemplateBoList.isEmpty()) {
                    typeTitle = 0;
                } else {
                    typeTitle = 1;
                }
            }
            // 如果推送方式条件不为空
            List<RuleEnginePushMethod> ruleEnginePushMethodList = new ArrayList<>();
            if (null != pageDto.getPushMethod()) {
                LambdaQueryWrapper<RuleEnginePushMethod> pushMethodWrapper = new LambdaQueryWrapper<RuleEnginePushMethod>()
                        .eq(RuleEnginePushMethod::getPushMethod, pageDto.getPushMethod());
                ruleEnginePushMethodList = ruleEnginePushMethodService.list(pushMethodWrapper);
            }
            LambdaQueryWrapper<RuleEngine> ruleEngineLambdaQueryWrapper = new LambdaQueryWrapper<>();
            //用于多语言搜索null
            Integer typeName = null;
            if (StringUtil.isNotEmpty(pageDto.getName())) {
                //多语言模糊搜索
                LambdaQueryWrapper<RuleEngine> wrapper = new LambdaQueryWrapper<RuleEngine>().select(RuleEngine::getName);
                List<RuleEngine> ruleEngineList = this.list(wrapper);
                List<Long> ids = ruleEngineList.stream().map(RuleEngine::getName).filter(Objects::nonNull).collect(Collectors.toList());
                Map<String, List<Long>> query = new HashMap<>();
                query.put(pageDto.getName(), ids);
                Map<String, List<MultiLanguageBo>> listByText = remoteMultiLanguageService.listByTextLike(query, "zh");
                List<MultiLanguageBo> langIdlist = listByText.get(pageDto.getName());
                List<Long> langIds = langIdlist.stream().map(MultiLanguageBo::getLangId).collect(Collectors.toList());
                if (!CollectionUtils.isEmpty(langIds)) {
                    typeName = 1;
                    ruleEngineLambdaQueryWrapper.in(RuleEngine::getName, langIds);
                } else {
                    typeName = 0;
                }
            }
            ruleEngineLambdaQueryWrapper.like(null != pageDto.getId(), RuleEngine::getId, pageDto.getId());
            ruleEngineLambdaQueryWrapper.eq(null != pageDto.getType(), RuleEngine::getPushType, pageDto.getType());
            ruleEngineLambdaQueryWrapper.in(!CollectionUtils.isEmpty(messageTemplateBoList), RuleEngine::getMessageTemplateId,
                    messageTemplateBoList.stream().map(MessageTemplateBo::getId).collect(Collectors.toSet()));
            Set<Long> idSet = ruleEnginePushMethodList.stream().map(RuleEnginePushMethod::getRuleEngineId).collect(Collectors.toSet());
            if (null != pageDto.getPushMethod()) {
                if (!CollectionUtils.isEmpty(idSet)) {
                    ruleEngineLambdaQueryWrapper.in(RuleEngine::getId, idSet);
                } else {
                    ruleEngineLambdaQueryWrapper.isNull(RuleEngine::getId);
                }
            }
            ruleEngineLambdaQueryWrapper.eq(null != typeTitle && typeTitle == 0, RuleEngine::getId, null);
            ruleEngineLambdaQueryWrapper.eq(null != typeName && typeName == 0, RuleEngine::getId, null);
            // 根据创建时间倒序排序
            ruleEngineLambdaQueryWrapper.orderByDesc(RuleEngine::getCreateTime);

            if (StringUtils.isNotBlank(pageDto.getBusinessTypeStr())) {
                ruleEngineLambdaQueryWrapper.eq(RuleEngine::getBusinessType, pageDto.getBusinessTypeStr());
            }

            IPage<RuleEngine> page = this.getBaseMapper()
                    .selectPage(new Page<>(pageDto.getPageNum(), pageDto.getPageSize()), ruleEngineLambdaQueryWrapper);
            List<RuleEnginePageVo> ruleEnginePageVoList = new ArrayList<>(pageDto.getPageSize());

            page.getRecords().forEach(ruleEngine -> {
                RuleEnginePageVo ruleEnginePageVo = ConvertUtil.convert(ruleEngine, RuleEnginePageVo.class);
                ruleEnginePageVo.setBusinessType(Stream.of(ruleEngine.getBusinessType().split(",")).map(Integer::valueOf).collect(Collectors.toList()));
                ruleEnginePageVo.setPushType(ruleEngine.getPushType().toString());
                // 规则名称多语言
                MultiLanguageBo nameBo = remoteMultiLanguageService.getById(ruleEngine.getName().toString());
                MultiLanguageVo nameVo = new MultiLanguageVo(nameBo.getLangId(),
                        com.chervon.technology.config.MultiLanguageUtil.getByLangCode(nameBo.getLangCode(), LocaleContextHolder.getLocale().getLanguage()));
                ruleEnginePageVo.setName(nameVo);
                // 消息模板多语言
                MessageTemplateBo messageTemplateBo = remoteMessageTemplateService
                        .get(LocaleContextHolder.getLocale().getLanguage(), ruleEngine.getMessageTemplateId());
                if (null != messageTemplateBo) {
                    ruleEnginePageVo.setMessageTemplateTitle(messageTemplateBo.getTitle());
                    ruleEnginePageVo.setType(messageTemplateBo.getType().toString());
                }
                // 推送方式列表
                LambdaQueryWrapper<RuleEnginePushMethod> wrapper = new LambdaQueryWrapper<RuleEnginePushMethod>()
                        .eq(RuleEnginePushMethod::getRuleEngineId, ruleEngine.getId());
                List<PushMethodEnum> pushMethodList = ruleEnginePushMethodService.list(wrapper)
                        .stream().map(RuleEnginePushMethod::getPushMethod).collect(Collectors.toList());
                ruleEnginePageVo.setPushMethod(pushMethodList);

                ruleEnginePageVoList.add(ruleEnginePageVo);
            });
            result = new PageResult<>(page.getCurrent(), page.getSize(), page.getTotal());
            result.setList(ruleEnginePageVoList);
        } catch (Exception e) {
            throw new BaseException(e.getMessage());
        }
        return result;
    }
}
