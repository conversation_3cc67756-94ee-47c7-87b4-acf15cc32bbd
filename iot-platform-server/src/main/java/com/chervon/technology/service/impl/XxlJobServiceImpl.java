package com.chervon.technology.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.chervon.common.core.utils.BeanCopyUtils;
import com.chervon.common.mybatis.config.BaseDo;
import com.chervon.fleet.web.api.entity.dto.ProductFaultDto;
import com.chervon.fleet.web.api.service.RemoteProductFaultService;
import com.chervon.operation.api.RemoteMessageTemplateService;
import com.chervon.operation.api.RemoteSuggestionService;
import com.chervon.technology.config.RnReleaseStateEnum;
import com.chervon.technology.domain.dataobject.FaultDictionary;
import com.chervon.technology.domain.dataobject.ReleaseRecord;
import com.chervon.technology.domain.dto.fault.message.FaultInfoDto;
import com.chervon.technology.entity.ProductRn;
import com.chervon.technology.mapper.FaultMessageMapper;
import com.chervon.technology.mapper.ReleaseRecordMapper;
import com.chervon.technology.service.*;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2023-01-31 14:17
 **/
@Slf4j
@Service
public class XxlJobServiceImpl implements XxlJobService {
    @Autowired
    OtaJobService otaJobService;
    @Autowired
    private ProductRnService productRnService;
    @Autowired
    private ReleaseRecordMapper releaseRecordMapper;
    @Autowired
    private FaultMessageMapper faultMessageMapper;
    @Autowired
    private FaultDictionaryService faultDictionaryService;
    @DubboReference
    private RemoteMessageTemplateService remoteMessageTemplateService;
    @DubboReference
    private RemoteProductFaultService remoteProductFaultService;
    @DubboReference
    private RemoteSuggestionService remoteSuggestionService;
    /**
     * 刷新任务执行状态，每分钟执行一次
     *
     * <AUTHOR>
     * @date 17:47 2022/9/2
     **/
    @Override
    @XxlJob("refreshJobStatusSchedule")
    public void refreshJobStatusSchedule() {
        otaJobService.refreshJobStatus();
        log.info("更新job发布状态");
    }

    /**
     * 定时同步product_fault表,涉及以下方法
     * FaultMessageMapper#listProductFault
     * RemoteProductFaultServiceImpl#save
     * RemoteMessageTemplateServiceImpl#listTitleForProductFault
     * RemoteMultiLanguageServiceImpl#listLanguageContentForAllLanguagesByLangIds
     */
    @Override
    public List<ProductFaultDto> syncProductFault() {
        List<FaultInfoDto> productFaultDtos = faultMessageMapper.listProductFault();
        Set<Long> messageTemplateIdSet = productFaultDtos.stream().map(FaultInfoDto::getMessageTemplateId).collect(Collectors.toSet());
        log.debug("syncProductFault -> 三表联查获取消息模板id列表,messageTemplateIdSet.size(): {}", messageTemplateIdSet.size());
        // key-消息模板ID, value-(key-语种 value-多语言内容)
        Map<Long, Map<String, String>> msgTemplateMap = remoteMessageTemplateService.listTitleForProductFault(messageTemplateIdSet);

        final List<Long> listSuggestionId = productFaultDtos.stream().filter(a->!Objects.isNull(a.getSuggestionId()))
                .map(FaultInfoDto::getSuggestionId).distinct().collect(Collectors.toList());
        final Map<Long, Map<String, String>> suggestionContentMap = remoteSuggestionService.listContentForLanguages(listSuggestionId, Arrays.asList("en", "de", "es", "nl", "fr"));
        List<ProductFaultDto> target = new ArrayList<>();
        for (FaultInfoDto productFaultDto : productFaultDtos) {
            final Map<String, String> mapSuggestionContent = suggestionContentMap.get(productFaultDto.getSuggestionId());
            Map<String, String> mapTemplateCodeTitle = msgTemplateMap.getOrDefault(productFaultDto.getMessageTemplateId(), new HashMap<>());
            for (Map.Entry<String, String> entryCodeTitle : mapTemplateCodeTitle.entrySet()) {
                //增加多语言维度
                ProductFaultDto productFaultForLanguage = BeanCopyUtils.copy(productFaultDto, ProductFaultDto.class);
                productFaultForLanguage.setLanguage(entryCodeTitle.getKey());
                productFaultForLanguage.setFaultTitle(entryCodeTitle.getValue());
                if(!CollectionUtils.isEmpty(mapSuggestionContent)){
                    final String content = mapSuggestionContent.get(productFaultForLanguage.getLanguage());
                    productFaultForLanguage.setSuggestionContent(content);
                }
                productFaultForLanguage.setProductSnCode(productFaultDto.getProductSnCode());
                target.add(productFaultForLanguage);
            }
        }
        log.debug("syncProductFault -> target.size(): {}", target.size());
        remoteProductFaultService.save(target);
        return target;
    }

    @Override
    public List<FaultDictionary> syncFaultDictionary() {
        List<FaultInfoDto> productFaultDtos = faultMessageMapper.listProductFault();
        Set<Long> messageTemplateIdSet = productFaultDtos.stream().map(FaultInfoDto::getMessageTemplateId).collect(Collectors.toSet());
        // key-消息模板ID, value-(key-多语言类型 value-多语言内容)
        Map<Long, Map<String, String>> msgTemplateMap = remoteMessageTemplateService.listTitleForProductFault(messageTemplateIdSet);
        Map<Long, Map<String, String>> msgContentMap = remoteMessageTemplateService.listContentForProductFault(messageTemplateIdSet);
        List<FaultDictionary> listFaultDictionary = new ArrayList<>();
        for (FaultInfoDto productFaultDto : productFaultDtos) {
            Map<String, String> mapTemplateCodeTitle = msgTemplateMap.getOrDefault(productFaultDto.getMessageTemplateId(), new HashMap<>());
            for (Map.Entry<String, String> entryCodeTitle : mapTemplateCodeTitle.entrySet()) {
                FaultDictionary faultDictionary = BeanCopyUtils.copy(productFaultDto, FaultDictionary.class);
                faultDictionary.setLanguage(entryCodeTitle.getKey());
                faultDictionary.setFaultTitle(entryCodeTitle.getValue());
                faultDictionary.setModifyTime(System.currentTimeMillis());
                faultDictionary.setId();
                Map<String, String> contentMap = msgContentMap.getOrDefault(productFaultDto.getMessageTemplateId(), new HashMap<>());
                faultDictionary.setFaultDesc(contentMap.get(entryCodeTitle.getKey()));
                listFaultDictionary.add(faultDictionary);
            }
        }
        faultDictionaryService.saveOrUpdateBatch(listFaultDictionary);
        return listFaultDictionary;
    }
}
