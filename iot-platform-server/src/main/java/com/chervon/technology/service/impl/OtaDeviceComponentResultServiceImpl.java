package com.chervon.technology.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.chervon.common.core.domain.PageResult;
import com.chervon.common.core.utils.ConvertUtil;
import com.chervon.common.mybatis.config.BaseDo;
import com.chervon.common.redis.constant.RedisConstant;
import com.chervon.common.redis.utils.RedisUtils;
import com.chervon.technology.api.dto.ota.ComponentResultDto;
import com.chervon.technology.api.toruleengine.PackageResultDto;
import com.chervon.technology.domain.bo.DictBo;
import com.chervon.technology.domain.bo.DictNodeBo;
import com.chervon.technology.domain.dto.ota.ComponentOtaListDto;
import com.chervon.technology.domain.dto.ota.DeviceOtaDetailDto;
import com.chervon.technology.domain.entity.Firmware;
import com.chervon.technology.domain.entity.OtaDeviceComponentResult;
import com.chervon.technology.domain.enums.ComponentResultStatusEnum;
import com.chervon.technology.domain.vo.ota.ComponentOtaListVo;
import com.chervon.technology.domain.vo.ota.DeviceOtaDetailVo;
import com.chervon.technology.domain.vo.ota.DeviceOtaHistoryVo;
import com.chervon.technology.mapper.OtaDeviceComponentResultMapper;
import com.chervon.technology.service.DictService;
import com.chervon.technology.service.FirmwareService;
import com.chervon.technology.service.OtaDeviceComponentResultService;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.stereotype.Service;

import java.time.Duration;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-08-04
 */
@Service
public class OtaDeviceComponentResultServiceImpl extends ServiceImpl<OtaDeviceComponentResultMapper, OtaDeviceComponentResult> implements OtaDeviceComponentResultService {

    private final FirmwareService firmwareService;

    @Autowired
    private DictService dictService;

    public OtaDeviceComponentResultServiceImpl(FirmwareService firmwareService) {
        this.firmwareService = firmwareService;
    }

    private static final String C_OTA_FAIL_CODE = "c_ota_fail";

    @Override
    public void updatePackageStatus(String deviceId, Long jobId,
                                    List<PackageResultDto> results) {
        for (PackageResultDto resultDto : results) {
            Firmware firmware = firmwareService.getById(resultDto.getPackageId());
            Long updateJobId = jobId;
            if (firmware != null) {
                updateJobId = firmware.getJobId();
            }
            OtaDeviceComponentResult componentResult = new OtaDeviceComponentResult();
            componentResult.setDeviceId(deviceId);
            componentResult.setJobId(updateJobId);
            componentResult.setFirmwareId(resultDto.getPackageId());
            componentResult.setDetail(resultDto.getDetail());
            componentResult.setStatus(resultDto.getStatus());
            LambdaUpdateWrapper<OtaDeviceComponentResult> wrapper = new LambdaUpdateWrapper<>();
            wrapper.eq(OtaDeviceComponentResult::getDeviceId, deviceId).
                    eq(OtaDeviceComponentResult::getJobId, updateJobId).
                    eq(OtaDeviceComponentResult::getFirmwareId, resultDto.getPackageId());
            this.update(componentResult, wrapper);
            //缓存升级期望时间
            String key = RedisConstant.OTA_RESULT + deviceId + "_" + jobId + "_" + resultDto.getPackageId();
            ComponentResultDto componentResultDto = new ComponentResultDto();
            componentResultDto.setSysReportTime(System.currentTimeMillis() / 1000);
            componentResultDto.setUpgradeStartTime(resultDto.getUpgradeStartTime());
            componentResultDto.setUpgradeExpectTime(resultDto.getUpgradeExpectTime());
            RedisUtils.setCacheObject(key, componentResultDto, Duration.ofDays(1));
        }
    }

    @Override
    public PageResult<DeviceOtaDetailVo> getOtaDetail(DeviceOtaDetailDto detailDto) {
        List<DictBo> dictList = dictService.listByDictName(LocaleContextHolder.getLocale().getLanguage(), Collections.singletonList(C_OTA_FAIL_CODE));
        Map<String, DictBo> collect = dictList.stream().collect(Collectors.toMap(DictBo::getDictName, Function.identity()));
        LambdaQueryWrapper<OtaDeviceComponentResult> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(OtaDeviceComponentResult::getJobId, detailDto.getJobId())
                .eq(OtaDeviceComponentResult::getDeviceId, detailDto.getDeviceId());
        Page<OtaDeviceComponentResult> page = new Page<>(detailDto.getPageNum(), detailDto.getPageSize());
        Page<OtaDeviceComponentResult> result = this.page(page, wrapper);
        PageResult<DeviceOtaDetailVo> pageResult = new PageResult<>(detailDto.getPageNum(),
                detailDto.getPageSize());
        pageResult.setPages(result.getPages());
        pageResult.setTotal(result.getTotal());
        List<DeviceOtaDetailVo> deviceOtaDetailVos = new ArrayList<>();
        result.getRecords().forEach(otaResult -> {
            DeviceOtaDetailVo convert = ConvertUtil.convert(otaResult,
                    DeviceOtaDetailVo.class);
            if (!ComponentResultStatusEnum.FAILED.getStatus().equals(otaResult.getStatus())) {
                convert.setDetail("");
            } else {
                // 设置数据来源
                convert.setDetail(collect.get(C_OTA_FAIL_CODE).getNodes()
                        .stream()
                        .filter(i -> StringUtils.equals(i.getLabel(), otaResult.getDetail()))
                        .findFirst()
                        .orElse(new DictNodeBo())
                        .getDescription());
            }
            convert.setUpgradeTime(otaResult.getUpdateTime());
            convert.setStatusLabel(ComponentResultStatusEnum.valueOfStatus(otaResult.getStatus()));
            deviceOtaDetailVos.add(convert);
        });
        pageResult.setList(deviceOtaDetailVos);
        return pageResult;
    }

    @Override
    public PageResult<ComponentOtaListVo> getComponentOtaList(ComponentOtaListDto dto) {
        // 结束时间加23小时59秒59秒
        if (dto.getEndTime() != null) {
            dto.getEndTime().setTime(dto.getEndTime().getTime() + 23 * 60 * 60 * 1000 + 59 * 60 * 1000 + 59 * 1000 - (long) dto.getZone() * 60 * 60 * 1000);
        }
        if (dto.getStartTime() != null) {
            dto.getStartTime().setTime(dto.getStartTime().getTime() - (long) dto.getZone() * 60 * 60 * 1000);
        }
        LambdaQueryWrapper<OtaDeviceComponentResult> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(OtaDeviceComponentResult::getComponentNo, dto.getComponentNo()).
                eq(OtaDeviceComponentResult::getDeviceId, dto.getDeviceId())
                .orderByDesc(BaseDo::getCreateTime);
        if (dto.getStatus() != null) {
            wrapper.eq(OtaDeviceComponentResult::getStatus, dto.getStatus());
        }
        if (StringUtils.isNotBlank(dto.getNewVersion())) {
            wrapper.like(OtaDeviceComponentResult::getNewVersion, dto.getNewVersion());
        }
        if (StringUtils.isNotBlank(dto.getOldVersion())) {
            wrapper.like(OtaDeviceComponentResult::getOldVersion, dto.getOldVersion());
        }
        if (dto.getStartTime() != null) {
            wrapper.ge(OtaDeviceComponentResult::getCreateTime, dto.getStartTime());
        }
        if (dto.getEndTime() != null) {
            wrapper.le(OtaDeviceComponentResult::getCreateTime, dto.getEndTime());
        }
        if (dto.getUserId() != null) {
            try {
                Long userId = Long.valueOf(dto.getUserId());
                wrapper.like(OtaDeviceComponentResult::getAppUserId, userId);
            } catch (Exception e) {
                return new PageResult<>(dto.getPageNum(), dto.getPageSize(), 0);
            }
        }
        Page<OtaDeviceComponentResult> page = new Page<>(dto.getPageNum(), dto.getPageSize());
        Page<OtaDeviceComponentResult> result = this.page(page, wrapper);
        PageResult<ComponentOtaListVo> pageResult = new PageResult<>(dto.getPageNum(), dto.getPageSize());
        pageResult.setPages(result.getPages());
        pageResult.setTotal(result.getTotal());
        List<ComponentOtaListVo> deviceOtaHistoryVos = new ArrayList<>();
        result.getRecords().forEach(otaResult -> {
            ComponentOtaListVo convert = ConvertUtil.convert(otaResult, ComponentOtaListVo.class);
            convert.setUpgradeTime(otaResult.getUpdateTime());
            convert.setUserId(otaResult.getAppUserId() == null ? null : otaResult.getAppUserId() + "");
            deviceOtaHistoryVos.add(convert);
        });
        ConvertUtil.convertList(result.getRecords(),
                DeviceOtaHistoryVo.class);
        pageResult.setList(deviceOtaHistoryVos);
        return pageResult;
    }

    @Override
    public List<OtaDeviceComponentResult> getDeviceComponentList(DeviceOtaDetailDto detailDto) {
        LambdaQueryWrapper<OtaDeviceComponentResult> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(OtaDeviceComponentResult::getJobId, detailDto.getJobId())
                .eq(OtaDeviceComponentResult::getDeviceId, detailDto.getDeviceId())
                .select(OtaDeviceComponentResult::getDeviceId, OtaDeviceComponentResult::getJobId, OtaDeviceComponentResult::getFirmwareId,
                        OtaDeviceComponentResult::getStatus, OtaDeviceComponentResult::getDetail);
        return this.list(wrapper);
    }

    @Override
    public List<OtaDeviceComponentResult> listByJobIdWithDeviceId(Long jobId, String deviceId) {
        return list(Wrappers.<OtaDeviceComponentResult>lambdaQuery()
                .eq(OtaDeviceComponentResult::getJobId,jobId)
                .eq(OtaDeviceComponentResult::getDeviceId,deviceId)
        );
    }
}
