package com.chervon.technology.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.csp.sentinel.util.StringUtil;
import com.amazonaws.services.iot.model.JobExecutionStatus;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.chervon.common.core.constant.CommonConstant;
import com.chervon.common.core.domain.BaseRemoteReqDto;
import com.chervon.common.core.domain.LoginSysUser;
import com.chervon.common.core.domain.PageResult;
import com.chervon.common.core.domain.SingleInfoReq;
import com.chervon.common.core.prop.AwsProperties;
import com.chervon.common.core.utils.ConvertUtil;
import com.chervon.common.core.utils.JsonUtils;
import com.chervon.common.core.utils.StringUtils;
import com.chervon.common.oss.uitl.S3Util;
import com.chervon.common.oss.uitl.UrlUtil;
import com.chervon.common.redis.constant.RedisConstant;
import com.chervon.common.redis.utils.RedisUtils;
import com.chervon.common.sso.CurrentLoginUtil;
import com.chervon.configuration.api.core.MultiLanguageBo;
import com.chervon.configuration.api.service.RemoteMultiLanguageService;
import com.chervon.iot.app.api.RemoteAppUserStateService;
import com.chervon.iot.middle.api.dto.device.IotPublishDto;
import com.chervon.iot.middle.api.exception.IotMiddleException;
import com.chervon.iot.middle.api.pojo.ota.JobDocument;
import com.chervon.iot.middle.api.pojo.ota.PackageInfo;
import com.chervon.iot.middle.api.service.RemoteAwsGroupService;
import com.chervon.iot.middle.api.service.RemoteDeviceShadowService;
import com.chervon.iot.middle.api.service.RemoteOtaService;
import com.chervon.operation.api.RemoteBrandService;
import com.chervon.operation.api.RemoteCategoryService;
import com.chervon.operation.api.RemoteGroupService;
import com.chervon.operation.api.vo.BrandVo;
import com.chervon.operation.api.vo.CategoryVo;
import com.chervon.technology.api.dto.ota.JobReleaseListDto;
import com.chervon.technology.api.enums.OtaJobDevelopStatus;
import com.chervon.technology.api.enums.OtaJobReleaseStatus;
import com.chervon.technology.api.enums.PackageTypeEnum;
import com.chervon.technology.api.exception.TechnologyErrorCode;
import com.chervon.technology.api.toruleengine.*;
import com.chervon.technology.api.vo.job.JobInfoVo;
import com.chervon.technology.api.vo.job.PackageInfoVo;
import com.chervon.technology.api.vo.ota.JobDetailVo;
import com.chervon.technology.api.vo.ota.JobReleaseDetailVo;
import com.chervon.technology.api.vo.ota.JobReleaseListVo;
import com.chervon.technology.config.ExceptionMessageUtil;
import com.chervon.technology.config.MultiLanguageUtil;
import com.chervon.technology.config.OtaOperationConfig;
import com.chervon.technology.domain.dataobject.*;
import com.chervon.technology.domain.dto.firmware.ListVersionDto;
import com.chervon.technology.domain.dto.ota.*;
import com.chervon.technology.domain.entity.*;
import com.chervon.technology.domain.vo.component.ComponentVersionVo;
import com.chervon.technology.domain.vo.component.ComponentVo;
import com.chervon.technology.domain.vo.ota.JobListVo;
import com.chervon.technology.domain.vo.ota.PreSignedUrlVo;
import com.chervon.technology.mapper.OtaJobMapper;
import com.chervon.technology.service.*;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.redisson.api.RLock;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.io.IOException;
import java.time.*;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeFormatterBuilder;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <p>
 * 升级任务 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-07-29
 */
@Service
@Slf4j
public class OtaJobServiceImpl extends ServiceImpl<OtaJobMapper, OtaJob> implements OtaJobService {

    public static final Long HALF_HOUR_MILLIS = 30 * 60 * 1000L;
    public static final String FILE_SPLIT_CODE = "/";
    public static final String HYPHEN_CODE = "_";
    private static final String URL_GET_ACCEPTED = "aws/things/%s/url/get/accepted";

    /**
     * 用户允许升级标识，兼容老设备
     */
    private static final String JOB_ACTION_GET_ACCEPTED = "aws/things/%s/jobs/%d/action/get/accepted";

    /**
     *用户允许升级标识，新设备
     */
    private static final String JOB_ACTION_GET_ACCEPTED_V2 = "aws/things/%s/action/get/accepted/v2";

    public static final String DEVICE_OTA_RESULT_LOCK_PREFIX = "device_ota_result_lock:";

    public static final String DEVICE_OTA_RESULT_TIMESTAMP = "device_ota_result_timestamp:";

    @DubboReference
    RemoteDeviceShadowService remoteDeviceShadowService;

    @DubboReference
    RemoteAwsGroupService remoteAwsGroupService;

    @DubboReference
    RemoteOtaService remoteOtaService;

    @DubboReference
    private RemoteMultiLanguageService languageService;

    @DubboReference
    RemoteGroupService remoteGroupService;

    @Autowired
    private S3Util s3Util;

    @Autowired
    private OtaDeviceActionService otaDeviceActionService;

    @Autowired
    private FirmwareService firmwareService;

    @Resource
    private OtaJobMapper otaJobMapper;

    @Autowired
    private AwsProperties awsProperties;

    @Autowired
    private OtaOperationConfig otaOperationConfig;

    @Autowired
    private OtaJobGroupService otaJobGroupService;

    @Autowired
    private ComponentService componentService;

    @Autowired
    private OtaDeviceResultService otaDeviceResultService;

    @Autowired
    private DeviceService deviceService;

    @Autowired
    private OtaDeviceComponentResultService otaDeviceComponentResultService;

    @Autowired
    ProductComponentService productComponentService;

    @Autowired
    private ProductService productService;

    @DubboReference
    private RemoteCategoryService remoteCategoryService;

    @DubboReference
    private RemoteBrandService remoteBrandService;

    @DubboReference
    private RemoteAppUserStateService remoteAppUserStateService;

    @DubboReference
    private RemoteMultiLanguageService remoteMultiLanguageService;

    @Override
    public void getUrl(String deviceId, List<String> packageKeys) throws IOException {
        IotPublishDto iotPublishDto = new IotPublishDto();
        iotPublishDto.setTopic(String.format(URL_GET_ACCEPTED, deviceId));
        List<PreSignedUrlVo> urlVoList = new ArrayList<>();
        for (String key : packageKeys) {
            PreSignedUrlVo preSignedUrlVo = new PreSignedUrlVo();
            preSignedUrlVo.setKey(key);
            String preSignedUrl = UrlUtil.completeUrl(awsProperties.getOtaBucket().getCdnHost(), key);
            preSignedUrlVo.setPreSignedUrl(preSignedUrl);
            urlVoList.add(preSignedUrlVo);
        }
        iotPublishDto.setPayLoad(urlVoList);
        remoteDeviceShadowService.publish(iotPublishDto);
    }

    @Override
    public void reportJobAction(String deviceId, Long jobId, JobActionVo jobActionVo) {
        otaDeviceActionService.reportJobAction(deviceId, jobId, jobActionVo);
    }

    @Override
    public void getJobAction(String deviceId, Long jobId) {
        OtaDeviceAction otaDeviceAction = otaDeviceActionService.getJobAction(deviceId, jobId);

        //兼容老设备，组装发往老设备报文
        IotPublishDto iotPublishDtoV1=assembleJobActionV1Message(jobId,deviceId,otaDeviceAction);
        remoteDeviceShadowService.publish(iotPublishDtoV1);
        log.info("deviceId:{}, jobId:{}, publish:{}", deviceId, jobId,
                JsonUtils.toJsonString(iotPublishDtoV1));
        //新设备，组装发往新设备报文
        IotPublishDto iotPublishDtoV2=assembleJobActionV2Message(jobId,deviceId,otaDeviceAction);
        remoteDeviceShadowService.publish(iotPublishDtoV2);
        log.info("deviceId:{}, jobId:{}, publish:{}", deviceId, jobId,
                JsonUtils.toJsonString(iotPublishDtoV2));
    }

    /**
     * 组装发往老设备是否允许升级消息(老设备升级后删除)
     * @param jobId
     * @param deviceId
     * @param otaDeviceAction
     * @return
     */
    @Deprecated
    private IotPublishDto assembleJobActionV1Message(Long jobId,String deviceId,OtaDeviceAction otaDeviceAction) {
        IotPublishDto iotPublishDto=new IotPublishDto();
        iotPublishDto.setTopic(String.format(JOB_ACTION_GET_ACCEPTED, deviceId, jobId));
        JobActionVo jobActionVo = new JobActionVo();
        jobActionVo.setAllow(Objects.nonNull(otaDeviceAction)?otaDeviceAction.getAction():false);
        iotPublishDto.setPayLoad(jobActionVo);
        return iotPublishDto;
    }

    /**
     * 组装发往新设备是否允许升级消息
     * @param jobId
     * @param deviceId
     * @param otaDeviceAction
     * @return
     */
    private IotPublishDto assembleJobActionV2Message(Long jobId, String deviceId, OtaDeviceAction otaDeviceAction) {
        IotPublishDto iotPublishDtoV2=new IotPublishDto();
        iotPublishDtoV2.setTopic(String.format(JOB_ACTION_GET_ACCEPTED_V2, deviceId));
        JobActionVoV2 jobActionVoV2 = new JobActionVoV2();
        //没有上报允许升级状态
        if(Objects.isNull(otaDeviceAction)){
            jobActionVoV2.setAllow(false);
        }else{
            //上报过允许升级状态且允许升级
            if(otaDeviceAction.getAction()){
                JobInfoVo jobInfoVo=getJobInfoByJobIdWithDeviceId(jobId,deviceId);
                jobActionVoV2.setJobInfo(jobInfoVo);
            }
            jobActionVoV2.setMessageId(otaDeviceAction.getMessageId());
            jobActionVoV2.setTimestamp(otaDeviceAction.getMessageTime());
            jobActionVoV2.setAllow(otaDeviceAction.getAction());

        }
        iotPublishDtoV2.setPayLoad(jobActionVoV2);
        return iotPublishDtoV2;
    }

    private JobInfoVo getJobInfoByJobIdWithDeviceId(Long jobId, String deviceId) {
        JobInfoVo jobInfoVo = null;
        OtaJob otaJob=getById(jobId);
        if(Objects.nonNull(otaJob)){
             jobInfoVo=new JobInfoVo();
             //查询总成升级记录
             List<OtaDeviceComponentResult> otaDeviceComponentResults=otaDeviceComponentResultService.listByJobIdWithDeviceId(jobId,deviceId);
             List<PackageInfoVo> packages=new ArrayList<>();
             for(OtaDeviceComponentResult otaDeviceComponentResult:otaDeviceComponentResults){
                 //查询固件信息
                 Firmware firmware=firmwareService.getById(otaDeviceComponentResult.getFirmwareId());
                 //查询总成零件信息
                 Component component=componentService.getByNo(otaDeviceComponentResult.getComponentNo());

                 PackageInfoVo packageInfoVo=ConvertUtil.convert(firmware, PackageInfoVo.class);
                 packageInfoVo.setPackageId(firmware.getId().toString());
                 packageInfoVo.setComponentType(component.getComponentType());
                 packageInfoVo.setPackageType(firmware.getPackageType().getValue());
                 String firmUrl = UrlUtil.completeUrl(awsProperties.getOtaBucket().getCdnHost(), firmware.getPackageKey());
                 packageInfoVo.setPackageUrl(firmUrl);
                 packages.add(packageInfoVo);
             }
             //因设备端处理不了大数值，jobId长整型转成字符串，返回给设备
             jobInfoVo.setJobIds(Arrays.asList(String.valueOf(jobId)));
             jobInfoVo.setPackageCount(otaDeviceComponentResults.size());
             jobInfoVo.setPackages(packages);
             jobInfoVo.setUpgradeMode(otaJob.getUpgradeMode().getValue());

        }else{
            log.error("getJobInfoByJobIdWithDeviceId otaDeviceResult jobId:{}, otaJob is empty.", jobId);
        }

        return jobInfoVo;
    }


    @Transactional(rollbackFor = Exception.class)
    @Override
    public void add(JobAddDto jobAddDto) {
        // 保存任务信息
        OtaJob otaJob = ConvertUtil.convert(jobAddDto, OtaJob.class);
        otaJob.setDevelopStatus(OtaJobDevelopStatus.DEVELOPING);
        otaJob.setReleaseStatus(OtaJobReleaseStatus.INIT);
        this.save(otaJob);

        // 保存固件信息
        List<FirmwareDto> firmwares = jobAddDto.getFirmwares();
        List<Firmware> firmwareList = new ArrayList<>();
        String technologyVersion = getTechnologyVersion(otaJob.getProductId(), otaJob.getId());
        otaJob.setTechnologyVersion(technologyVersion);
        String customVersion = getCustomVersion(otaJob.getProductId(), LocalDateTime.now());
        otaJob.setCustomVersion(customVersion);
        otaJob.setPackageCount(firmwares.size());
        this.updateById(otaJob);
        for (FirmwareDto firmwareDto : firmwares) {
            Firmware convert = ConvertUtil.convert(firmwareDto, Firmware.class);
            convert.setProductId(jobAddDto.getProductId());
            convert.setJobId(otaJob.getId());
            firmwareList.add(convert);
        }
        firmwareService.saveBatch(firmwareList);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void edit(JobEditDto jobEditDto) {
        // 更新任务信息
        List<FirmwareDto> firmwares = jobEditDto.getFirmwares();
        List<Firmware> firmwareList = new ArrayList<>();
        OtaJob oldJob = this.getById(jobEditDto.getJobId());
        if (!(oldJob.getDevelopStatus().equals(OtaJobDevelopStatus.DEVELOPING))) {
            throw ExceptionMessageUtil.getException(TechnologyErrorCode.JOB_NOT_ALLOWED_EDIT, jobEditDto.getJobId());
        }
        for (FirmwareDto firmwareDto : firmwares) {
            Firmware convert = ConvertUtil.convert(firmwareDto, Firmware.class);
            convert.setProductId(oldJob.getProductId());
            convert.setJobId(oldJob.getId());
            firmwareList.add(convert);
        }
        OtaJob otaJob = ConvertUtil.convert(jobEditDto, OtaJob.class);
        otaJob.setProductId(oldJob.getProductId());
        otaJob.setPackageCount(firmwares.size());
        otaJob.setId(jobEditDto.getJobId());
        this.updateById(otaJob);
        // 更新固件信息
        // 1. 先删除该任务下老的固件
        firmwareService.removeByJobId(jobEditDto.getJobId());
        // 1. 保存新固件列表
        firmwareService.saveBatch(firmwareList);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delete(SingleInfoReq<Long> jobId) {
        OtaJob otaJob = this.getById(jobId.getReq());
        if (!(otaJob.getDevelopStatus().equals(OtaJobDevelopStatus.DEVELOPING))) {
            throw ExceptionMessageUtil.getException(TechnologyErrorCode.JOB_NOT_ALLOWED_DELETE, jobId.getReq());
        }
        firmwareService.removeByJobId(jobId.getReq());
        if (otaJob != null) {
            this.removeById(jobId.getReq());
            if (otaJob.getReleaseContent() != null) {
                remoteMultiLanguageService.deleteByLangIds(Arrays.asList(Long.valueOf(otaJob.getReleaseContent())));
            }
        }
    }

    @Override
    public PreSignedUrlVo getUploadUrl(UploadUrlDto uploadUrlDto) {
        long currentTimeMillis = System.currentTimeMillis();
        Date expiration = new Date(currentTimeMillis + HALF_HOUR_MILLIS);
        String productModel = uploadUrlDto.getProductModel();
        String componentNo = uploadUrlDto.getComponentNo();
        String fileName = uploadUrlDto.getFileName();
        String key = productModel + FILE_SPLIT_CODE
                + componentNo + FILE_SPLIT_CODE
                + currentTimeMillis + HYPHEN_CODE
                + fileName;
        String preSignedUrl = s3Util.getPreSignedPutPublicUrl(awsProperties.getOtaBucket().getName(), key, expiration);
        PreSignedUrlVo preSignedUrlVo = new PreSignedUrlVo();
        preSignedUrlVo.setKey(key);
        preSignedUrlVo.setPreSignedUrl(preSignedUrl);
        return preSignedUrlVo;
    }

    @Override
    public String getDownloadUrl(String key) {
        return UrlUtil.completeUrl(awsProperties.getOtaBucket().getCdnHost(), key);
    }

    @Override
    public PageResult<JobListVo> listPage(JobListDto jobListDto) {
        Page<OtaJob> page = new Page<>(jobListDto.getPageNum(), jobListDto.getPageSize());
        //分页查询
        Page<OtaJob> pageResult = this.page(page,
                Wrappers.<OtaJob>lambdaQuery()
                        .eq(Objects.nonNull(jobListDto.getProductId()), OtaJob::getProductId, jobListDto.getProductId())
                        .like(StringUtils.isNotEmpty(jobListDto.getJobId()), OtaJob::getId, jobListDto.getJobId())
                        .like(StringUtils.isNotEmpty(jobListDto.getTechnicalVersion()),OtaJob::getTechnologyVersion,jobListDto.getTechnicalVersion())
                        .eq(Objects.nonNull(jobListDto.getUpgradeMode()),OtaJob::getUpgradeMode, jobListDto.getUpgradeMode())
                        .eq(Objects.nonNull(jobListDto.getDevelopStatus()),OtaJob::getDevelopStatus, jobListDto.getDevelopStatus())
                        .ge(StringUtils.isNotBlank(jobListDto.getCreateStartTime()),OtaJob::getCreateTime, jobListDto.getCreateStartTime())
                        .le(StringUtils.isNotBlank(jobListDto.getCreateEndTime()),OtaJob::getCreateTime, jobListDto.getCreateEndTime())
                        .ge(StringUtils.isNotBlank(jobListDto.getUpdateStartTime()),OtaJob::getUpdateTime, jobListDto.getUpdateStartTime())
                        .le(StringUtils.isNotBlank(jobListDto.getUpdateEndTime()),OtaJob::getUpdateTime, jobListDto.getUpdateEndTime())
                        .orderByDesc(OtaJob::getCreateTime)
        );

        List<JobListVo> jobListVos = new ArrayList<>();
        pageResult.getRecords().forEach(otaJob -> {
            JobListVo convert = ConvertUtil.convert(otaJob, JobListVo.class);
            convert.setJobId(otaJob.getId());
            convert.setCanEnsureClose(
                    otaOperationConfig.hasApproveClosePermission(otaJob.getDevelopStatus()));

            jobListVos.add(convert);
        });
        PageResult<JobListVo> result = new PageResult<>(jobListDto.getPageNum(),
                jobListDto.getPageSize());
        result.setPages(pageResult.getPages());
        result.setTotal(pageResult.getTotal());
        result.setList(jobListVos);
        return result;
    }

    @Override
    public JobDetailVo getDetail(Long jobId) {
        JobDetailVo detail = otaJobMapper.getDetail(jobId);
        if (detail == null) {
            return new JobDetailVo();
        }
        if (StringUtils.isNotEmpty(detail.getCategoryName())) {
            Long categoryId = Long.valueOf(detail.getCategoryName());
            CategoryVo categoryVo = remoteCategoryService.getDetail(new BaseRemoteReqDto<>(LocaleContextHolder.getLocale(), categoryId));
            detail.setCategoryName(categoryVo.getCategoryName().getMessage());
        }
        if (StringUtils.isNotEmpty(detail.getBrandName())) {
            BrandVo brandVo = remoteBrandService.getDetail(new BaseRemoteReqDto<>(LocaleContextHolder.getLocale(), Long.valueOf(detail.getBrandName())));
            detail.setBrandName(brandVo.getBrandName().getMessage());
        }
        return detail;
    }

    @Override
    public List<JobReleaseListVo> listReleaseByStatusList(Long productId, List<OtaJobReleaseStatus> releaseStatusList) {
        return otaJobMapper.listReleaseByStatusList(productId, releaseStatusList);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateDevelopStatus(JobStatusDto jobStatusDto) {
        Long jobId = jobStatusDto.getJobId();
        OtaJobDevelopStatus status = jobStatusDto.getStatus();
        OtaJob job = this.getById(jobId);
        if (job == null) {
            throw ExceptionMessageUtil.getException(TechnologyErrorCode.JOB_NOT_EXISTED, jobId);
        }
        OtaJobDevelopStatus sourceStatus = job.getDevelopStatus();
        // 操作：确认封板
        OtaJob otaJob = new OtaJob();
        otaJob.setId(jobId);
        otaJob.setDevelopStatus(status);
        switch (status) {
            case CLOSED:
                if (!otaOperationConfig.hasApproveClosePermission(sourceStatus)) {
                    throw ExceptionMessageUtil.getException(TechnologyErrorCode.JOB_NOT_ALLOWED_APPROVE);
                }
                // 修改发布状态为待发布
                otaJob.setReleaseStatus(OtaJobReleaseStatus.RELEASE_WAITING);
                break;
            default:
                throw ExceptionMessageUtil.getException(TechnologyErrorCode.JOB_STATUS_INVALID, status);
        }
        this.updateById(otaJob);

    }

    @Override
    public List<String> listVersions(ListVersionDto listVersionDto) {
        return firmwareService.listVersions(listVersionDto);
    }

    @Override
    public JobDocument getJobDocument(Long jobId) {
        OtaJob otaJob = this.getById(jobId);
        JobDocument jobDocument = ConvertUtil.convert(otaJob, JobDocument.class);
        jobDocument.setRemark(otaJob.getReleaseContent());
        jobDocument.setUpgradeMode(otaJob.getUpgradeMode().getValue());
        List<Firmware> firmwares = firmwareService.listByJobId(jobId);
        List<PackageInfo> packageInfos = new ArrayList<>();
        for (Firmware firmware : firmwares) {
            PackageInfo packageInfo = ConvertUtil.convert(firmware, PackageInfo.class);
            packageInfo.setPackageId(firmware.getId().toString());
            packageInfos.add(packageInfo);
        }
        jobDocument.setPackages(packageInfos);
        return jobDocument;
    }

    @Override
    public PageResult<JobReleaseListVo> pageRelease(JobReleaseListDto jobReleaseListDto) {
        IPage<JobReleaseListVo> result = otaJobMapper.pageRelease(new Page<>(jobReleaseListDto.getPageNum(), jobReleaseListDto.getPageSize()), jobReleaseListDto);
        buildJobReleaseListVo(result.getRecords());
        PageResult<JobReleaseListVo> pageResult = new PageResult<>(result.getCurrent(),
                result.getSize(), result.getTotal());
        pageResult.setPages(result.getPages());
        pageResult.setList(result.getRecords());
        return pageResult;
    }

    @Override
    public List<JobReleaseListVo> listRelease(JobReleaseListDto jobReleaseListDto) {
        List<JobReleaseListVo> records = otaJobMapper.listRelease(jobReleaseListDto);
        buildJobReleaseListVo(records);
        return records;
    }

    private void buildJobReleaseListVo(List<JobReleaseListVo> records) {
        if (CollectionUtil.isNotEmpty(records)) {
            LoginSysUser loginUser = CurrentLoginUtil.getCurrent();
            String userGuid = loginUser.getLdapUserGuid();
            for (JobReleaseListVo vo : records) {
                vo.setCanEnsureRelease(
                        otaOperationConfig.hasConfirmReleasePermission(vo.getReleaseStatus())
                );
                vo.setCanRefuseRelease(
                        otaOperationConfig.hasRefuseReleasePermission(vo.getReleaseStatus())
                );
                vo.setCanTestEnsureRelease(
                        otaOperationConfig.hasTestConfirmReleasePermission(vo.getReleaseStatus())
                );
                vo.setCanTestRefuseRelease(
                        otaOperationConfig.hasTestRefuseReleasePermission(vo.getReleaseStatus())
                );
                vo.setCanEnsureStopRelease(
                        otaOperationConfig.hasApproveConfirmStopReleasePermission(vo.getReleaseStatus())
                );
                vo.setCanRefuseStopRelease(
                        otaOperationConfig.hasApproveRefuseStopReleasePermission(vo.getReleaseStatus())
                );
                vo.setCanViewApplyRefuseReason(OtaJobReleaseStatus.RELEASE_REFUSED.equals(vo.getReleaseStatus()));
                vo.setCanViewTestRefuseReason(OtaJobReleaseStatus.RELEASE_TEST_REFUSED.equals(vo.getReleaseStatus()));
                vo.setCanViewStopRefuseReason(OtaJobReleaseStatus.STOP_REFUSED.equals(vo.getReleaseStatus()));

                vo.setCanViewReleaseDetail(OtaOperationConfig.viewReleaseDetailList.contains(vo.getReleaseStatus()));
                vo.setCanViewOtaResult(OtaOperationConfig.viewOtaResultList.contains(vo.getReleaseStatus()));

                vo.setCanNullifyRefused(OtaJobReleaseStatus.NULLIFY_IN_REVIEW.equals(vo.getReleaseStatus()));
                vo.setCanViewNullifyRefusedReason(OtaJobReleaseStatus.NULLIFY_REJECTED.equals(vo.getReleaseStatus()));
                vo.setCanNullifyPassed(OtaJobReleaseStatus.NULLIFY_IN_REVIEW.equals(vo.getReleaseStatus()));
            }
        }
    }

    @Override
    public JobReleaseDetailVo getReleaseDetail(Long jobId) {
        return otaJobMapper.getReleaseDetail(jobId);
    }

    @Override
    public void updateReleaseStatus(Long jobId, OtaJobReleaseStatus status) {
        OtaJob otaJob = new OtaJob();
        otaJob.setId(jobId);
        otaJob.setReleaseStatus(status);
        this.updateById(otaJob);
    }

    @Override
    public void updateCustomVersion(Long jobId, String customVersion) {
        OtaJob otaJob = new OtaJob();
        otaJob.setId(jobId);
        otaJob.setCustomVersion(customVersion);
        this.updateById(otaJob);
    }

    @Override
    public void updateTechnologyVersion(Long jobId, String technologyVersion) {
        OtaJob otaJob = new OtaJob();
        otaJob.setId(jobId);
        otaJob.setTechnologyVersion(technologyVersion);
        this.updateById(otaJob);
    }

    @Override
    public JobDocument check(String deviceId, Boolean singleMcu, Map<String, String> componentMap, String lang, String shortToken) {
        log.info("OtaJobServiceImpl check deviceId:{} start", deviceId);
        JobDocument jobDocument = new JobDocument();
        jobDocument.setPackageCount(0);
        // 1. 根据设备id获取分组名称列表
        List<String> groupNames = remoteAwsGroupService.listGroupNames(deviceId);
        if (CollectionUtil.isEmpty(componentMap)) {
            componentMap = getAllFirmwareVersion(deviceId);
        }
        log.info("OtaJobServiceImpl check deviceId:{},componentMap:{}", deviceId, JSONUtil.toJsonStr(componentMap));

        if (CollectionUtil.isEmpty(groupNames) || CollectionUtil.isEmpty(componentMap)) {
            jobDocument.setErrorMsg("groupNames or componentMap is null");
            return jobDocument;
        }
        // 2. 根据分组名称列表获取已发布或者测试分组测试中的job列表
        List<String> jobIds = otaJobGroupService.getReadyJobIds(groupNames, deviceId);
        if (CollectionUtil.isEmpty(jobIds)) {
            jobDocument.setErrorMsg("Can't find jobId of device");
            return jobDocument;
        }
        //根据shortToken获取userId
        Long userId = remoteAppUserStateService.getUserIdByShortToken(shortToken);

        List<PackageInfo> packageInfos = Lists.newArrayList();
        String latestJobId = "";
        for (String jobId : jobIds) {
            // 4. 根据job列表、总成零件号获取总成零件号、最新总成零件版本map
            if (singleMcu == null || singleMcu) {
                packageInfos = checkSingleMCU(componentMap, jobId, deviceId, userId);
            } else {
                packageInfos = checkDeltaPackage(componentMap, jobId, deviceId, userId);
            }
            if (CollectionUtil.isNotEmpty(packageInfos)) {
                latestJobId = jobId;
                break;
            }
        }
        if (StringUtils.isEmpty(latestJobId)) {
            jobDocument.setErrorMsg("Can't find Valid packageInfos");
            return jobDocument;
        }
        // 3. 根据最新的jobId，获取升级信息，组装JobDocument
        OtaJob otaJob = this.getById(latestJobId);
        log.info("OtaJobServiceImpl check deviceId:{},latestJobId:{}", deviceId, latestJobId);
        jobDocument = ConvertUtil.convert(otaJob, JobDocument.class);
        jobDocument.setPackages(packageInfos);
        jobDocument.setPackageCount(packageInfos.size());
        jobDocument.setTechnologyVersion(getTechnologyVersion(componentMap, otaJob.getProductId(), packageInfos));
        String langCode = "";
        if (StringUtils.isNotEmpty(otaJob.getReleaseContent())) {
            MultiLanguageBo languageBo = languageService.getById(otaJob.getReleaseContent());
            if (languageBo != null && languageBo.getLangId() != null) {
                langCode = languageBo.getLangCode();
                LocaleContextHolder.setLocale(new Locale(lang));
                String mapValue = MultiLanguageUtil.getByLangCode(languageBo.getLangCode());
                jobDocument.setRemark(mapValue);
            }
        }
        jobDocument.setUpgradeMode(otaJob.getUpgradeMode().getValue());
        jobDocument.setJobIds(Lists.newArrayList(latestJobId));
        // 生成版本号给app显示
        jobDocument.setCustomVersion(getCustomVersion(otaJob, packageInfos, componentMap));

        // 2. 根据设备分组名称列表及最新的job，获取最新job命中设备的分组名称
        String groupName = otaJobGroupService.getGroupNameByJobId(groupNames, otaJob.getId());
        //初始化设备OTA记录且设置是否有升级任务
        initOtaDeviceResult(deviceId, otaJob, groupName, userId, jobDocument, langCode);
        return jobDocument;
    }


    /**
     * 获取单MCU包
     */
    private List<PackageInfo> checkSingleMCU(Map<String, String> componentMap, String jobId, String deviceId, Long userId) {
        // 5. 单MCU升级包判断
        List<Firmware> firmwares = firmwareService.listLatestVersionByJobIds(Lists.newArrayList(jobId));
        List<PackageInfo> packageInfos = new ArrayList<>();
        for (Firmware firmware : firmwares) {
            String currentVersion = componentMap.get(firmware.getComponentNo());
            if (StringUtils.isEmpty(currentVersion)) {
                continue;
            }
            if (currentVersion.contains("_")) {
                currentVersion = currentVersion.split("_")[1];
            }
            if (currentVersion != null
                    && compare(currentVersion, firmware.getPackageVersion()) < 0
                    && compare(currentVersion, firmware.getMinimumVersion()) >= 0) {
                PackageInfo packageInfo = ConvertUtil.convert(firmware, PackageInfo.class);
                Component component = componentService.getByNo(packageInfo.getComponentNo());
                if (component == null) {
                    continue;
                }
                packageInfo.setPackageId(firmware.getId().toString());
                packageInfo.setCompatibleVersion(firmware.getMinimumVersion());
                String preSignedUrl = UrlUtil.completeUrl(awsProperties.getOtaBucket().getCdnHost(), packageInfo.getPackageKey());
                packageInfo.setPackageUrl(preSignedUrl);
                packageInfo.setComponentName(component.getComponentName());
                packageInfo.setComponentType(component.getComponentType());
                packageInfo.setPackageType(firmware.getPackageType().getValue());
                packageInfo.setDisplayVersion(firmware.getDisplayVersion());
                packageInfos.add(packageInfo);
                initOtaDeviceComponentResult(deviceId, firmware.getJobId(), firmware.getId(),
                        component.getComponentNo(), component.getComponentName(),
                        firmware.getPackageVersion(),
                        componentMap.get(component.getComponentNo()), userId);
            }
        }
        return packageInfos;
    }

    /**
     * 获取差分包
     */
    private List<PackageInfo> checkDeltaPackage(Map<String, String> componentMap, String jobId, String deviceId, Long userId) {
        // 5. 整包升级包判断
        Set<String> keys = componentMap.keySet();
        String componentNo = keys.iterator().next();
        String currentVersion = componentMap.get(componentNo);
        if (currentVersion.contains("_")) {
            currentVersion = currentVersion.split("_")[1];
        }
        Firmware firmware = firmwareService.getFirmwarePackage(jobId, componentNo);
        List<PackageInfo> packageInfos = new ArrayList<>();
        Component component = componentService.getByNo(componentNo);
        if (firmware == null) {
            return Collections.emptyList();
        } else if (compare(currentVersion, firmware.getPackageVersion()) < 0 && compare(currentVersion, firmware.getMinimumVersion()) >= 0) {
            PackageInfo packageInfo = convertFirmwareToPackageInfo(firmware, component);
            packageInfos.add(packageInfo);
        }
        initOtaDeviceComponentResult(deviceId, firmware.getJobId(), firmware.getId(), component.getComponentNo(),
                component.getComponentName(), firmware.getPackageVersion(), currentVersion, userId);
        //差分包
        List<Firmware> firmwareList = firmwareService.getFirmwareByJob(componentNo);
        if (CollectionUtil.isEmpty(firmwareList)) {
            return packageInfos;
        }
        for (Firmware fw : firmwareList) {
//	        if (!isFullPackage(fw)) {
//                continue;
//            }
            if (compare(fw.getPackageVersion(), firmware.getPackageVersion()) < 0 && compare(currentVersion, fw.getPackageVersion()) < 0
                    && compare(currentVersion, fw.getMinimumVersion()) >= 0) {
                packageInfos.add(convertFirmwareToPackageInfo(fw, component));
            }
        }
        if (packageInfos.size() > 1) {
            Collections.reverse(packageInfos);
        }

        return packageInfos;
    }


    /**
     * 是否满足大版本包
     */
    private boolean isFullPackage(Firmware fw) {
        if (fw == null) {
            return false;
        }
        if (fw.getPackageType() == null) {
            return false;
        }
        return PackageTypeEnum.FULL_PACKAGE.equals(fw.getPackageType());
    }

    private PackageInfo convertFirmwareToPackageInfo(Firmware firmware, Component component) {
        PackageInfo packageInfo = ConvertUtil.convert(firmware, PackageInfo.class);
        packageInfo.setPackageId(firmware.getId().toString());
        packageInfo.setCompatibleVersion(firmware.getMinimumVersion());
        String preSignedUrl = UrlUtil.completeUrl(awsProperties.getOtaBucket().getCdnHost(), packageInfo.getPackageKey());
        packageInfo.setPackageUrl(preSignedUrl);
        packageInfo.setComponentName(component.getComponentName());
        packageInfo.setComponentType(component.getComponentType());
        packageInfo.setPackageType(firmware.getPackageType().getValue());
        packageInfo.setDisplayVersion(firmware.getDisplayVersion());
        return packageInfo;
    }


    private Map<String, String> getAllFirmwareVersion(String deviceId) {
        String key = RedisConstant.OTA_DEVICE_COMPONENT_MAP + deviceId;
        Map<String, String> componentMap = RedisUtils.getCacheObject(key);
        if (CollectionUtil.isNotEmpty(componentMap)) {
            return componentMap;
        }
        return remoteOtaService.getAllFirmwareVersion(deviceId);
    }


    private int compare(String str1, String str2) {
        if (StringUtils.isEmpty(str2)) {
            return 1;
        }
        String[] str1Split = str1.split("\\.");
        String[] str2Split = str2.split("\\.");
        int lim = Math.min(str1Split.length, str2Split.length);
        for (int i = 0; i < lim; i++) {
            String splitStr1 = str1Split[i];
            String splitStr2 = str2Split[i];
            if (splitStr1.length() > splitStr2.length()) {
                return 1;
            }
            if (splitStr1.length() < splitStr2.length()) {
                return -1;
            }
            int compare = StringUtils.compare(splitStr1, splitStr2);
            if (compare == 0) {
                continue;
            }
            return compare;
        }
        return 0;
    }

    private String getCustomVersion(OtaJob otaJob, List<PackageInfo> packageInfos, Map<String, String> componentVersionMap) {

        Map<String, String> packageInfoMap = packageInfos.stream().collect(Collectors.toMap(PackageInfo::getComponentNo, PackageInfo::getPackageVersion, (k1, k2) -> k2));
        List<String> customVersionList = Lists.newArrayList();
        //组装显示版本号列表
        componentVersionMap.forEach((componentNo, version) -> {
            //设备总成存在于升级任务总成，以任务配置的版本号为准，不存在则以设备版本为准
            if (packageInfoMap.containsKey(componentNo)) {
                String firmwareVersion = packageInfoMap.get(componentNo);
                customVersionList.add(componentNo + "#" + firmwareVersion);
            } else {
                customVersionList.add(componentNo + "#" + version);
            }
        });
        Collections.sort(customVersionList);
        if  (customVersionList.size() == 1) {
            // 单MCU产品（如R），客户版本号显示MCU的完整版本号
            String version = customVersionList.get(0);
            return version.substring(version.indexOf(".") - 1);
        }
        return otaJob.getCustomVersion();
    }

    private void initOtaDeviceResult(String deviceId, OtaJob otaJob, String groupName, Long userId, JobDocument jobDocument, String langCode) {
        OtaDeviceResult oldOtaDeviceResult = otaDeviceResultService.getOne(
                Wrappers.<OtaDeviceResult>lambdaQuery()
                        .eq(OtaDeviceResult::getDeviceId, deviceId)
                        .eq(OtaDeviceResult::getJobId, otaJob.getId()));

        if (Objects.isNull(oldOtaDeviceResult)) {
            OtaDeviceResult otaDeviceResult = new OtaDeviceResult();
            otaDeviceResult.setDeviceId(deviceId);
            otaDeviceResult.setJobId(otaJob.getId());
            otaDeviceResult.setStatus(JobExecutionStatus.QUEUED.toString());
            Device device = deviceService.getCurrentFirmwareVersion(deviceId);
            if(Objects.nonNull(device)){
                otaDeviceResult.setOldVersion(device.getCustomVersion());
                otaDeviceResult.setOldTechnologyVersion(device.getTechnologyVersion());
            }
            otaDeviceResult.setNewVersion(jobDocument.getCustomVersion());
            otaDeviceResult.setContent(langCode);
            otaDeviceResult.setGroupName(groupName);
            otaDeviceResult.setAppUserId(userId);
            otaDeviceResultService.save(otaDeviceResult);
        }
        //设置可以升级
        jobDocument.setHasOtaJob(true);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void refreshJobStatus() {
        LambdaUpdateWrapper<OtaJob> startWrapper = new LambdaUpdateWrapper<>();
        startWrapper.le(OtaJob::getStartTime, new Date()).eq(OtaJob::getReleaseStatus,
                OtaJobReleaseStatus.RELEASE_READY);
        startWrapper.set(OtaJob::getReleaseStatus, OtaJobReleaseStatus.RELEASED);
        startWrapper.set(OtaJob::getUpdateTime, LocalDateTime.now());
        List<OtaJob> startList = this.list(startWrapper);
        if (CollectionUtil.isNotEmpty(startList)) {
            for (OtaJob otaJob : startList) {
                // 发布到正式分组
                publishJob(otaJob.getId(), otaJob.getProductId(), false);
            }
        }
        startWrapper.set(OtaJob::getUpdateBy, "xxl-job");
        this.update(startWrapper);
        LambdaUpdateWrapper<OtaJob> endWrapper = new LambdaUpdateWrapper<>();
        endWrapper.le(OtaJob::getEndTime, new Date()).eq(OtaJob::getReleaseStatus,
                OtaJobReleaseStatus.RELEASED);
        endWrapper.set(OtaJob::getReleaseStatus, OtaJobReleaseStatus.OVER);
        endWrapper.set(OtaJob::getUpdateBy, "xxl-job");
        endWrapper.set(OtaJob::getUpdateTime, LocalDateTime.now());
        List<OtaJob> endList = this.list(endWrapper);
        if (CollectionUtil.isNotEmpty(endList)) {
            List<Long> jobIds =
                    endList.stream().filter(Objects::nonNull).map(OtaJob::getId).distinct().collect(Collectors.toList());
            remoteOtaService.stopJob(jobIds);
        }
        this.update(endWrapper);

    }

    @Override
    public void reportJobStatus(JobStatusRuleDto jobStatusRuleDto) {
        String thingArn = jobStatusRuleDto.getThingArn();
        String[] split = thingArn.split("/");
        if (split.length < 2) {
            return;
        }
        String deviceId = split[1];
        Long jobId = Long.valueOf(jobStatusRuleDto.getJobId());

        OtaDeviceResult oldOtaDeviceResult=otaDeviceResultService.getOtaDeviceResultByDeviceIdWithJobId(deviceId,jobId);
        if(Objects.nonNull(oldOtaDeviceResult)&&!"SUCCEEDED".equals(oldOtaDeviceResult.getStatus())){
            oldOtaDeviceResult.setStatus(jobStatusRuleDto.getStatus());
            otaDeviceResultService.updateById(oldOtaDeviceResult);
        }
        //若成功，更新设备固件版本，显示版本
        if ("SUCCEEDED".equals(jobStatusRuleDto.getStatus())) {
            OtaJob otaJob = getById(jobId);
            deviceService.updateDeviceVersionV2(jobId, deviceId, otaJob.getCustomVersion());
        }
    }


    @Override
    public void otaCheck(CheckJobDto checkJobDto) throws IOException {
        String deviceId = checkJobDto.getDeviceId();
        Boolean isSingleMcu = checkJobDto.getSingleMcu();
        String lang = checkJobDto.getLang();
        if (StringUtils.isEmpty(lang)) {
            lang = "en";
        }
        Map<String, String> componentMap = checkJobDto.getComponentMap();

        //v2版本新增字段
        String clientType = checkJobDto.getClientType();

        JobDocument jobDocument = new JobDocument();
        jobDocument.setPackageCount(0);
        IotPublishDto iotPublishDto = new IotPublishDto();
        jobDocument = check(deviceId, isSingleMcu, componentMap, lang, checkJobDto.getShortToken());
        if (StringUtils.isEmpty(clientType)) {
            //兼容之前版本
            iotPublishDto.setTopic(String.format("aws/things/%s/ota/check/accepted", deviceId));
        } else {
            iotPublishDto.setTopic(String.format("aws/things/%s/%s/ota/check/accepted", checkJobDto.getClientType(), deviceId));
        }

        jobDocument.setMessageId(checkJobDto.getMessageId());
        iotPublishDto.setQos(1);
        iotPublishDto.setPayLoad(jobDocument);

        remoteDeviceShadowService.publish(iotPublishDto);
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public String publishJob(Long jobId, Long productId, Boolean isTest) {
        Product product = productService.getById(productId);
        if (Objects.isNull(product)) {
            throw ExceptionMessageUtil.getException(TechnologyErrorCode.TECHNOLOGY_PRODUCT_NOT_EXIST);
        }

        List<String> targets;
        List<String> groupNames;
        if (isTest) {

            groupNames = otaJobGroupService.getTestGroupName(jobId);
            if (CollectionUtil.isEmpty(groupNames)) {
                return null;
            }
            //校验分组是否存在于aws
            checkOtaGroup(groupNames, isTest);
            targets = remoteGroupService.listArns(groupNames);
        } else {
            groupNames = otaJobGroupService.getProductGroupName(jobId);
            if (CollectionUtil.isEmpty(groupNames)) {
                //当没有生产分组时,使用默认以productId为groupName的组发布
                String thingGroupArn = remoteAwsGroupService.getThingGroupArn(String.valueOf(productId));
                targets = Lists.newArrayList(thingGroupArn);
                //新增以productId为组名与jobId的关系记录
                OtaJobGroup otaJobGroup = new OtaJobGroup();
                otaJobGroup.setGroupType(CommonConstant.ONE);
                otaJobGroup.setGroupName(String.valueOf(productId));
                otaJobGroup.setJobId(jobId);
                otaJobGroupService.save(otaJobGroup);
            } else {
                targets = remoteGroupService.listArns(groupNames);
            }
            //校验分组是否存在于aws
            checkOtaGroup(groupNames, isTest);
            // 发布到正式分组时，取消之前已发布的升级任务
            cancelPreReleasedOtaJob(jobId, productId);
        }
        JobDocument jobDocument = this.getJobDocument(jobId);
        jobDocument.setJobIds(Collections.singletonList(jobId.toString()));
        // 下发升级任务待设备端升级后删除
        remoteOtaService.pushOtaJob(jobId.toString(), targets, jobDocument);
        // 新版本支持自定义下发升级通知
        remoteOtaService.publishOtaNotifyMessage(jobId, product.getProductSnCode());
        // 确认发布完成，返回去更新客户版本号
       return getCustomVersion(productId, LocalDateTime.now());

    }

    private void checkOtaGroup(List<String> groupNames, Boolean isTest) {
        groupNames.forEach(groupName -> {
            try {
                remoteAwsGroupService.getThingGroupArn(groupName);
            } catch (IotMiddleException iotMiddleException) {
                if (isTest) {
                    throw ExceptionMessageUtil.getException(TechnologyErrorCode.JOB_TEST_GROUP_NOT_EXIST, groupName);
                } else {
                    throw ExceptionMessageUtil.getException(TechnologyErrorCode.JOB_PRD_GROUP_NOT_EXIST, groupName);
                }
            }
        });
    }

    /**
     * 获取客户版本号
     *
     * @param productId 产品id
     * @return 客户版本号
     */
    public String getCustomVersion(Long productId, LocalDateTime dateTime) {
        List<String> components = productComponentService.listComponentNosByPid(productId);
        // 单MCU产品（R设备）暂不给客户版本号填值
        if (components.size() == 1) {
            return null;
        }
        return dateTime.atZone(ZoneId.systemDefault())
                .withZoneSameInstant(ZoneOffset.ofHours(8))
                .format(new DateTimeFormatterBuilder().appendPattern("yyMMddHH").toFormatter());
    }

    @Override
    public String getTechnologyVersion(Long productId, Long jobId) {
        List<ComponentVo> components =
                productComponentService.all(productId);
        StringBuilder technologyVersion = new StringBuilder();
        Map<String, ComponentVersionVo> firmwareMap = firmwareService.mapTechnologyVersionByJobId(jobId);
        for (ComponentVo component : components) {
            ProductComponent productComponent = productComponentService.getByPidAndComponent(productId, component.getComponentNo());
            technologyVersion.append(component.getComponentNo()).append("#").
                    append(productComponent == null ? "" : productComponent.getSoftwareCoding()).append("_");
            String version;
            if (firmwareMap.containsKey(component.getComponentNo())) {
                version = firmwareMap.get(component.getComponentNo()).getVersion();
            } else {
                version = firmwareService.getLatestPackageVersion(
                        component.getComponentNo());
            }
            technologyVersion.append(version == null ? StringUtils.EMPTY : version);
            technologyVersion.append("-");
        }
        if (technologyVersion.length() > 0) {
            technologyVersion.deleteCharAt(technologyVersion.length() - 1);
        }
        return technologyVersion.toString();
    }


    @SuppressWarnings("Duplicates")
    private String getTechnologyVersion(Map<String, String> componentMap, Long productId, List<PackageInfo> packageInfos) {
        Map<String, String> packageInfoMap = packageInfos.stream().collect(Collectors.toMap(PackageInfo::getComponentNo, PackageInfo::getPackageVersion, (k1, k2) -> k2));
        List<String> technologyVersionList = Lists.newArrayList();
        for (String componentNo : componentMap.keySet()) {
            StringBuilder tempTechnologyVersionStringBuilder = new StringBuilder();
            ProductComponent productComponent = productComponentService.getByPidAndComponent(productId, componentNo);
            if (Objects.isNull(productComponent)) {
                log.error("product componentNo:{} not exist", componentNo);
                continue;
            }
            //1.拼接总成零件
            tempTechnologyVersionStringBuilder.append(componentNo).append("#");
            //2.软件编码拼接
            if (StringUtils.isNotBlank(productComponent.getSoftwareCoding())) {
                //软件编码不存在不拼接
                tempTechnologyVersionStringBuilder.append(productComponent.getSoftwareCoding()).append("_");
            }
            //3.版本号拼接
            String version = StringUtils.isEmpty(packageInfoMap.get(componentNo)) ? componentMap.get(componentNo) : packageInfoMap.get(componentNo);
            if (version.contains("_")) {
                version = version.split("_")[1];
            }

            tempTechnologyVersionStringBuilder.append(version == null ? StringUtils.EMPTY : version);
            technologyVersionList.add(tempTechnologyVersionStringBuilder.toString());
        }
        //排序
        Collections.sort(technologyVersionList);
        StringBuilder technologyVersion = new StringBuilder();
        technologyVersionList.forEach(tech -> {
            technologyVersion.append(tech);
            technologyVersion.append("-");
        });
        technologyVersion.deleteCharAt(technologyVersion.length() - 1);
        return technologyVersion.toString();
    }

    /**
     * 取消之前已发布的job
     */
    private void cancelPreReleasedOtaJob(Long jobId, Long productId) {
        List<JobReleaseListVo> jobReleaseListVo = this.listReleaseByStatusList(productId,
                Arrays.asList(OtaJobReleaseStatus.RELEASED, OtaJobReleaseStatus.RELEASE_READY));
        jobReleaseListVo.forEach(jobReleaseVo -> {
            if (jobId.equals(jobReleaseVo.getId())) {
                // 任务id为当前任务，则不处理
                return;
            }
            remoteOtaService.cancelOtaJob(jobReleaseVo.getId().toString());
            this.updateReleaseStatus(jobReleaseVo.getId(), OtaJobReleaseStatus.STOPPED);
        });
    }

    private void initOtaDeviceComponentResult(String deviceId, Long jobId, Long firmwareId,
                                              String componentNo, String componentName, String newVersion, String oldVersion, Long userId) {
        LambdaQueryWrapper<OtaDeviceComponentResult> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(OtaDeviceComponentResult::getDeviceId, deviceId).
                eq(OtaDeviceComponentResult::getJobId, jobId).
                eq(OtaDeviceComponentResult::getFirmwareId, firmwareId);
        if (otaDeviceComponentResultService.count(wrapper) == 0) {
            OtaDeviceComponentResult componentResult = new OtaDeviceComponentResult();
            componentResult.setDeviceId(deviceId);
            componentResult.setJobId(jobId);
            componentResult.setFirmwareId(firmwareId);
            componentResult.setComponentNo(componentNo);
            componentResult.setComponentName(componentName);
            componentResult.setNewVersion(newVersion);
            componentResult.setOldVersion(oldVersion);
            //componentResult.setAppUserId(userId);
            try {
                otaDeviceComponentResultService.save(componentResult);
            } catch (Exception ex) {
                throw ExceptionMessageUtil.getException(TechnologyErrorCode.TECHNOLOGY_DEVICE_COMPONENT_SAVE_ERROR, ex.getMessage());
            }
        }
    }


    @Override
    public void updateOtaStatus(Long jobId, String deviceId, OtaResultDto otaResultDto) {
        String deviceOtaResultLock = DEVICE_OTA_RESULT_LOCK_PREFIX + jobId + ":" + deviceId;
        RLock lock = RedisUtils.getClient().getLock(deviceOtaResultLock);
        boolean lockResult = true;
        try {
            lockResult = lock.tryLock(5L, 3L, TimeUnit.SECONDS);
            if (lockResult) {
                String deviceOtaResultTimestampKey = DEVICE_OTA_RESULT_TIMESTAMP + jobId + ":" + deviceId;
                if (StringUtils.isEmpty(otaResultDto.getTimestamp())) {
                    handleUpdateOtaStatus(jobId, deviceId, otaResultDto);
                    return;
                }
                Long currentOtaResultTimestamp = Long.valueOf(otaResultDto.getTimestamp());

                Long oldDeviceOtaResultTimestamp = RedisUtils.getCacheObject(deviceOtaResultTimestampKey);
                if (Objects.isNull(oldDeviceOtaResultTimestamp) || oldDeviceOtaResultTimestamp.compareTo(currentOtaResultTimestamp) < 0) {
                    handleUpdateOtaStatus(jobId, deviceId, otaResultDto);
                    RedisUtils.setCacheObject(deviceOtaResultTimestampKey, currentOtaResultTimestamp, Duration.ofMinutes(1));
                } else {
                    log.warn("updateOtaStatus oldDeviceOtaResultTimestamp:{},currentOtaResultTimestamp:{},current message is {}, out of order discard the message",
                            oldDeviceOtaResultTimestamp, currentOtaResultTimestamp, JsonUtils.toJsonString(otaResultDto));
                }

            }
        } catch (InterruptedException e) {
            log.error("jobId:{} , deviceId:{}, updateOtaStatus get lock error", jobId, deviceId);
        } finally {
            if (lockResult && lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
    }

    private void handleUpdateOtaStatus(Long jobId, String deviceId, OtaResultDto otaResultDto) {
        String jobStatus = otaResultDto.getJobStatus();
        //1、更新任务状态
        OtaDeviceResult otaDeviceResult = new OtaDeviceResult();
        otaDeviceResult.setDeviceId(deviceId);
        otaDeviceResult.setJobId(jobId);
        otaDeviceResult.setStatus(otaResultDto.getJobStatus());
        otaDeviceResultService.update(otaDeviceResult,
                Wrappers.<OtaDeviceResult>lambdaUpdate()
                        .eq(OtaDeviceResult::getDeviceId, deviceId)
                        .eq(OtaDeviceResult::getJobId, jobId)
        );
        //2、成功时更新用户版本号
        //若成功，更新设备固件版本，显示版本
        if (JobExecutionStatus.SUCCEEDED.toString().equals(jobStatus)) {
            OtaJob otaJob = getById(jobId);
            deviceService.updateDeviceVersionV2(jobId, deviceId, otaJob.getCustomVersion());
        }

        //3、更新任务对应总成零件升级状态
        otaDeviceComponentResultService.updatePackageStatus(deviceId, jobId, otaResultDto.getPackageResults());
    }
}
