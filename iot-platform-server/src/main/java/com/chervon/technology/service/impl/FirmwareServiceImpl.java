package com.chervon.technology.service.impl;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.chervon.technology.domain.dto.firmware.ListVersionDto;
import com.chervon.technology.domain.entity.Firmware;
import com.chervon.technology.domain.vo.component.ComponentVersionVo;
import com.chervon.technology.mapper.FirmwareMapper;
import com.chervon.technology.service.FirmwareService;

import java.util.List;
import java.util.Map;
import java.util.Objects;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 固件管理 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-07-29
 */
@Service
public class FirmwareServiceImpl extends ServiceImpl<FirmwareMapper, Firmware> implements FirmwareService {

    @Autowired
    private FirmwareMapper firmwareMapper;

    @Override
    public void removeByJobId(Long jobId) {
        LambdaUpdateWrapper<Firmware> wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(Firmware::getJobId, jobId);
        this.remove(wrapper);
    }

    @Override
    public List<String> listVersions(ListVersionDto listVersionDto) {
        QueryWrapper<Firmware> wrapper = new QueryWrapper<>();
        wrapper.eq(Firmware.PRODUCT_ID, listVersionDto.getProductId()).
                eq(Firmware.COMPONENT_NO, listVersionDto.getComponentNo()).
                select("distinct " + Firmware.PACKAGE_VERSION);
        List<Object> objects = this.listObjs(wrapper);
        return (List<String>) (List) objects;
    }

    @Override
    public List<Firmware> listByJobId(Long jobId) {
        QueryWrapper<Firmware> wrapper = new QueryWrapper<>();
        wrapper.eq(Firmware.JOB_ID, jobId).orderByDesc(Firmware.PACKAGE_VERSION);
        return this.list(wrapper);
    }

    @Override
    public Map<String, ComponentVersionVo> mapTechnologyVersionByJobId(Long jobId) {
        return firmwareMapper.mapTechnologyByJobId(jobId);
    }

    @Override
    public Map<String, ComponentVersionVo> mapDisplayVersionByJobId(Long jobId) {
        return firmwareMapper.mapDisplayVersionByJobId(jobId);
    }

    @Override
    public String getLatestPackageVersion(String componentNo) {
        LambdaQueryWrapper<Firmware> wrapper = new LambdaQueryWrapper();
        wrapper.eq(Firmware::getComponentNo, componentNo).orderByDesc(Firmware::getPackageVersion).
                select(Firmware::getPackageVersion).last("LIMIT 1");
        Firmware firmware = this.getOne(wrapper);
        if (firmware != null) {
            return firmware.getPackageVersion();
        }
        return null;
    }

    @Override
    public String getLatestDisplayVersion(String componentNo) {
        LambdaQueryWrapper<Firmware> wrapper = new LambdaQueryWrapper();
        wrapper.eq(Firmware::getComponentNo, componentNo).orderByDesc(Firmware::getDisplayVersion).
                select(Firmware::getDisplayVersion).last("LIMIT 1");
        Firmware firmware = this.getOne(wrapper);
        if (firmware != null) {
            return firmware.getDisplayVersion();
        }
        return null;
    }

    @Override
    public String getDisplayVersion(Long jobId, String componentNo) {
        if (StringUtils.isEmpty(componentNo)) {
            return null;
        }
        Firmware firmware = this.getOne(
                Wrappers.<Firmware>lambdaQuery()
                        .eq(Firmware::getComponentNo, componentNo)
                        .eq(Objects.nonNull(jobId), Firmware::getJobId, jobId)
                        .select(Firmware::getDisplayVersion).orderByDesc(Firmware::getCreateTime)
                        .last("LIMIT 1")
        );
        if (firmware != null) {
            return firmware.getDisplayVersion();
        }
        return null;
    }

    @Override
    public String getFirmwareVersion(Long jobId, String componentNo) {
        if (StringUtils.isEmpty(componentNo)) {
            return null;
        }
        Firmware firmware = this.getOne(
                Wrappers.<Firmware>lambdaQuery()
                        .eq(Firmware::getComponentNo, componentNo)
                        .eq(Objects.nonNull(jobId), Firmware::getJobId, jobId)
                        .select(Firmware::getPackageVersion).orderByDesc(Firmware::getCreateTime)
                        .last("LIMIT 1")
        );
        if (firmware != null) {
            return firmware.getPackageVersion();
        }
        return null;
    }

    @Override
    public List<Firmware> listLatestVersionByJobIds(List<String> jobIds) {
        return firmwareMapper.listLatestVersionByJobIds(jobIds);
    }

    @Override
    public List<Firmware> listWholePackage(List<String> jobIds, String componentNo,
                                           String currentVersion) {
        return firmwareMapper.listWholePackage(jobIds, componentNo, currentVersion);
    }

    @Override
    public Firmware getFirmwarePackage(String jobId, String componentNo) {
        LambdaQueryWrapper<Firmware> wrapper = new LambdaQueryWrapper();
        wrapper.eq(Firmware::getComponentNo, componentNo)
                .eq(Firmware::getJobId, jobId)
                .eq(Firmware::getIsDeleted, 0)
                .orderByDesc(Firmware::getCreateTime)
                .last("LIMIT 1");
        return this.getOne(wrapper);

    }

    @Override
    public List<Firmware> getFirmwareByJob(String componentNo) {
        return this.getBaseMapper().listFirmwareByJob(componentNo);
    }

    @Override
    public List<Firmware> getFirmwareByComponentNo(Long productId, String componentNo) {
        return list(Wrappers.<Firmware>lambdaQuery().eq(Firmware::getComponentNo, componentNo).eq(Firmware::getProductId, productId));
    }


    @Override
    public boolean existed(String jobId, Long packageId) {
        LambdaQueryWrapper<Firmware> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(Firmware::getJobId, jobId).eq(Firmware::getId, packageId);
        return this.count(wrapper) > 0 ? true : false;
    }
}
