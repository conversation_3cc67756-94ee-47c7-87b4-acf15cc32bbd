package com.chervon.technology.service.message_analysis.handlers;

import cn.hutool.json.JSONObject;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.chervon.message.api.dto.DeviceMessageDto;
import com.chervon.technology.api.enums.PushMethodEnum;
import com.chervon.technology.api.enums.SwitchFlagEnum;
import com.chervon.technology.api.toruleengine.FaultMessageAlarmLogAddDto;
import com.chervon.technology.domain.dataobject.DeviceFault;
import com.chervon.technology.domain.dataobject.FaultMessage;
import com.chervon.technology.service.DeviceFaultService;
import com.chervon.technology.service.message_analysis.AbsBaseHandler;
import com.chervon.technology.service.message_analysis.entity.ContextAttributes;
import com.chervon.technology.service.message_analysis.entity.ShadowId;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 此策略为故障列表刷新处理器
 */
@Service
@Slf4j
public class Handler_FaultAlert extends AbsBaseHandler {

    @Autowired
    private DeviceFaultService deviceFaultService;
    private List<String> SHADOW_LIST = Arrays.asList(ShadowId.ID_44005,ShadowId.ID_42002,ShadowId.ID_45001);
    private List<String> SHADOW_CLEAR_ALL = Arrays.asList("190000","500000","400000");

    @Override
    public String handleName() {
        return super.handleName(this.getClass());
    }

    @Override
    public void process(Map<String,Object> handlerContext) {
        log.info("processor: {} begin execute!", this.handleName());
        final FaultMessageAlarmLogAddDto faultMessageAlarmLogAddDto = (FaultMessageAlarmLogAddDto) handlerContext.get(ContextAttributes.FaultMessageAlarmLogAddDto.getCode());
        if (Objects.isNull(faultMessageAlarmLogAddDto)) {
            return;
        }
        if(!SHADOW_LIST.contains(faultMessageAlarmLogAddDto.getPropertyId())){
            return;
        }
        final Object objValue = faultMessageAlarmLogAddDto.getMqttBody().get(faultMessageAlarmLogAddDto.getPropertyId());
        if(Objects.isNull(objValue)){
            return;
        }
        final FaultMessage faultMessage = (FaultMessage) handlerContext.get(ContextAttributes.FaultMessage.getCode());
        if(Objects.isNull(faultMessage)){
            return;
        }
        JSONObject jsonObject = new JSONObject(objValue);
        //获取故障码
        String faultCode = jsonObject.getStr(ShadowId.INDEX_1);
        if(SHADOW_CLEAR_ALL.contains(faultCode)){
            updateClearAllFault(faultMessageAlarmLogAddDto.getDeviceId());
            return;
        }else if(faultCode.startsWith("19") && faultCode.endsWith("00") && faultCode.length()>=6){
            updateClearModelFault(faultMessageAlarmLogAddDto.getDeviceId(),faultCode.substring(0,faultCode.length()-2));
            return;
        }
        //获取故障状态
        Boolean faultStatus = jsonObject.getBool(ShadowId.INDEX_2);
        if(Objects.isNull(faultCode) || Objects.isNull(faultStatus)){
            return;
        }
        List<DeviceMessageDto> messageList= (List<DeviceMessageDto>) handlerContext.get(ContextAttributes.DeviceMessage.getCode());
        final String users = messageList.stream().filter(a -> a.getPushTypes().contains(PushMethodEnum.BANNER.getPushTypes()) && !Objects.isNull(a.getUserId()))
                .map(a -> a.getUserId()).distinct().collect(Collectors.joining(","));
        if(faultStatus.equals(false)){
            updateClearDeviceFault(faultMessageAlarmLogAddDto.getDeviceId(),faultCode);
            return;
        }
        Integer status = faultStatus ? SwitchFlagEnum.OPEN.getType() : SwitchFlagEnum.CLOSE.getType();
        DeviceFault deviceFault=new DeviceFault();
        deviceFault.setFaultMessageId(faultMessageAlarmLogAddDto.getFaultMessageId());
        deviceFault.setDeviceId(faultMessageAlarmLogAddDto.getDeviceId());
        deviceFault.setProductId(faultMessage.getProductId());
        deviceFault.setFaultCode(faultCode);
        deviceFault.setEventType((String) handlerContext.get(ContextAttributes.EventType.getCode()));
        deviceFault.setFaultStatus(status);
        deviceFault.setUsers(users);
        deviceFault.setUpdateTime(new Date());
        deviceFault.setCreateTime(new Date());
        deviceFault.setId();
        // 更新设备故障状态
        deviceFaultService.saveOrUpdate(deviceFault);
    }

    private void updateClearDeviceFault(String deviceId,String faultCode){
        LambdaUpdateWrapper<DeviceFault> updateWrapper=new LambdaUpdateWrapper();
        updateWrapper.eq(DeviceFault::getDeviceId,deviceId);
        updateWrapper.eq(DeviceFault::getFaultCode,faultCode);
        updateWrapper.eq(DeviceFault::getFaultStatus,1);
        updateWrapper.set(DeviceFault::getFaultStatus,0);
        updateWrapper.set(DeviceFault::getUpdateTime,new Date());
        deviceFaultService.update(updateWrapper);
    }

    private void updateClearAllFault(String deviceId){
        LambdaUpdateWrapper<DeviceFault> updateWrapper=new LambdaUpdateWrapper();
        updateWrapper.eq(DeviceFault::getDeviceId,deviceId);
        updateWrapper.eq(DeviceFault::getFaultStatus,1);
        updateWrapper.set(DeviceFault::getFaultStatus,0);
        updateWrapper.set(DeviceFault::getUpdateTime,new Date());
        deviceFaultService.update(updateWrapper);
    }

    private void updateClearModelFault(String deviceId,String faultCodePrefix){
        LambdaUpdateWrapper<DeviceFault> updateWrapper=new LambdaUpdateWrapper();
        updateWrapper.eq(DeviceFault::getDeviceId,deviceId);
        updateWrapper.likeRight(DeviceFault::getFaultCode,faultCodePrefix);
        updateWrapper.eq(DeviceFault::getFaultStatus,1);
        updateWrapper.set(DeviceFault::getFaultStatus,0);
        updateWrapper.set(DeviceFault::getUpdateTime,new Date());
        deviceFaultService.update(updateWrapper);
    }
}

