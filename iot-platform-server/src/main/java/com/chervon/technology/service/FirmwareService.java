package com.chervon.technology.service;

import com.chervon.technology.domain.dto.firmware.ListVersionDto;
import com.chervon.technology.domain.entity.Firmware;
import com.baomidou.mybatisplus.extension.service.IService;
import com.chervon.technology.domain.vo.component.ComponentVersionVo;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 固件管理 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-07-29
 */
public interface FirmwareService extends IService<Firmware> {

    /**
     * 删除指定任务id下的固件包
     * <AUTHOR>
     * @date 17:50 2022/8/1
     * @param jobId:
     * @return void
     **/
    void removeByJobId(Long jobId);

    /**
     * 获取兼容版本列表
     * <AUTHOR>
     * @date 15:23 2022/8/3
     * @param listVersionDto:
     * @return java.util.List<java.lang.String>
     **/
    List<String> listVersions(ListVersionDto listVersionDto);

    /**
     * 根据jobId获取固件列表
     * <AUTHOR>
     * @date 16:26 2022/8/10
     * @param jobId:
     * @return java.util.List<com.chervon.technology.domain.entity.Firmware>
     **/
    List<Firmware> listByJobId(Long jobId);

    /**
     * 获取技术版本号map
     * <AUTHOR>
     * @date 17:23 2022/8/15
     * @param jobId:
     * @return java.util.Map<java.lang.String,java.lang.String>
     **/
    Map<String, ComponentVersionVo> mapTechnologyVersionByJobId(Long jobId);

    /**
     * 获取显示版本号map
     * <AUTHOR>
     * @date 17:23 2022/8/15
     * @param jobId:
     * @return java.util.Map<java.lang.String,java.lang.String>
     **/
    Map<String, ComponentVersionVo> mapDisplayVersionByJobId(Long jobId);

    /**
     * 获取最新的固件版本号
     * @param componentNo
     * @return
     */
    String getLatestPackageVersion(String componentNo);

    /**
     * 获取最新的显示版本号
     * @param componentNo
     * @return
     */
    String getLatestDisplayVersion(String componentNo);

    /**
     * 获取显示版本号
     *
     * @param componentNo 总成零件号
     * @param jobId 任务id
     * @return
     */
    @Deprecated
    String getDisplayVersion(Long jobId ,String componentNo);

    /**
     * 获取显示版本号
     *
     * @param componentNo 总成零件号
     * @param jobId 任务id
     * @return
     */
    String getFirmwareVersion(Long jobId ,String componentNo);

    /**
     * 根据jobId获取相同总成零件号最新的固件列表
     * <AUTHOR>
     * @date 19:19 2022/8/24
     * @param jobIds:
     * @return java.util.List<com.chervon.technology.domain.entity.Firmware>
     **/
    List<Firmware> listLatestVersionByJobIds(List<String> jobIds);

    /**
     * 获取可用升级的整包列表
     * <AUTHOR>
     * @date 11:10 2022/8/25
     * @param jobIds:
     * @param componentNo:
     * @param currentVersion:
     * @return java.util.List<com.chervon.technology.domain.entity.Firmware>
     **/
    List<Firmware> listWholePackage(List<String> jobIds, String componentNo, String currentVersion);

    /**
     * 获取指定升级包
     *
     * @param componentNo:
     * @return java.util.List<com.chervon.technology.domain.entity.Firmware>
     * <AUTHOR>
     * @date 11:10 2022/8/25
     **/
    Firmware getFirmwarePackage(String jobId, String componentNo);

    /**
     * 获取任务有效的固件
     *
     * @param componentNo:
     * @return java.util.List<com.chervon.technology.domain.entity.Firmware>
     * <AUTHOR>
     * @date 11:10 2022/8/25
     **/
    List<Firmware> getFirmwareByJob(String componentNo);

    /**
     * 根据总成零件获取关联的固件
     * @param componentNo 总成零件号
     * @param productId 产品Id
     * @return 固件信息
     */
    List<Firmware> getFirmwareByComponentNo(Long productId, String componentNo);

    /**
     * 判断任务下是否有对应的包
     * <AUTHOR>
     * @date 9:23 2022/10/13
     * @param jobId:
     * @param packageId:
     * @return boolean
     **/
    boolean existed(String jobId, Long packageId);
}
