package com.chervon.technology.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.chervon.common.core.constant.CommonConstant;
import com.chervon.technology.domain.entity.OtaJobGroup;
import com.chervon.technology.mapper.OtaJobGroupMapper;
import com.chervon.technology.service.OtaJobGroupService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 升级任务 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-08-08
 */
@Service
public class OtaJobGroupServiceImpl extends ServiceImpl<OtaJobGroupMapper, OtaJobGroup> implements OtaJobGroupService {

    @Override
    public List<String> getTestGroupName(Long jobId) {
        LambdaQueryWrapper<OtaJobGroup> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(OtaJobGroup::getJobId, jobId).eq(OtaJobGroup::getGroupType,
            CommonConstant.ZERO).select(OtaJobGroup::getGroupName);
        List<Object> objects = this.listObjs(wrapper);
        List<String> groupNames = (List<String>)(List)objects;
        return groupNames;
    }

    @Override
    public void removeByJobId(Long jobId) {
        LambdaQueryWrapper<OtaJobGroup> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(OtaJobGroup::getJobId, jobId);
        this.remove(wrapper);
    }

    @Override
    public List<String> getProductGroupName(Long jobId) {
        LambdaQueryWrapper<OtaJobGroup> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(OtaJobGroup::getJobId, jobId).eq(OtaJobGroup::getGroupType,
            CommonConstant.ONE).select(OtaJobGroup::getGroupName);
        List<Object> objects = this.listObjs(wrapper);
        List<String> groupNames = (List<String>)(List)objects;
        return groupNames;
    }

    @Override
    public List<String> getReadyJobIds(List<String> groupNames, String deviceId) {
        return this.getBaseMapper().getReadyJobIds(groupNames, deviceId);
    }

    @Override
    public Boolean hasTargetGroup(Long jobId) {
        LambdaQueryWrapper<OtaJobGroup> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(OtaJobGroup::getJobId, jobId);
        return this.count(wrapper) > 0;
    }

    @Override
    public List<Long> getJobIdsByGroupName(String groupName) {
        return this.getBaseMapper().getJobIdsByGroupName(groupName);
    }

    @Override
    public String getGroupNameByJobId(List<String> groupNames, Long jobId) {
        LambdaQueryWrapper<OtaJobGroup> wrapper = new LambdaQueryWrapper<>();
        wrapper.select(OtaJobGroup::getGroupName).eq(OtaJobGroup::getJobId, jobId)
            .in(OtaJobGroup::getGroupName, groupNames).last("limit 1");
        OtaJobGroup otaJobGroup = this.getOne(wrapper);
        if (otaJobGroup != null) {
            return otaJobGroup.getGroupName();
        }
        return null;
    }
}
