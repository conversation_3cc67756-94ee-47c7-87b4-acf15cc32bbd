package com.chervon.technology.util;

import com.chervon.technology.config.IotPlatformCommon;
import com.chervon.technology.domain.enums.NetworkModesEnum;
import org.springframework.util.CollectionUtils;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022-08-04
 */
public class ProductUtil {

    /**
     * 设备联网方式处理
     * @param networkModes 设备联网方式
     * @return 联网方式
     */
    //wifi > 4G > BLE > DT> LAN >noNetworked
    public static String dealNetworkMode(List<String> networkModes) {
        if (CollectionUtils.isEmpty(networkModes)) {
            return null;
        }
        if (networkModes.contains(NetworkModesEnum.WIFI_AND_BLE.getValue()) ||
                networkModes.contains(NetworkModesEnum.WIFI.getValue())) {
            return IotPlatformCommon.WIFI;
        } else if(networkModes.contains(NetworkModesEnum.MODE_4G.getValue()) ||
                networkModes.contains(NetworkModesEnum.BLE_AND_4G.getValue())) {
            return IotPlatformCommon.MODE_4G;
        } else if (networkModes.contains(NetworkModesEnum.BLE.getValue())) {
            return IotPlatformCommon.BLE;
        } else if (networkModes.contains(NetworkModesEnum.D_OR_T.getValue())) {
            return IotPlatformCommon.DT;
        } else if (networkModes.contains(NetworkModesEnum.LAN.getValue())) {
            return IotPlatformCommon.LAN;
        } else if (networkModes.contains(NetworkModesEnum.NOT_NETWORKED.getValue())) {
            return IotPlatformCommon.NO_NETWORKED;
        } else {
            return null;
        }
    }
}
