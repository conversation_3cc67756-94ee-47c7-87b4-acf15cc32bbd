package com.chervon.iot.middle.service.data_analysis.handlers;

import cn.hutool.json.JSONObject;
import com.chervon.iot.middle.service.data_analysis.AbsBaseHandler;
import com.chervon.iot.middle.service.data_analysis.entity.ContextAttributes;
import com.chervon.iot.middle.service.data_analysis.entity.DeviceInfoDto;
import com.chervon.iot.middle.service.data_analysis.entity.ShadowId;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Map;
import java.util.Objects;

/**
 * 此策略为故障列表刷新处理器
 */
@Service
@Slf4j
public class Handler_Fault_44005 extends AbsBaseHandler {
    @Override
    public String handleName() {
        return super.handleName(this.getClass());
    }

    @Override
    public void process(Map<String,Object> handlerContext) {
        log.info("processor: {} begin execute!", this.handleName());
        final DeviceInfoDto deviceInfoDto = (DeviceInfoDto) handlerContext.get(ContextAttributes.DeviceInfoDto.getCode());
        if (Objects.isNull(deviceInfoDto)) {
            return;
        }
        Object object = deviceInfoDto.getReportedObject().get(ShadowId._44005);
        if (Objects.isNull(object)) {
            return;
        }
        JSONObject jsonObject = new JSONObject(object);
        //后续业务处理

    }
}

