package com.chervon.iot.middle.service.impl;

import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.amazonaws.auth.AWSStaticCredentialsProvider;
import com.amazonaws.auth.BasicAWSCredentials;
import com.amazonaws.services.iot.AWSIot;
import com.amazonaws.services.iot.AWSIotClientBuilder;
import com.amazonaws.services.iot.model.AttachPolicyRequest;
import com.amazonaws.services.iot.model.CreateKeysAndCertificateRequest;
import com.amazonaws.services.iot.model.CreateKeysAndCertificateResult;
import com.amazonaws.services.s3.model.S3Object;
import com.amazonaws.services.s3.model.S3ObjectInputStream;
import com.amazonaws.util.StringUtils;
import com.chervon.common.core.prop.AwsProperties;
import com.chervon.common.core.utils.AesUtils;
import com.chervon.common.oss.uitl.S3Util;
import com.chervon.fleet.web.api.entity.enums.BusinessTypeEnum;
import com.chervon.iot.middle.api.dto.IotThingQueryDto;
import com.chervon.iot.middle.api.service.RemoteAppService;
import com.chervon.iot.middle.api.vo.device.IotDeviceCertP12Vo;
import com.chervon.iot.middle.api.vo.device.IotDeviceCertVo;
import com.chervon.iot.middle.service.AwsIotService;
import com.chervon.iot.middle.service.DeviceCertificateService;
import io.netty.util.internal.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.bouncycastle.cert.X509CertificateHolder;
import org.bouncycastle.cert.jcajce.JcaX509CertificateConverter;
import org.bouncycastle.jce.provider.BouncyCastleProvider;
import org.bouncycastle.openssl.PEMKeyPair;
import org.bouncycastle.openssl.PEMParser;
import org.bouncycastle.openssl.jcajce.JcaPEMKeyConverter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.io.*;
import java.security.*;
import java.security.cert.CertificateException;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/**
 * <AUTHOR>
 * @className IotDataServiceImpl
 * @description
 * @date 2022/5/16 16:37
 */
@DubboService
@Service
@Slf4j
public class RemoteAppServiceImpl implements RemoteAppService {

    @Autowired
    private AwsIotService awsIotService;
    @Autowired
    private DeviceCertificateService deviceCertificateService;

    @Autowired
    private S3Util s3Util;

    private static final ExecutorService threadPool = Executors.newFixedThreadPool(15);

    @Autowired
    private AwsProperties awsProperties;

    @Value("${env.aws.certSecretKey:2q0ua230feng426}")
    private String certSecretKey;

    static {
        try {
            Security.addProvider(new BouncyCastleProvider());
        } catch (Exception e) {
            e.printStackTrace();
        }
    }


    @SuppressWarnings("Duplicates")
    @Override
    public IotDeviceCertVo getIotCertPem() {
        AWSIot awsIot = awsIotService.getAwsIot();
        // 创建设备证书
        CreateKeysAndCertificateRequest createCertRequest =
                new CreateKeysAndCertificateRequest();
        createCertRequest.setSetAsActive(true);
        CreateKeysAndCertificateResult certResult = awsIot
                .createKeysAndCertificate(createCertRequest);
        String deviceCertificatePem = certResult.getCertificatePem();
        String deviceCertificateArn = certResult.getCertificateArn();
        String devicePrivateKey = certResult.getKeyPair().getPrivateKey();

        // 将证书和策略附加在一起
        AttachPolicyRequest attachPolicyRequest = new AttachPolicyRequest();
        attachPolicyRequest.setPolicyName(AwsIotService.awsProperties.getPolicyName());
        attachPolicyRequest.setTarget(deviceCertificateArn);
        awsIot.attachPolicy(attachPolicyRequest);

        IotDeviceCertVo iotDeviceCertVO = new IotDeviceCertVo();
        iotDeviceCertVO.setCertificatePem(deviceCertificatePem);
        iotDeviceCertVO.setPrivateKey(devicePrivateKey);
        iotDeviceCertVO.setDeviceArn(deviceCertificateArn);
//        CertRecord certRecord = new CertRecord();
//        certRecord.setArn(deviceCertificateArn);
//        certRecord.setUserId(userId);
//        certRecord.setCreateTime(new Date());
//        mongoTemplate.save(certRecord);
        return iotDeviceCertVO;
    }

    public static void main444(String[] args) {
        //sit
//		AWSIot awsIot = AWSIotClientBuilder.standard().withRegion("us-east-1")
//				.withCredentials(new AWSStaticCredentialsProvider(new BasicAWSCredentials("********************",
//						"Ducot3pH8lp7TzqJNdrzVkTAzQ6QkGZTMDQdML2k"))).build();
        //pre
        AWSIot awsIot = AWSIotClientBuilder.standard().withRegion("us-east-1")
                .withCredentials(new AWSStaticCredentialsProvider(new BasicAWSCredentials("********************",
                        "L6tp0LF3Zm2ALQ8zvbz1+I+tH/Ux0HOMJpuhR/k2"))).build();

        // 创建设备证书
        CreateKeysAndCertificateRequest createCertRequest =
                new CreateKeysAndCertificateRequest();
        createCertRequest.setSetAsActive(true);
        CreateKeysAndCertificateResult certResult = awsIot
                .createKeysAndCertificate(createCertRequest);
        String deviceCertificatePem = certResult.getCertificatePem();
        String deviceCertificateArn = certResult.getCertificateArn();
        String devicePrivateKey = certResult.getKeyPair().getPrivateKey();

        // 将证书和策略附加在一起
        AttachPolicyRequest attachPolicyRequest = new AttachPolicyRequest();
        //sit
        //attachPolicyRequest.setPolicyName("DevicePolicySit");
        //pre
        attachPolicyRequest.setPolicyName("DevicePolicyPre");

        attachPolicyRequest.setTarget(deviceCertificateArn);
        awsIot.attachPolicy(attachPolicyRequest);

        IotDeviceCertVo iotDeviceCertVO = new IotDeviceCertVo();
        iotDeviceCertVO.setCertificatePem(deviceCertificatePem);
        iotDeviceCertVO.setPrivateKey(devicePrivateKey);
        iotDeviceCertVO.setDeviceArn(deviceCertificateArn);
        System.out.println(JSONUtil.parseObj(iotDeviceCertVO).toString());

    }

    @Override
    public IotDeviceCertVo getIotCertPemFromS3(Integer businessType) {
        //ego app 证书路径
        String egoAppCertPath="cert/cert";
        //fleet app证书路径
        String fleetAppCertPath="cert/fleet_cert";
        if (businessType == BusinessTypeEnum.FLEET.getType()) {
            return getIotCertPemByS3Path(fleetAppCertPath);
        }
        return getIotCertPemByS3Path(egoAppCertPath);
    }

    private IotDeviceCertVo getIotCertPemByS3Path(String path) {
        //从s3中获取证书
        S3Object s3Object = s3Util.getS3Object(awsProperties.getPictureBucket().getName(), path);
        S3ObjectInputStream is = s3Object.getObjectContent();
        StringBuilder cert = new StringBuilder();
        try {
            BufferedReader reader = new BufferedReader(
                    new InputStreamReader(is, StringUtils.UTF8));
            String line;
            while ((line = reader.readLine()) != null) {
                cert.append(line);
            }
        } catch (IOException e) {
            log.error("RemoteAppServiceImpl#RemoteAppServiceImpl -> read cert error!", e);
        } finally {
            try {
                is.close();
            } catch (IOException e) {
                log.error("RemoteAppServiceImpl#RemoteAppServiceImpl -> S3ObjectInputStream close error!", e);
            }
        }
        IotDeviceCertVo certVo = new IotDeviceCertVo();
        certVo.setCertJSONStr(cert.toString());
        return certVo;
    }


    @Override
    public Map<String, Object> reportedData(IotThingQueryDto req) {
        if (req == null || com.chervon.common.core.utils.StringUtils.isBlank(req.getDeviceId()) || CollectionUtils.isEmpty(req.getThingIds())) {
            return null;
        }
        String shadowString = awsIotService.getShadowString(null, req.getDeviceId());
        if (shadowString == null) {
            return null;
        }
        try {
            com.alibaba.fastjson.JSONObject jsonObject = JSON.parseObject(shadowString);
            if (jsonObject.containsKey("state")) {
                com.alibaba.fastjson.JSONObject state = jsonObject.getJSONObject("state");
                if (state.containsKey("reported")) {
                    com.alibaba.fastjson.JSONObject reported = state.getJSONObject("reported");
                    Map<String, Object> map = new HashMap<>();
                    req.getThingIds().forEach(e -> map.put(e, reported.get(e)));
                    return map;
                }
            }
            return null;
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return null;
        }
    }

    public static void main(String[] args) throws CertificateException, IOException, KeyStoreException, NoSuchAlgorithmException {
        String pre="{\"deviceArn\":\"arn:aws:iot:us-east-1:263696813455:cert/be3260a7b4b949446954a8d6b829688d99dc2fa3ac2570c3718359669244f621\",\n" +
                "\"certificatePem\":\"-----BEGIN CERTIFICATE-----\\nMIIDWjCCAkKgAwIBAgIVAPBWmslVrLRKZcozy3T4Md+T+h8lMA0GCSqGSIb3DQEB\\nCwUAME0xSzBJBgNVBAsMQkFtYXpvbiBXZWIgU2VydmljZXMgTz1BbWF6b24uY29t\\nIEluYy4gTD1TZWF0dGxlIFNUPVdhc2hpbmd0b24gQz1VUzAeFw0yNDA4MDEwNTQ4\\nMDlaFw00OTEyMzEyMzU5NTlaMB4xHDAaBgNVBAMME0FXUyBJb1QgQ2VydGlmaWNh\\ndGUwggEiMA0GCSqGSIb3DQEBAQUAA4IBDwAwggEKAoIBAQC/xRjS8jqPwo53n1Cp\\nyWP7Keh9L2VdzugPs4qDOQkSNjkugsEjYL7bEAAXFE1kxYOc5qQ2PsyqUzmf1c7o\\n072jERdQqO+IBpi7vaJl5XVCK5DO5NdWyBeqll58vp2l46WnsTPeH4NLG65NAuMc\\nuhnacpgQV8MFAz/7clcmpufQpsmatsC+MtXzOBInJR7Pr/kp/+vPQWdg7xXCcnAm\\nDcZNRFEuOjSoaZ8rXiJYV0v60k9em2+d1e+05fOMzDkXU8ZMZuuRtXf7/4MJ4gdx\\nene6ECdiQkwGGiz2Q94CZjy/0G+SG0b9OnPsOTNveRzsK9Z3+vhqCR6q/4LMkRZ/\\n9xtpAgMBAAGjYDBeMB8GA1UdIwQYMBaAFB5zvEGP+EkyKztjqbsgrH5p+S+nMB0G\\nA1UdDgQWBBSj4gRYEzO9UfD27OmO+uPr2snR6zAMBgNVHRMBAf8EAjAAMA4GA1Ud\\nDwEB/wQEAwIHgDANBgkqhkiG9w0BAQsFAAOCAQEAZ5NHiyNuxBpDi3uFpLlrxpWG\\nKZkcZ0k9wuZSruL1dZ0Ubf7EMFRC1ykeOimytJYVZelJebH0lipmIjmp2Jsih1QD\\nBzvjMZlyF841TiNGJWzXu2YjiY/UbRjcHoYTRxAxA2mFbitwkXP2mTJ4YiOzKn69\\nWvmm8A/2FD7isb5KzyYVFf7LNqMvYpsyGP4NOnBoZlEDN8qSyXvgDlcdOUP4Wwty\\nWZ/7EGIVIOQDnZYvgL2GCHvYe3vqSt9Qx8rihxMYtuBcjO/S6O+eGmZDhC/M1sqx\\nt0w0D2wfar1ikGzsmbH+Axk163YwGMLRQMo+45n9BBlSUVJPhQXno4qiQOD7uw==\\n-----END CERTIFICATE-----\",\n" +
                "\"privateKey\":\"-----BEGIN RSA PRIVATE KEY-----\\nMIIEpAIBAAKCAQEAv8UY0vI6j8KOd59Qqclj+ynofS9lXc7oD7OKgzkJEjY5LoLB\\nI2C+2xAAFxRNZMWDnOakNj7MqlM5n9XO6NO9oxEXUKjviAaYu72iZeV1QiuQzuTX\\nVsgXqpZefL6dpeOlp7Ez3h+DSxuuTQLjHLoZ2nKYEFfDBQM/+3JXJqbn0KbJmrbA\\nvjLV8zgSJyUez6/5Kf/rz0FnYO8VwnJwJg3GTURRLjo0qGmfK14iWFdL+tJPXptv\\nndXvtOXzjMw5F1PGTGbrkbV3+/+DCeIHcXp3uhAnYkJMBhos9kPeAmY8v9BvkhtG\\n/Tpz7Dkzb3kc7CvWd/r4agkeqv+CzJEWf/cbaQIDAQABAoIBAQC6uOrG2txyhfm6\\nOAGOkG0tbUzWN4P3pzMFtsezpaFTfkCHLQFptRWgrx+nWTlSWY8hAuH5f4RAg/Cj\\n1Mc2pJUNZeLRwuwYAnRMuQSbOqTF3S2Ohfu2n/WLQAaYZ/GQSOxPL5dVPFHWnAlH\\nKvyASCRMMzAlGY7mHpf5+Qcqt3EYEtsanLmM7X8qP20syRvMI5BHAd/PbryXOTwn\\neztqSUCIQnBNsIHTEG1nJZdZVXgzNGNXcuMlbEFR1kIVERYP3UVoPKEaCAuQyodC\\npSmuRjAjBQ2No09JFOIUzwkGJbfLzqKmM5PQSerDxs7chF98uVnBYwCCbzvUgKxx\\nrhWL3pQBAoGBAPy0VwQJOXEC4mtbqVvETHcwf03+JrSBXa3mNhvTwAvcVr5P8Mt9\\nSCVHcDrRKRb6xEYji7JZgc4El2Om1kTvlsEuadr7dZy6s/TuDwCbUCYYLaQGqGDf\\noFv04ec1VZMRQ0rQXDkeQCTetSPUf8qye0L0TI67uRfD3vlaUzjLEYuhAoGBAMJF\\nU1kcnEW6Z+InAdVZaZaBMDmc/TAdBW3Ts3e/W3gpDpixYjShwhsqcckr0iNxIvvA\\nc9CeVuWRVmzvLNakBq/y7QdAusmFBY6qZsV6lbZzoddB7B/U3AMhP3B3N8tDK5Ev\\nGT8/o4eYmyyfDY5+duSS06aO2F7Tf9plBlR6zjrJAoGBAM4/njmUEcbT9NpssHhO\\naZ5p/a05PnvajgvttcHWfIrxpfakJkLfGLAcc1P9/ExeFS2tu0mwVgpAoxLZS05u\\nQZuf9xOcYqytkUzZs3LqUGsXFsEmEe0m96m1316moBZtY3+/OVYBA5dUEYGR4U8d\\nhw7pd0aTnojv+5+Ggh/7VXGhAoGATh+44+YYhgXvTsfm+dgqGQc80H5sZxPUohT3\\nYBmI/Cpun0L+PjIqr+gCuCayK+SMb/c129hBE7K9sbMBMhPmSI2FelwygWXIFVY7\\nXADvAd9AXyM4hJiCHZQaQez/d/M8KvdozMGXzqi1Bl20AvCieUr+aEWbh47AJKBp\\nykFUe9kCgYADwlgv/5salKt9+Oni851pZ1BlRImigGtmXfRa41QUJ/A0Hy6WZ+FL\\nAFY0iGu70FAvG8U+oR/LZ/fCNNbAdVRaARMm1DqyI1vxlhRe2zDH2ieH5FMHC1Xf\\nDdMco3xvYzeQtfsqeM850w5vf2N+NspHFS47jM9xHuyjVbYYBw8abg==\\n-----END RSA PRIVATE KEY-----\"}";

        String na="{\"deviceArn\":\"arn:aws:iot:us-east-1:174647904994:cert/93cddc81204b951d40321485038b6073574fa8627caa5e1a40d2a68d632dd748\",\n" +
                "\"certificatePem\":\"-----BEGIN CERTIFICATE-----\\nMIIDWTCCAkGgAwIBAgIUJRoElKUpLoeLrWtfCMR+R/xx0z8wDQYJKoZIhvcNAQEL\\nBQAwTTFLMEkGA1UECwxCQW1hem9uIFdlYiBTZXJ2aWNlcyBPPUFtYXpvbi5jb20g\\nSW5jLiBMPVNlYXR0bGUgU1Q9V2FzaGluZ3RvbiBDPVVTMB4XDTI0MDgwMTA1NTcx\\nOVoXDTQ5MTIzMTIzNTk1OVowHjEcMBoGA1UEAwwTQVdTIElvVCBDZXJ0aWZpY2F0\\nZTCCASIwDQYJKoZIhvcNAQEBBQADggEPADCCAQoCggEBALQ7vv9ZvuRcmSsyKdzt\\nIy+5/CAZ7e+s2JMJOR5ztp/5H5Uz41izi7M5gmoGFFOHraWhiCKMFwQZJRPODwf7\\nn7gmsGkH6EC2ci2jro4dAE7ZSp0QZc03OGVAui8Yzis9Yd4tOlU7ETqhX3Px7dze\\naMZfsCfwmVLwVnJw4x6V1zYoPIR51QrS1FRHu0E/I+mCpPnmD+XmlGMwCZ2B+ycG\\nxG8CT/fGjQN+zrLZCEzkSoyNXv7vzZAgJpASxouXOTPBPuiDusFz8xQCmRdvjksR\\n0KLY713bfYwMrR0+GduJco8IwmeS0Kx+sr96VoKuXpdUxQ9Qy0MDyzO0z8zs0gCX\\nqBsCAwEAAaNgMF4wHwYDVR0jBBgwFoAUWFKGQbxleEYxn2hFYU4AbEzhk0swHQYD\\nVR0OBBYEFPzCllJYDFrOaK+Gvz065NDUxhUkMAwGA1UdEwEB/wQCMAAwDgYDVR0P\\nAQH/BAQDAgeAMA0GCSqGSIb3DQEBCwUAA4IBAQCOjST3R8dgHEN5g6ai9H7ZhWWE\\nY1veJ/7PonKtEQ2+QQABK+OfPa6BoUt0gmnB55sk77+WDE7L0s5VvqOPdlNme5RH\\nLOc0xHnqgzX1j3Xya6+Z1eX7A1tWsEM7+JZ7qwbhLsIweHghVJJBCeXjWvpJVPP2\\n/9JwXXG7t8aQMu8rRF754K56bqjbD+FE1528ubt9qD6b+ld329+8mZtDVthRak+S\\n7wViJw2q/KSdhIiX+DGn4IwmuwaUh1tCRedCLiqLhVN7dI77Vb+ptKqWPxJbv0RM\\n7eD9PS1cYhE6EH5Lv0oDX482WN9G5tHvKR+mKEQliurAPjzV5YlJRozLBbBf\\n-----END CERTIFICATE-----\",\n" +
                "\"privateKey\":\"-----BEGIN RSA PRIVATE KEY-----\\nMIIEpQIBAAKCAQEAtDu+/1m+5FyZKzIp3O0jL7n8IBnt76zYkwk5HnO2n/kflTPj\\nWLOLszmCagYUU4etpaGIIowXBBklE84PB/ufuCawaQfoQLZyLaOujh0ATtlKnRBl\\nzTc4ZUC6LxjOKz1h3i06VTsROqFfc/Ht3N5oxl+wJ/CZUvBWcnDjHpXXNig8hHnV\\nCtLUVEe7QT8j6YKk+eYP5eaUYzAJnYH7JwbEbwJP98aNA37OstkITORKjI1e/u/N\\nkCAmkBLGi5c5M8E+6IO6wXPzFAKZF2+OSxHQotjvXdt9jAytHT4Z24lyjwjCZ5LQ\\nrH6yv3pWgq5el1TFD1DLQwPLM7TPzOzSAJeoGwIDAQABAoIBAQCUt5vxXd9a1WF5\\nuCm851uaXQ/CeULQ+sG2Ce08yGRP+iNqXOdiYLyMRY7r51pMVm8ddX08bHXtZsYm\\nYmgawTDYpev4rpKhGyp5qE83aoy/gyLDGS23OLJrMg6vH/u2yC6CQrWYcGXWCCQY\\nKu+HyWmRDpHvUuNhAXmDcsylhySOxR7myHanlCjXpwWft2wqABRRqwSUX0C28odz\\nEF4Hd8glVmCTr5lGO0Gz76XM5Wa0R2plfehoYQzUKtNmkophZzCHQ2721NbXPBWA\\nRRBUyE0VyVi5xTKyQ0I6kLNYBiQScYDy9lvUwlSPrQzIWnahtf6zdMCeIc/lJvT9\\nGJo2RuT5AoGBAOoR2MNzCidDemfSFWMx/2tm9cQVmj6y2Ipr94WaHmMcFRi5zoJj\\nFIb7oSP0sa8/R64fQZwxN2oFry7eXgqsq7MbTOqywBFwXFEBaKR+3X3UNtC0WrsN\\nrNodC1l7ela/OJnOy276QmFkdiQOqaaE5pgBY8YDT6e4NAmyZYFi9+DPAoGBAMUe\\noxGyZP/32Y6nKBFstaxr+fAoMyA1P1ZhBNeEMrXbtUPhq2JaXldhKruT2r7kvFXx\\nlnKYJBJDPSGACt1XCnp1eEOIS8+Xyo81NQYzzM+HSQdEJNQDEERmTSEzakohj8nl\\nVeFlIxf0s9cUni5NddQzMvvaOBWRxIaq+8Y7dt71AoGAVRtaGm64sWAKoLc1SZUD\\nTvTnxX0pUUTMEUCChlRsWhL65Luih6wo6Y+SnrU46gRzNq9strL6eBVLBhqaTP1N\\njNkPVYLa7TxShK2jc7QhKsA/khERsFOflsratpV7/RCYLuyO8XITMsBJaIDgVIic\\nI4bV1Uic7NnAKgpwd+QZRucCgYEAvteslUOcGV4CmeQoHMadFXPspaCl7vbefWis\\n4WMyHXO7iR8BaNWJJRwD3VuGmu6lkKe3OvclZTEIDFkFK8pLcMguSWO50iBpkkC3\\nN2wLTJj0ET7eUQ0uQKqy3gQDUgYwSg3pGbFrSpQ1UXS6uKVyAKIZKFDbn7ZjCOad\\noAyOR8kCgYEAyyjpU+iJWUzmaOLAy14kQfC46CGG6VPyBT3bQGe707s1AsApvTF6\\nbd/Lag5TK+ED71UzHTJ3fAbwKmgaWcwDEjdcJtndQ2WAZyEhd0ycp9yRnvQ7fuO6\\nL5X5+96AXoyLLK1Jm20CqLJZQUP3OPzwwX0t38neEQYSvpRRfrnxuGc=\\n-----END RSA PRIVATE KEY-----\"}";

        String eu="{\"deviceArn\":\"arn:aws:iot:eu-central-1:174647904994:cert/dc55acfa4018f7e54ce3c76dda45c1f60205f79b5a1fd26820fc0a84cfa6a074\",\n" +
                "\"certificatePem\":\"-----BEGIN CERTIFICATE-----\\nMIIDWTCCAkGgAwIBAgIUQqmj788taSO67b+ftfuicwsd9IwwDQYJKoZIhvcNAQEL\\nBQAwTTFLMEkGA1UECwxCQW1hem9uIFdlYiBTZXJ2aWNlcyBPPUFtYXpvbi5jb20g\\nSW5jLiBMPVNlYXR0bGUgU1Q9V2FzaGluZ3RvbiBDPVVTMB4XDTI0MDgwMTA2MDEw\\nN1oXDTQ5MTIzMTIzNTk1OVowHjEcMBoGA1UEAwwTQVdTIElvVCBDZXJ0aWZpY2F0\\nZTCCASIwDQYJKoZIhvcNAQEBBQADggEPADCCAQoCggEBAKxPEmT5p0NPRPOAETws\\nvlZlPCtkBKBIIZZKGyjVADvp00SxGdW9rq8RG8kCKCTqR7cfLap35c2n/qWHP3dJ\\n6KSqfoPCM6/F0UZc9Z+5W8Unk0xXwX+LcQXVHRcX5+jscjvHAv6W6UYd5k8MIFbt\\nM6t7vY+Qtvu8mpfhMxzoJPgcn0yka7wGjoGdZ9CfHDw4ZOeaPsNQ1ZUYdyiuwZX2\\nP+kNw8q0QKjtOZisYzSBW7SO2KxsRkLwm9ncL6978lxJFT4QY1QAc7767s/c/NLL\\nOcOe7NvFLnnS4bOscD7g/gJjWQreMIg48BQxkJI6DLBpG5GNNiYJiTC5+R+acwtm\\naDUCAwEAAaNgMF4wHwYDVR0jBBgwFoAUtUgo5N9nkPKfqkPRrcV50RgL/ukwHQYD\\nVR0OBBYEFB5aGQ/gnw5WowyO39NrN6IWF653MAwGA1UdEwEB/wQCMAAwDgYDVR0P\\nAQH/BAQDAgeAMA0GCSqGSIb3DQEBCwUAA4IBAQCwQtb+VSDfNhj6vbtctNfA6JoP\\n/qyfTH3BwzivJML/jlD24UIuJ9vVzjI6JbRE1mGk4lyvFp0f5LQ9qQiQi2tcEPTX\\n038xbhMoLsQafYFEJk0GoYW/TX5jCrFn7zrSqQoLc9aWQD8BljD1rvwcCR5KtaVK\\nWDzCB+ZgW1vLU7pkvV4jPCqecXsBpItE99irl0NrKKLHk5kJhm4t1B3lqTCmg12a\\nF3C+b4HjwKMv1pd0HuA53VmE5fNheuWWQa6Kt91yWKvJF/XilffteAfH+hfoQzia\\n2pRseV642nMib0e8oNqivH8AypeI6ISOOaOJ5EaQhrlnYyZyMrWEQhcJHazZ\\n-----END CERTIFICATE-----\",\n" +
                "\"privateKey\":\"-----BEGIN RSA PRIVATE KEY-----\\nMIIEpAIBAAKCAQEArE8SZPmnQ09E84ARPCy+VmU8K2QEoEghlkobKNUAO+nTRLEZ\\n1b2urxEbyQIoJOpHtx8tqnflzaf+pYc/d0nopKp+g8Izr8XRRlz1n7lbxSeTTFfB\\nf4txBdUdFxfn6OxyO8cC/pbpRh3mTwwgVu0zq3u9j5C2+7yal+EzHOgk+ByfTKRr\\nvAaOgZ1n0J8cPDhk55o+w1DVlRh3KK7BlfY/6Q3DyrRAqO05mKxjNIFbtI7YrGxG\\nQvCb2dwvr3vyXEkVPhBjVABzvvruz9z80ss5w57s28UuedLhs6xwPuD+AmNZCt4w\\niDjwFDGQkjoMsGkbkY02JgmJMLn5H5pzC2ZoNQIDAQABAoIBAQCLNRrET1oNrDYl\\nj5rdBb0Eqacx2w1NvNi9xcMpeuREg0U3IumfYC6Hq4fKHp72K/yiemL3bQRQ4V9+\\ntcTVh/WXS1MI+dWuNWA8u4+JakQ1BSMgs2lVtP8r2iA1bw/ig1xK5R6LNrrh9fgC\\nduaNs/dhnIfqO91JhfdTqxITUY3EkG+kylbujhYwH86f0XkaAJAH5olLZz6wSwwH\\nSvCuNiuYsSeC5PRQLMZ/MgesZVJZ/fRMmDrMYwsjDQOSm5JY6htefqIBv2z8eEes\\nAIqui53JBrkGqk3i9TEE9LPGcAOrQzT1ev+AIcb+yT7xHk91Oif2tbLUnAv2XjzL\\nBUoUXPNlAoGBANl30zxrH0cmrQQGbstXzjN1sprQ6FhMSJm6S2rrSZYutbTS9hm1\\nD51DzXMkLaPAdDIh7dhMF+E3u98+5GKWkZP8bR881Beh9RB/XcIolpB0NPRjxVMD\\nJv7GaPES4euikcHAUnLrwFX0l6UjIJZg5CBRm1UBPoqddf+egpGlOzuXAoGBAMrW\\n3MfSprVOt1K/huvbd4D5WHqwoAOtF3XffnBg7TOqvhiH+9nUGWDDoQfej2YDS1U/\\nQOWqshvyhvB9l7xjOGj8i5YhHejCh4JcUBCl6SfyL/lmaHScllnx7kkdmUPXD4gJ\\nx+rfCdJFFMik6/Y39JZeHzIObT2VUccgfTZgVmQTAoGBALbSfct1Yz41zY5sughJ\\nMRkiAZ0Vs79HomFatLyWQa74Zdo24J6MESxtK/N4nsXjrcvXqlUE4657ZH4yKV6Y\\nppp6xGJWiRe1GIleCjyIUsvuZfZvShyXsdjOhLrm3Pd0HIV+YdIrT+7zSemStnq1\\nyhNBiAzWlvWBmwnjAHHhLwLRAoGAH+9ABz3E/VqNRjvdNBjzcZzQL1DLgIip3ub2\\n8x5veejx0M7n+d2IuVFTcve5P+hY3ez9fKspK3k9gXNkhkZgxTati09fdBvcVcvO\\n1zAnyyA4KV3UKDfW/AY46837KVEcouBXV0USOMajhQtiEvuwSFQ9ORZIYJ+3plAZ\\nJox4TX0CgYAZYSh570TEEalzxqmBx81mrA+miJJnlumVL/zNf5Ln1BY1CXUv1QDN\\nhUnZOF6FkOaCmvd9Gh4oQK1uju9r5imkWmnloTcsznxBTCUykF70VaZDip1KWG2c\\nPoB75qTiVvk72RuSOVkk0iY++sTHaLtByvCSMqYBBae7EwRmiJGpDw==\\n-----END RSA PRIVATE KEY-----\"}";


        String aaa = AesUtils.encrypt(eu, "2q0ua230feng426");
        String decrypt = AesUtils.decrypt(aaa, "2q0ua230feng426");
        JSONObject jsonObject = JSONUtil.parseObj(decrypt);
        String privateKey = jsonObject.getStr("privateKey");
        String certificatePem = jsonObject.getStr("certificatePem");
        String deviceArn = jsonObject.getStr("deviceArn");
        byte[] bytes = convertPEMToPKCS12(privateKey, certificatePem,
                StringUtil.EMPTY_STRING);
        IotDeviceCertP12Vo iotDeviceCertP12Vo = new IotDeviceCertP12Vo();
        iotDeviceCertP12Vo.setCertificateP12(bytes);
        iotDeviceCertP12Vo.setDeviceArn(deviceArn);
        iotDeviceCertP12Vo.setPrivateKey(privateKey);
    }

    @Override
    public IotDeviceCertP12Vo getIotCertP12(Integer businessType)
            throws CertificateException, NoSuchAlgorithmException, KeyStoreException, IOException {
        IotDeviceCertVo iotCert = getIotCertPemFromS3(businessType);
        String decrypt = AesUtils.decrypt(iotCert.getCertJSONStr(), certSecretKey);
        JSONObject jsonObject = JSONUtil.parseObj(decrypt);
        String privateKey = jsonObject.getStr("privateKey");
        String certificatePem = jsonObject.getStr("certificatePem");
        String deviceArn = jsonObject.getStr("deviceArn");
        byte[] bytes = convertPEMToPKCS12(privateKey, certificatePem,
                StringUtil.EMPTY_STRING);
        IotDeviceCertP12Vo iotDeviceCertP12Vo = new IotDeviceCertP12Vo();
        iotDeviceCertP12Vo.setCertificateP12(bytes);
        iotDeviceCertP12Vo.setDeviceArn(deviceArn);
        iotDeviceCertP12Vo.setPrivateKey(privateKey);

        IotDeviceCertP12Vo certP12Vo = new IotDeviceCertP12Vo();
        certP12Vo.setCertJSONStr(AesUtils.encrypt(JSONUtil.parseObj(iotDeviceCertP12Vo).toString(), certSecretKey));
        return certP12Vo;
    }

    /**
     * 将pem格式证书转换为p12格式
     *
     * @param privateKey:
     * @param cerFile:
     * @param password:
     * <AUTHOR>
     * @date 15:26 2022/5/16
     * @return: byte[]
     **/
    public static byte[] convertPEMToPKCS12(final String privateKey, final String cerFile,
                                            final String password)
            throws IOException, CertificateException, KeyStoreException, NoSuchAlgorithmException {
        // get the private key
        StringReader reader = new StringReader(privateKey);
        PEMParser pem = new PEMParser(reader);
        PrivateKey key = null;
        try {
            PEMKeyPair pemKeyPair = ((PEMKeyPair) pem.readObject());
            JcaPEMKeyConverter jcaPEMKeyConverter = new JcaPEMKeyConverter().setProvider("BC");
            KeyPair keyPair = jcaPEMKeyConverter.getKeyPair(pemKeyPair);
            key = keyPair.getPrivate();
        } finally {
            pem.close();
            reader.close();
        }

        // Get the certificate
        java.security.cert.Certificate x509Certificate = null;
        try {
            reader = new StringReader(cerFile);
            pem = new PEMParser(reader);
            X509CertificateHolder certHolder = (X509CertificateHolder) pem.readObject();
            x509Certificate =
                    new JcaX509CertificateConverter().setProvider("BC")
                            .getCertificate(certHolder);
        } finally {
            pem.close();
            reader.close();
        }

        // Put them into a PKCS12 keystore and write it to a byte[]
        ByteArrayOutputStream bos = new ByteArrayOutputStream();
        try {
            KeyStore ks = KeyStore.getInstance("PKCS12");
            ks.load(null);
            ks.setKeyEntry("alias", key, password.toCharArray(),
                    new java.security.cert.Certificate[]{x509Certificate});
            ks.store(bos, password.toCharArray());
        } finally {
            bos.close();
        }
        return bos.toByteArray();
    }
}
