package com.chervon.iot.middle.service.impl;

import com.alibaba.csp.sentinel.util.StringUtil;
import com.amazonaws.services.iot.AWSIot;
import com.amazonaws.services.iot.model.*;
import com.amazonaws.services.iotdata.AWSIotData;
import com.amazonaws.services.iotdata.model.UpdateThingShadowRequest;
import com.amazonaws.services.iotdata.model.UpdateThingShadowResult;
import com.chervon.common.core.constant.CommonConstant;
import com.chervon.iot.middle.api.dto.device.IotInsertDeviceDto;
import com.chervon.iot.middle.api.exception.IotMiddleErrorCode;
import com.chervon.iot.middle.api.service.RemoteDeviceService;
import com.chervon.iot.middle.api.vo.device.IotDeviceCertVo;
import com.chervon.iot.middle.config.ExceptionMessageUtil;
import com.chervon.iot.middle.domain.pojo.DeviceCertificate;
import com.chervon.iot.middle.service.AwsIotService;
import com.chervon.iot.middle.service.DeviceCertificateService;
import com.google.common.base.Charsets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.IOUtils;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ClassPathResource;
import org.springframework.core.io.Resource;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.io.IOException;
import java.nio.ByteBuffer;
import java.util.*;

/**
 * <p>
 * 设备管理 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-02-14
 */
@Slf4j
@Service
@DubboService
public class RemoteDeviceServiceImpl implements RemoteDeviceService {

    @Autowired
    private AwsIotService awsIotService;
    @Autowired
    private DeviceCertificateService deviceCertificateService;

    @Override
    public IotDeviceCertVo insertDeviceToAws(IotInsertDeviceDto iotInsertDeviceDto) {
        AWSIot awsIot = awsIotService.getAwsIot();
        if (StringUtils.hasLength(iotInsertDeviceDto.getGroupName()) &&
                !awsIotService.checkGroupExisted(iotInsertDeviceDto.getGroupName())) {
            throw ExceptionMessageUtil.getException(IotMiddleErrorCode.GROUP_NAME_NOT_EXIST, iotInsertDeviceDto.getGroupName());
        }
        if (StringUtils.hasLength(iotInsertDeviceDto.getProductKey()) &&
                !awsIotService.checkProductExisted(iotInsertDeviceDto.getProductKey())) {
            throw ExceptionMessageUtil.getException(IotMiddleErrorCode.PRODUCT_KEY_NOT_EXIST, iotInsertDeviceDto.getProductKey());
        }
        checkPolicyExist(awsIot);

        // 申请设备证书，存在直接返回，不存在创建
        final List<DeviceCertificate> deviceCertificates = deviceCertificateService.applyForCertificate(Arrays.asList(iotInsertDeviceDto.getDeviceId()));
        if(CollectionUtils.isEmpty(deviceCertificates)){
            throw ExceptionMessageUtil.getException(IotMiddleErrorCode.DEVICE_CERTIFICATE_CREATE_FAIL, iotInsertDeviceDto.getDeviceId());
        }
        final DeviceCertificate deviceCertificate = deviceCertificates.get(0);

        Map<String, String> attributeMap = Optional.ofNullable(iotInsertDeviceDto.getAttributeMap()).orElseGet(() -> new HashMap(CommonConstant.ONE));
        attributeMap.put("certId", deviceCertificate.getCertificateId());

        //检查aws设备是否存在
        checkAwsDeviceExist(iotInsertDeviceDto,attributeMap);

        // 将设备与证书关联
        AttachThingPrincipalRequest attachThingBuild = new AttachThingPrincipalRequest();
        attachThingBuild.setThingName(iotInsertDeviceDto.getDeviceId());
        attachThingBuild.setPrincipal(deviceCertificate.getCertificateArn());
        awsIot.attachThingPrincipal(attachThingBuild);

        IotDeviceCertVo iotDeviceCertVO = new IotDeviceCertVo();
        iotDeviceCertVO.setCertificatePem(deviceCertificate.getCertificatePem());
        iotDeviceCertVO.setPrivateKey(deviceCertificate.getPrivateKey());

        String groupName = iotInsertDeviceDto.getGroupName();
        if (StringUtils.hasLength(groupName)) {
            AddThingToThingGroupRequest addThingToThingGroupRequest =
                    new AddThingToThingGroupRequest();
            addThingToThingGroupRequest.setThingGroupName(groupName);
            addThingToThingGroupRequest.setThingName(iotInsertDeviceDto.getDeviceId());
            awsIot.addThingToThingGroup(addThingToThingGroupRequest);
        }

        // 创建设备影子
        AWSIotData awsIotData = awsIotService.getAWSIotData();
        UpdateThingShadowRequest updateThingShadowRequest = new UpdateThingShadowRequest();
        updateThingShadowRequest.setThingName(iotInsertDeviceDto.getDeviceId());
        ByteBuffer payload = awsIotService.buildInitPayload();
        updateThingShadowRequest.setPayload(payload);
        UpdateThingShadowResult updateThingShadowResult = awsIotData.updateThingShadow(updateThingShadowRequest);
        int statusCode = updateThingShadowResult.getSdkHttpMetadata().getHttpStatusCode();
        if (statusCode != HttpStatus.OK.value()) {
            throw ExceptionMessageUtil.getException(IotMiddleErrorCode.OPERATE_AWS_REST_EXCEPTION, "update device shadow error!", updateThingShadowRequest.toString(), statusCode);
        }

        return iotDeviceCertVO;
    }

    /**
     * 检查aws是否存在该设备
     * @param iotInsertDeviceDto
     * @param attributeMap
     */
    private void checkAwsDeviceExist(IotInsertDeviceDto iotInsertDeviceDto, Map<String, String> attributeMap) {
        if (awsIotService.checkDeviceNameExisted(iotInsertDeviceDto.getDeviceId())) {
            //存在设备
            awsIotService.updateAwsDevice(iotInsertDeviceDto.getDeviceId(),iotInsertDeviceDto.getProductKey(),attributeMap);
        }else{
            //不存在，创建AWS设备
            awsIotService.createAwsDevice(iotInsertDeviceDto,attributeMap);
        }
    }

    @Override
    public IotDeviceCertVo updateDeviceCert(String deviceId) {
        AWSIot awsIot = awsIotService.getAwsIot();
        if (!awsIotService.checkDeviceNameExisted(deviceId)) {
            throw ExceptionMessageUtil.getException(IotMiddleErrorCode.DEVICE_NAME_NOT_EXIST, deviceId);
        }
        final List<DeviceCertificate> deviceCertificates = deviceCertificateService.applyForCertificate(Arrays.asList(deviceId));
        if(CollectionUtils.isEmpty(deviceCertificates)){
            throw ExceptionMessageUtil.getException(IotMiddleErrorCode.DEVICE_CERTIFICATE_CREATE_FAIL, deviceId);
        }
        final DeviceCertificate deviceCertificate = deviceCertificates.get(0);

        // 将设备与证书关联
        AttachThingPrincipalRequest attachThingBuild = new AttachThingPrincipalRequest();
        attachThingBuild.setThingName(deviceId);
        attachThingBuild.setPrincipal(deviceCertificate.getCertificateArn());
        awsIot.attachThingPrincipal(attachThingBuild);

        IotDeviceCertVo iotDeviceCertVO = new IotDeviceCertVo();
        iotDeviceCertVO.setCertificatePem(deviceCertificate.getCertificatePem());
        iotDeviceCertVO.setPrivateKey(deviceCertificate.getPrivateKey());
        return iotDeviceCertVO;
    }

    @Override
    public void updateCertStatus(String deviceId, Boolean active) {
        AWSIot awsIot = awsIotService.getAwsIot();
        if (!awsIotService.checkDeviceNameExisted(deviceId)) {
            throw ExceptionMessageUtil.getException(IotMiddleErrorCode.DEVICE_NAME_NOT_EXIST, deviceId);
        }
        // 从设备属性中获取证书id
        DescribeThingRequest describeThingRequest = new DescribeThingRequest();
        describeThingRequest.setThingName(deviceId);
        DescribeThingResult describeThingResult = awsIot.describeThing(describeThingRequest);
        Map<String, String> attributes = describeThingResult.getAttributes();
        String certId = attributes.get("certId");
        if (StringUtil.isEmpty(certId)) {
            log.warn("device not band cert, deviceId: {}", deviceId);
            return;
        }
        // 更新证书状态
        UpdateCertificateRequest request = new UpdateCertificateRequest();
        request.setCertificateId(certId);
        if (active) {
            request.setNewStatus(CertificateStatus.ACTIVE);
        } else {
            request.setNewStatus(CertificateStatus.INACTIVE);
        }
        UpdateCertificateResult updateCertificateResult = awsIot.updateCertificate(request);
        int statusCode = updateCertificateResult.getSdkHttpMetadata().getHttpStatusCode();
        if (statusCode != HttpStatus.OK.value()) {
            throw ExceptionMessageUtil.getException(IotMiddleErrorCode.OPERATE_AWS_REST_EXCEPTION, "update device certificate error!", request.toString(), statusCode);
        }

    }


    @Override
    public String getProductKey(String deviceId) {
        if (!awsIotService.checkDeviceNameExisted(deviceId)) {
            return "";
        }
        AWSIot awsIot = awsIotService.getAwsIot();
        DescribeThingRequest describeThingRequest = new DescribeThingRequest();
        describeThingRequest.setThingName(deviceId);
        DescribeThingResult result = awsIot.describeThing(describeThingRequest);
        return result.getThingTypeName();
    }

    @Override
    public void updateAttributes(String deviceId, Map<String, String> newAttributes) {
        awsIotService.updateAttributes(deviceId, newAttributes);
    }

    @Override
    public String getAttributes(String deviceId, String attributeName) {
        return awsIotService.getDeviceAttribute(deviceId, attributeName);
    }

    private static void checkPolicyExist(AWSIot awsIot) {
        GetPolicyRequest getPolicyRequest = new GetPolicyRequest();
        getPolicyRequest.withPolicyName(AwsIotService.awsProperties.getPolicyName());
        try {
            awsIot.getPolicy(getPolicyRequest);
        } catch (ResourceNotFoundException e) {
            log.info("policy is null, create policy......");
            Resource resource = new ClassPathResource("DevicePolicy.json");
            CreatePolicyRequest policyRequest = new CreatePolicyRequest();
            policyRequest.setPolicyName(AwsIotService.awsProperties.getPolicyName());
            try {
                policyRequest.setPolicyDocument(
                        IOUtils.toString(resource.getInputStream(), Charsets.UTF_8.toString()));
            } catch (IOException ex) {
                throw ExceptionMessageUtil.getException(IotMiddleErrorCode.DEVICE_POLICY_ERROR, policyRequest.toString());
            }
            awsIot.createPolicy(policyRequest);
        }
    }



}
