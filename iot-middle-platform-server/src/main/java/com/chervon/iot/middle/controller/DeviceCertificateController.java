package com.chervon.iot.middle.controller;

import com.chervon.iot.middle.service.DeviceCertificateService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.annotations.Param;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.Arrays;

/**
 * <AUTHOR>
 * @description 设备证书测试接口
 * @date 2024/1/11 9:34
 */
@RestController
@RequestMapping("/certificate")
@Slf4j
@Api(tags = "设备证书测试相关接口")
public class DeviceCertificateController {

    @Autowired
    private DeviceCertificateService deviceCertificateService;

    @GetMapping("/asyncApply")
    @ApiOperation(value = "异步申请设备证书")
    public void selectIotLastHourUsageData(@RequestParam String deviceId) {
        deviceCertificateService.asyncApplyForCertificate(Arrays.asList(deviceId));
    }

    @GetMapping("/apply")
    @ApiOperation(value = "通用获取数据接口")
    public Object getLastColumnData(@Param("deviceId") String deviceId) {
        return deviceCertificateService.applyForCertificate(Arrays.asList(deviceId));
    }
}
