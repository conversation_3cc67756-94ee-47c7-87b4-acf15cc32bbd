package com.chervon.iot.middle.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.chervon.common.core.constant.CommonConstant;
import com.chervon.common.core.domain.PageRequest;
import com.chervon.common.core.domain.PageResult;
import com.chervon.common.core.utils.ConvertUtil;
import com.chervon.common.core.utils.StringUtils;
import com.chervon.iot.middle.api.dto.device.IotDeviceShadowBatchDto;
import com.chervon.iot.middle.api.dto.device.IotDeviceShadowDto;
import com.chervon.iot.middle.api.dto.device.IotPublishDto;
import com.chervon.iot.middle.api.dto.device.IotReportShadowDto;
import com.chervon.iot.middle.api.dto.log.ShadowLogPageDto;
import com.chervon.iot.middle.api.enums.ThingModelType;
import com.chervon.iot.middle.api.exception.IotMiddleErrorCode;
import com.chervon.iot.middle.api.pojo.thingmodel.Event;
import com.chervon.iot.middle.api.pojo.thingmodel.Property;
import com.chervon.iot.middle.api.service.RemoteDeviceService;
import com.chervon.iot.middle.api.service.RemoteDeviceShadowService;
import com.chervon.iot.middle.api.vo.device.IotDeviceServiceVo;
import com.chervon.iot.middle.api.vo.log.DeviceShadowLogVo;
import com.chervon.iot.middle.api.vo.shadow.IotDeviceShadowItemVo;
import com.chervon.iot.middle.config.ExceptionMessageUtil;
import com.chervon.iot.middle.service.AwsIotService;
import com.chervon.iot.middle.service.IotDataService;
import com.chervon.iot.middle.service.IotThingModelIdentifierService;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * <AUTHOR>
 * @className RemoteDeviceShadowServiceImpl
 * @description
 * @date 2022/7/18 10:12
 */
@DubboService
@Service
@Slf4j
public class RemoteDeviceShadowServiceImpl  implements RemoteDeviceShadowService {

    @Autowired
    private RemoteDeviceService remoteDeviceService;

    @Autowired
    private AwsIotService awsIotService;

    @Autowired
    private IotDataService iotDataService;
    @Autowired
    private IotThingModelIdentifierService iotThingModelIdentifierService;


    @Override
    public Boolean publish(IotPublishDto iotPublishDto) {
        String jsonStr = JSONUtil.toJsonStr(iotPublishDto.getPayLoad());
        byte[] bytes = jsonStr.getBytes();
        return awsIotService.publish(iotPublishDto.getTopic(), bytes,
                iotPublishDto.getQos());
    }

    @Override
    public Object updateShadowItem(IotDeviceShadowDto iotDeviceShadowDto) {
        return awsIotService.updateProperty(iotDeviceShadowDto);
    }

    @Override
    public void updateDesired(IotDeviceShadowBatchDto iotDeviceShadowBatchDto) {
        awsIotService.updateDesired(iotDeviceShadowBatchDto);
    }

    @Override
    public void updateNamedShadowItems(IotReportShadowDto iotReportShadowDto) {
        awsIotService.updateNamedShadowItems(iotReportShadowDto);
    }

    @Override
    public void updateNamedShadowItems(List<IotReportShadowDto> shadowDtoList) {
        shadowDtoList.stream().forEach(shadowDto -> {
            awsIotService.updateNamedShadowItems(shadowDto);
        });
    }

    @Override
    public Object getShadowItem(String deviceId, String identifier) {
        String shadowString = awsIotService.getShadowString(null, deviceId);
        JSONObject jsonObject = JSONUtil.parseObj(shadowString);
        if (jsonObject != null) {
            JSONObject state = jsonObject.getJSONObject("state");
            if (state != null) {
                JSONObject reported = state.getJSONObject("reported");
                if (reported != null) {
                    return reported.get(identifier);
                }
            }
        }
        return null;
    }

    @Override
    public Object getShadowItem(String deviceId, String identifier, String param) {
        String shadowString = awsIotService.getShadowString(null, deviceId);
        JSONObject jsonObject = JSONUtil.parseObj(shadowString);
        if (jsonObject != null) {
            JSONObject state = jsonObject.getJSONObject("state");
            if (state != null) {
                JSONObject reported = state.getJSONObject("reported");
                if (reported != null) {
                    JSONObject object = reported.getJSONObject(identifier);
                    if (object != null) {
                        return object.get(param);
                    }
                }
            }
        }
        return null;
    }

    @Override
    public Map<String, Object> listShadowItems(String deviceId, List<String> propertyKeys) {
        return null;
    }

    @Override
    public Map<String, Object> listAllShadowItems(String deviceId, ThingModelType type) {
        return null;
    }

    @SuppressWarnings("Duplicates")
    @Override
    public PageResult<IotDeviceShadowItemVo> pageItemsWithThingModel(PageRequest pageRequest, String deviceId, ThingModelType type) {
        PageResult<IotDeviceShadowItemVo> pageResult = new PageResult<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        String productKey = remoteDeviceService.getProductKey(deviceId);
        if(StringUtils.isEmpty(productKey)){
            return pageResult;
        }
        List<IotDeviceShadowItemVo> listVos = new ArrayList<>();
        String shadowString = awsIotService.getShadowString(null, deviceId);
        com.alibaba.fastjson.JSONObject jsonObject = JSON.parseObject(shadowString).getJSONObject("state");
        Map<String, Object> reportedShadowItem = jsonObject.getJSONObject("reported");
        Map<String, Object> desiredShadowItem = jsonObject.getJSONObject("desired");
        if (reportedShadowItem == null) {
            reportedShadowItem = new HashMap<>();
        }
        if (desiredShadowItem == null) {
            desiredShadowItem = new HashMap<>();
        }
        Map<String, Object> reportedMetadata = JSON.parseObject(shadowString).getJSONObject("metadata").getJSONObject("reported");
        Map<String, Object> desiredMetadata = JSON.parseObject(shadowString).getJSONObject("metadata").getJSONObject("desired");

        if(type == ThingModelType.PROPERTY) {
            PageResult<Property> propertyPageResult = iotThingModelIdentifierService.pageProperties(pageRequest, productKey);
            pageResult.setPages(propertyPageResult.getPages());
            pageResult.setTotal(propertyPageResult.getTotal());
            if(CollectionUtil.isEmpty(propertyPageResult.getList())){
                return pageResult;
            }
            for (Property property : propertyPageResult.getList()) {
                IotDeviceShadowItemVo convert = ConvertUtil.convert(property, IotDeviceShadowItemVo.class);
                Object reportedValue = reportedShadowItem.get(property.getIdentifier());
                if (reportedValue != null) {
                    if (reportedValue instanceof Boolean) {
                        convert.setReported((Boolean) reportedValue ? 1 : 0);
                    } else {
                        convert.setReported(reportedShadowItem.get(property.getIdentifier()));
                    }
                    convert.setReportedTime(getNormalTime(reportedMetadata, property.getIdentifier()));
                }
                Object desiredValue = desiredShadowItem.get(property.getIdentifier());
                if (desiredValue != null) {
                    if (desiredValue instanceof Boolean) {
                        convert.setDesired((Boolean) desiredValue ? 1 : 0);
                    } else {
                        convert.setDesired(desiredShadowItem.get(property.getIdentifier()));
                    }
                    convert.setDesiredTime(getNormalTime(desiredMetadata, property.getIdentifier()));
                }
                listVos.add(convert);
            }
        } else if (type == ThingModelType.EVENT) {
            PageResult<Event> eventPageResult = iotThingModelIdentifierService.pageEvents(pageRequest, productKey);
            pageResult.setPages(eventPageResult.getPages());
            pageResult.setTotal(eventPageResult.getTotal());
            if(CollectionUtil.isEmpty(eventPageResult.getList())){
                return pageResult;
            }
            for (Event event : eventPageResult.getList()) {
                IotDeviceShadowItemVo convert = ConvertUtil.convert(event, IotDeviceShadowItemVo.class);
                convert.setReported(reportedShadowItem.get(event.getIdentifier()));
                convert.setReportedTime(getMultiTime(reportedMetadata, event.getIdentifier()));

                convert.setDesired(desiredShadowItem.get(event.getIdentifier()));
                convert.setDesiredTime(getMultiTime(desiredMetadata, event.getIdentifier()));

                listVos.add(convert);
            }
        }else if (type == ThingModelType.SERVICE){
            PageResult<com.chervon.iot.middle.api.pojo.thingmodel.Service> servicePageResult = iotThingModelIdentifierService.pageServices(pageRequest, productKey);
            pageResult.setPages(servicePageResult.getPages());
            pageResult.setTotal(servicePageResult.getTotal());
            if(CollectionUtil.isEmpty(servicePageResult.getList())){
                return pageResult;
            }
            for (com.chervon.iot.middle.api.pojo.thingmodel.Service service : servicePageResult.getList()) {
                IotDeviceShadowItemVo convert = ConvertUtil.convert(service, IotDeviceShadowItemVo.class);
                convert.setReported(reportedShadowItem.get(service.getIdentifier()));
                convert.setReportedTime(getMultiTime(reportedMetadata, service.getIdentifier()));

                convert.setDesired(desiredShadowItem.get(service.getIdentifier()));
                convert.setDesiredTime(getMultiTime(desiredMetadata, service.getIdentifier()));
                listVos.add(convert);
            }
        }else{
            throw ExceptionMessageUtil.getException(IotMiddleErrorCode.UNKNOWN_THING_MODEL_TYPE, type);
        }
        pageResult.setList(listVos);
        return pageResult;
    }

    private Long getNormalTime(Map<String, Object> reportedMetadata, String identifier) {
        if (reportedMetadata != null) {
            Object obj= reportedMetadata.get(identifier);
            if(obj instanceof Map){
                Map<String, Object> reportedMeta = (Map<String, Object>) reportedMetadata.get(identifier);
                if (reportedMeta != null && reportedMeta.get("timestamp") != null) {
                    return CommonConstant.ONE_THOUSAND *
                            Long.valueOf(reportedMeta.get("timestamp").toString());
                }
            }
        }
        return null;
    }

    private Long getMultiTime(Map<String, Object> metadata, String identifier) {
        if (metadata == null) {
            return null;
        }
        Map<String, Object> desiredMeta = (Map<String, Object>) metadata.get(identifier);
        if (desiredMeta == null || CollectionUtil.isEmpty(desiredMeta)) {
            return null;
        }

        Map.Entry<String, Object> next = desiredMeta.entrySet().iterator().next();
        if (!(next.getValue() instanceof Map)) {
            return CommonConstant.ONE_THOUSAND * Long.valueOf(next.getValue().toString());
        }
        Map<String, Object> value = (Map<String, Object>) next.getValue();
        if (value.get("timestamp") == null) {
            return null;
        }
        return CommonConstant.ONE_THOUSAND * Long.valueOf(value.get("timestamp").toString());
    }

    @Override
    public Object getEvent(String deviceId, String eventKey) {
        return null;
    }

    @Override
    public Map<String, Object> listEvent(String deviceId, List<String> eventKeys) {
        return null;
    }

    @Override
    public Map<String, Object> listAllEvent(String deviceId) {
        return null;
    }

    @Override
    public Boolean executeService(IotDeviceServiceVo iotDeviceServiceVo) {
        return null;
    }

    @Override
    public Boolean getOnlineStatus(String deviceId) {
        return null;
    }

    @Override
    public Date getLastOnlineTime(String deviceId) {
        return null;
    }

    @Override
    public PageResult<DeviceShadowLogVo> pageDeviceShadowLog(ShadowLogPageDto shadowLogPageDto) {
        return iotDataService.pageDeviceShadowLog(shadowLogPageDto);
    }

    @Override
    public void removeShadow(String deviceId, String shadowName) {
        awsIotService.removeShadow(deviceId, shadowName);
    }
}
