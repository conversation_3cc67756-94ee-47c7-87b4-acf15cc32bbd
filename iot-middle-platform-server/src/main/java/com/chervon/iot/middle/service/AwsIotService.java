package com.chervon.iot.middle.service;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSONObject;
import com.amazonaws.auth.AWSStaticCredentialsProvider;
import com.amazonaws.auth.BasicAWSCredentials;
import com.amazonaws.services.iot.AWSIot;
import com.amazonaws.services.iot.AWSIotClientBuilder;
import com.amazonaws.services.iot.model.ResourceNotFoundException;
import com.amazonaws.services.iot.model.*;
import com.amazonaws.services.iotdata.AWSIotData;
import com.amazonaws.services.iotdata.AWSIotDataClientBuilder;
import com.amazonaws.services.iotdata.model.*;
import com.chervon.common.core.constant.CommonConstant;
import com.chervon.common.core.prop.AwsProperties;
import com.chervon.common.core.utils.StringUtils;
import com.chervon.common.redis.utils.RedisRepository;
import com.chervon.iot.middle.api.dto.device.IotDeviceShadowBatchDto;
import com.chervon.iot.middle.api.dto.device.IotDeviceShadowDto;
import com.chervon.iot.middle.api.dto.device.IotInsertDeviceDto;
import com.chervon.iot.middle.api.dto.device.IotReportShadowDto;
import com.chervon.iot.middle.api.exception.IotMiddleErrorCode;
import com.chervon.iot.middle.config.ExceptionMessageUtil;
import com.chervon.iot.middle.domain.constant.DataConstant;
import com.chervon.iot.middle.domain.constant.RedisConstant;
import com.google.gson.JsonObject;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;

import java.nio.ByteBuffer;
import java.nio.charset.Charset;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @className AwsIotService
 * @description
 * @date 2022/2/22 18:08
 */
@Service
@Slf4j
@NoArgsConstructor
public class AwsIotService {

    private static final Integer LIST_DEVICE_PAGE_SIZE = 50;
    static public AwsProperties awsProperties;
    static private BasicAWSCredentials awsCredentials;
    @Autowired
    private RedisRepository redisRepository;
    @Autowired
    @Lazy
    private IotDataService iotDataService;
    //避免连接重复创建
    private AWSIot awsIot;
    private AWSIotData awsIotData;

    public AwsIotService(AwsProperties awsProperties) {
        AwsIotService.awsProperties = awsProperties;
        AwsIotService.awsCredentials = new BasicAWSCredentials(awsProperties.getAccessId(),
                awsProperties.getSecretKey());
    }

    public AWSIot getAwsIot() {
        if (awsIot == null) {
            String region = awsProperties.getRegion();
            awsIot = AWSIotClientBuilder.standard().withRegion(region)
                    .withCredentials(new AWSStaticCredentialsProvider(awsCredentials))
                    .build();
        }
        return awsIot;

    }

    public AWSIotData getAWSIotData() {
        if (awsIotData == null) {
            String region = awsProperties.getRegion();
            awsIotData = AWSIotDataClientBuilder.standard().withRegion(region)
                    .withCredentials(new AWSStaticCredentialsProvider(awsCredentials))
                    .build();
        }
        return awsIotData;
    }

    public void updateAttributes(String deviceId, Map<String, String> newAttributes) {
        AWSIot awsIot = getAwsIot();
        DescribeThingRequest describeThingRequest = new DescribeThingRequest();
        describeThingRequest.setThingName(deviceId);
        DescribeThingResult describeThingResult = awsIot.describeThing(describeThingRequest);
        Map<String, String> attributes = describeThingResult.getAttributes();

        UpdateThingRequest updateThingRequest = new UpdateThingRequest();
        updateThingRequest.setThingName(deviceId);
        AttributePayload attributePayload = new AttributePayload();
        attributes.putAll(newAttributes);
        attributePayload.setAttributes(attributes);
        updateThingRequest.setAttributePayload(attributePayload);
        awsIot.updateThing(updateThingRequest);
    }

    /**
     * 获取设备属性
     *
     * @param deviceId      设备ID
     * @param attributeName 属性名称
     * @return java.lang.String
     */
    public String getDeviceAttribute(String deviceId, String attributeName) {
        AWSIot awsIot = getAwsIot();
        DescribeThingRequest describeThingRequest = new DescribeThingRequest();
        describeThingRequest.setThingName(deviceId);
        DescribeThingResult describeThingResult = awsIot.describeThing(describeThingRequest);
        Map<String, String> attributes = describeThingResult.getAttributes();
        return attributes.get(attributeName);
    }

    /**
     * 向 IoT core 发布自定义消息
     *
     * @param topic:   主题
     * @param payLoad: 消息负载
     * @param qos:     消息质量
     * @return java.lang.Boolean
     * <AUTHOR>
     * @date 14:04 2022/2/17
     **/
    public Boolean publish(String topic, byte[] payLoad, int qos) {
        AWSIotData awsIotData = getAWSIotData();
        PublishRequest publishRequest = new PublishRequest();
        publishRequest.setTopic(topic);
        publishRequest.setPayload(ByteBuffer.wrap(payLoad));
        publishRequest.setQos(qos);
        PublishResult publishResult = awsIotData.publish(publishRequest);
        int statusCode = publishResult.getSdkHttpMetadata().getHttpStatusCode();
        if (statusCode == HttpStatus.OK.value()) {
            return true;
        } else {
            log.error(IotMiddleErrorCode.OPERATE_AWS_REST_EXCEPTION.getErrorMessage(), "publish payload to topic error!",
                    publishRequest.toString(), statusCode);
            return false;
        }
    }


    /**
     * 更新设备影子属性期望值
     *
     * @return java.lang.Boolean
     * <AUTHOR>
     * @date 14:05 2022/2/17
     **/
    public Object updateProperty(IotDeviceShadowDto iotDeviceShadowDto) {
        if (!checkDeviceNameExisted(iotDeviceShadowDto.getDeviceId())) {
            throw ExceptionMessageUtil.getException(IotMiddleErrorCode.DEVICE_NAME_NOT_EXIST, iotDeviceShadowDto.getDeviceId());
        }
        AWSIotData awsIotData = getAWSIotData();
        UpdateThingShadowRequest updateThingShadowRequest = new UpdateThingShadowRequest();
        updateThingShadowRequest.setThingName(iotDeviceShadowDto.getDeviceId());
        String payloadString = null;
        if (iotDeviceShadowDto.getIsReported()) {
            payloadString = buildPayloadString(iotDeviceShadowDto.getIdentifier(),
                    iotDeviceShadowDto.getValue(), "reported");
            iotDataService.saveReportedLog(DataConstant.STATUS_DATA_TYPE, iotDeviceShadowDto.getDeviceId(), payloadString);
            updateThingShadowRequest.setPayload(ByteBuffer.wrap(payloadString.getBytes()));
            UpdateThingShadowResult updateThingShadowResult = awsIotData.updateThingShadow(updateThingShadowRequest);
            int statusCode = updateThingShadowResult.getSdkHttpMetadata().getHttpStatusCode();
            if (statusCode != HttpStatus.OK.value()) {
                log.error(IotMiddleErrorCode.OPERATE_AWS_REST_EXCEPTION.getErrorMessage(), "update device shadow error!",
                        updateThingShadowRequest.toString(), statusCode);
                return false;
            }
            return true;
        } else {
            payloadString = buildPayloadString(iotDeviceShadowDto.getIdentifier(),
                    iotDeviceShadowDto.getValue(), "desired");
            String taskKey =
                    RedisConstant.IOT_TASK_KEY_PRE
                            + iotDeviceShadowDto.getDeviceId()
                            + ":" + iotDeviceShadowDto.getIdentifier();
            String taskQueueKey =
                    RedisConstant.IOT_TASK_QUEUE_PRE
                            + iotDeviceShadowDto.getDeviceId()
                            + ":" + iotDeviceShadowDto.getIdentifier();
            Integer timeout = iotDeviceShadowDto.getTimeout() == null ? CommonConstant.TEN :
                    iotDeviceShadowDto.getTimeout();
            redisRepository.set(taskKey, timeout);
            // 移除阻塞队列历史数据，防止影响该次请求判断
            while (redisRepository.rightPop(taskQueueKey) != null) {
                continue;
            }
            updateThingShadowRequest.setPayload(ByteBuffer.wrap(payloadString.getBytes()));
            UpdateThingShadowResult updateThingShadowResult = awsIotData
                    .updateThingShadow(updateThingShadowRequest);
            int httpStatusCode = updateThingShadowResult.getSdkHttpMetadata().getHttpStatusCode();
            if (httpStatusCode == HttpStatus.OK.value()) {
                Object pop = redisRepository.bRPop(timeout, taskQueueKey);
                redisRepository.del(taskKey);
                if (pop == null) {
                    throw ExceptionMessageUtil.getException(IotMiddleErrorCode.SERVICE_TIMEOUT, taskQueueKey);
                }
                return pop;
            } else {
                redisRepository.del(taskKey);
                throw ExceptionMessageUtil.getException(IotMiddleErrorCode.OPERATE_AWS_REST_EXCEPTION, "update device shadow error!",
                        updateThingShadowResult.toString(), httpStatusCode);
            }
        }
    }

    /**
     * 批量更新设备影子上报与下发
     *
     * @return java.lang.Boolean
     * <AUTHOR>
     * @date 14:05 2022/2/17
     **/
    @SuppressWarnings("Duplicates")
    public Object updateDesired(IotDeviceShadowBatchDto iotDeviceShadowBatchDto) {
        if (!checkDeviceNameExisted(iotDeviceShadowBatchDto.getDeviceId())) {
            throw ExceptionMessageUtil.getException(IotMiddleErrorCode.DEVICE_NAME_NOT_EXIST, iotDeviceShadowBatchDto.getDeviceId());
        }
        AWSIotData awsIotData = getAWSIotData();
        UpdateThingShadowRequest updateThingShadowRequest = new UpdateThingShadowRequest();
        updateThingShadowRequest.setThingName(iotDeviceShadowBatchDto.getDeviceId());
        String payloadString;
        if (CollectionUtil.isNotEmpty(iotDeviceShadowBatchDto.getIotDeviceShadowDtoList())) {
            payloadString = batchBuildPayloadString(iotDeviceShadowBatchDto.getIotDeviceShadowDtoList(), iotDeviceShadowBatchDto.getActionType());
        } else {
            payloadString = iotDeviceShadowBatchDto.getInstructions();
        }
        updateThingShadowRequest.setPayload(ByteBuffer.wrap(payloadString.getBytes()));
        awsIotData.updateThingShadow(updateThingShadowRequest);
        return "success";
    }


    /**
     * 构建初始设备影子消息负载
     *
     * @return java.nio.ByteBuffer
     * <AUTHOR>
     * @date 14:06 2022/2/17
     **/
    public ByteBuffer buildInitPayload() {
        JsonObject object = new JsonObject();
        object.add("state", new JsonObject());
        return ByteBuffer.wrap(object.toString().getBytes());
    }


    private String buildPayloadString(String key, Object value, String direction) {
        JSONObject jsonObject = new JSONObject();
        jsonObject.put(key, value);

        JSONObject desiredJson = new JSONObject();
        desiredJson.put(direction, jsonObject);

        JSONObject stateJson = new JSONObject();
        stateJson.put("state", desiredJson);

        return stateJson.toJSONString();
    }

    private String batchBuildPayloadString(List<IotDeviceShadowDto> iotDeviceShadowDtos, String direction) {
        JSONObject jsonObject = new JSONObject();
        iotDeviceShadowDtos.forEach(iotDeviceShadowDto -> {
            jsonObject.put(iotDeviceShadowDto.getIdentifier(), iotDeviceShadowDto.getValue());
        });
        JSONObject desiredJson = new JSONObject();
        desiredJson.put(direction, jsonObject);
        JSONObject stateJson = new JSONObject();
        stateJson.put("state", desiredJson);
        return stateJson.toJSONString();
    }

    private String buildPayloadString(Map<String, Object> reported) {
        JSONObject reportedJson = new JSONObject();
        reportedJson.put("reported", reported);
        JSONObject stateJson = new JSONObject();
        stateJson.put("state", reportedJson);
        return stateJson.toJSONString();
    }

    /**
     * 检查设备名称是否已存在
     *
     * @param deviceName: 设备名称
     * @return java.lang.Boolean
     * <AUTHOR>
     * @date 14:16 2022/2/17
     **/
    public Boolean checkDeviceNameExisted(String deviceName) {
        AWSIot awsIot = getAwsIot();
        DescribeThingRequest describeThingRequest = new DescribeThingRequest();
        describeThingRequest.setThingName(deviceName);
        try {
            awsIot.describeThing(describeThingRequest);
        } catch (ResourceNotFoundException ex) {
            return false;
        }
        return true;
    }

    /**
     * 检查分组是否已存在
     *
     * @param groupName: 分组名称
     * @return java.lang.Boolean
     * <AUTHOR>
     * @date 14:16 2022/2/17
     **/
    public Boolean checkGroupExisted(String groupName) {
        AWSIot awsIot = getAwsIot();
        DescribeThingGroupRequest describeThingGroupRequest = new DescribeThingGroupRequest();
        describeThingGroupRequest.setThingGroupName(groupName);
        try {
            awsIot.describeThingGroup(describeThingGroupRequest);
        } catch (ResourceNotFoundException ex) {
            return false;
        }
        return true;
    }

    /**
     * 添加设备至aws静态分组
     *
     * @param groupName 分组名称
     * @param deviceId  设备ID
     */
    public void addDevice2StaticGroup(String groupName, String deviceId) {
        AWSIot awsIot = getAwsIot();
        AddThingToThingGroupRequest addThingToThingGroupRequest = new AddThingToThingGroupRequest();
        addThingToThingGroupRequest.setThingGroupName(groupName);
        addThingToThingGroupRequest.setThingName(deviceId);
        AddThingToThingGroupResult result = awsIot.addThingToThingGroup(addThingToThingGroupRequest);
        log.info("addDevice2StaticGroup result is {}", JSONUtil.toJsonStr(result));
    }

    /**
     * 查询aws分组内的设备
     *
     * @param groupName 分组名称
     * @return java.util.List<java.lang.String>
     */
    public List<String> listThingsInGroup(String groupName) {
        AWSIot awsIot = getAwsIot();
        List<String> deviceIds = new ArrayList<>();
        ListThingsInThingGroupRequest request = new ListThingsInThingGroupRequest();
        request.setMaxResults(LIST_DEVICE_PAGE_SIZE);
        request.setNextToken(null);
        request.setThingGroupName(groupName);
        ListThingsInThingGroupResult result = awsIot
                .listThingsInThingGroup(request);
        deviceIds.addAll(result.getThings());
        String nextToken = result.getNextToken();
        while (!StringUtils.isEmpty(nextToken)) {
            request = new ListThingsInThingGroupRequest();
            request.setMaxResults(LIST_DEVICE_PAGE_SIZE);
            request.setNextToken(nextToken);
            request.setThingGroupName(groupName);
            result = awsIot
                    .listThingsInThingGroup(request);
            nextToken = result.getNextToken();
            deviceIds.addAll(result.getThings());
        }
        return deviceIds;
    }

    /**
     * 从分组中移除设备
     *
     * @param groupName 分组名称
     * @param deviceId  设备ID
     */
    public void removeDeviceFromGroup(String groupName, String deviceId) {
        AWSIot awsIot = getAwsIot();
        RemoveThingFromThingGroupRequest removeThingFromThingGroupRequest = new RemoveThingFromThingGroupRequest();
        removeThingFromThingGroupRequest.setThingGroupName(groupName);
        removeThingFromThingGroupRequest.setThingName(deviceId);
        awsIot.removeThingFromThingGroup(removeThingFromThingGroupRequest);
    }


    /**
     * 获取分组的aws信息
     *
     * @param groupName: 分组名称
     * @return java.lang.String
     * <AUTHOR>
     * @date 2022年10月10日
     **/
    public DescribeThingGroupResult getDescribeThingGroupResult(String groupName) {
        AWSIot awsIot = getAwsIot();
        DescribeThingGroupRequest describeThingGroupRequest = new DescribeThingGroupRequest();
        describeThingGroupRequest.setThingGroupName(groupName);
        try {
            return awsIot.describeThingGroup(describeThingGroupRequest);
        } catch (ResourceNotFoundException ex) {
            return null;
        }

    }

    /**
     * 检查产品是否已存在
     *
     * @param productKey: 产品标识
     * @return java.lang.Boolean
     * <AUTHOR>
     * @date 14:16 2022/2/17
     **/
    public Boolean checkProductExisted(String productKey) {
        AWSIot awsIot = getAwsIot();
        DescribeThingTypeRequest describeThingTypeRequest = new DescribeThingTypeRequest();
        describeThingTypeRequest.setThingTypeName(productKey);
        try {
            awsIot.describeThingType(describeThingTypeRequest);
        } catch (ResourceNotFoundException ex) {
            return false;
        }
        return true;
    }

    /**
     * 根据设备名称获取产品标识
     *
     * @return java.lang.String
     * <AUTHOR>
     * @date 18:08 2022/3/7
     **/
    public String getProductKey(String deviceName) {
        AWSIot awsIot = getAwsIot();
        DescribeThingRequest describeThingRequest = new DescribeThingRequest();
        describeThingRequest.setThingName(deviceName);
        DescribeThingResult describeThingResult = awsIot.describeThing(describeThingRequest);
        return describeThingResult.getThingTypeName();
    }

    /**
     * 根据设备名称获取产品标识(缓存)
     *
     * @return java.lang.String
     * <AUTHOR>
     * @date 18:08 2022/3/7
     **/

    public String getProductKeyRedis(String deviceName) {
        String redisKey = RedisConstant.IOT_THING_MODEL_DEVICE_ID + deviceName;
        Object productKeyObject = redisRepository.get(redisKey);
        if (productKeyObject == null) {
            try {
                String productKey = getProductKey(deviceName);
                redisRepository.setExpire(redisKey, productKey, 2592000L);
                return productKey;
            } catch (Exception e) {
                throw ExceptionMessageUtil.getException(IotMiddleErrorCode.THING_MODEL_NOT_EXIST, deviceName);
            }
        }
        return String.valueOf(productKeyObject);

    }

    /**
     * 根据设备名称获取产品标识(缓存)(忽略异常)
     **/
    public String getProductKeyRedisIgnoreEx(String deviceName) {
        String redisKey = RedisConstant.IOT_THING_MODEL_DEVICE_ID + deviceName;
        Object productKeyObject = redisRepository.get(redisKey);
        if (productKeyObject != null) {
            return String.valueOf(productKeyObject);
        }
        String productKey;
        try {
            productKey = getProductKey(deviceName);
            redisRepository.setExpire(redisKey, productKey, 2592000L);
        } catch (Exception e) {
            productKey = "null";
            redisRepository.setExpire(redisKey, productKey, 3600L);
        }
        return productKey;
    }


    /**
     * 获取设备影子字符串
     *
     * @param shadowName: 设备影子名称
     * @param deviceName: 设备标识
     * @return java.lang.String
     * <AUTHOR>
     * @date 17:55 2022/5/19
     **/
    public String getShadowString(String shadowName, String deviceName) {
        if (!checkDeviceNameExisted(deviceName)) {
            throw ExceptionMessageUtil.getException(IotMiddleErrorCode.DEVICE_NAME_NOT_EXIST, deviceName);
        }
        AWSIotData awsIotData = getAWSIotData();
        GetThingShadowRequest getThingShadowRequest = new GetThingShadowRequest();
        getThingShadowRequest.setThingName(deviceName);
        getThingShadowRequest.setShadowName(shadowName);
        GetThingShadowResult thingShadow = awsIotData.getThingShadow(getThingShadowRequest);
        int statusCode = thingShadow.getSdkHttpMetadata().getHttpStatusCode();
        if (statusCode != HttpStatus.OK.value()) {
            throw ExceptionMessageUtil.getException(IotMiddleErrorCode.OPERATE_AWS_REST_EXCEPTION, "get device shadow error!", getThingShadowRequest.toString(), statusCode);
        }
        ByteBuffer payload = thingShadow.getPayload();
        Charset charset = Charset.defaultCharset();
        return charset.decode(payload).toString();
    }

    public void updateNamedShadowItems(IotReportShadowDto iotReportShadowDto) {
        AWSIotData awsIotData = getAWSIotData();
        UpdateThingShadowRequest updateThingShadowRequest = new UpdateThingShadowRequest();
        updateThingShadowRequest.setThingName(iotReportShadowDto.getDeviceId());
        String payloadString = buildPayloadString(iotReportShadowDto.getReported());
        updateThingShadowRequest.setPayload(ByteBuffer.wrap(payloadString.getBytes()));
        updateThingShadowRequest.setShadowName(iotReportShadowDto.getShadowName());
        UpdateThingShadowResult updateThingShadowResult = awsIotData.updateThingShadow(updateThingShadowRequest);
        int statusCode = updateThingShadowResult.getSdkHttpMetadata().getHttpStatusCode();
        if (statusCode != HttpStatus.OK.value()) {
            throw ExceptionMessageUtil.getException(IotMiddleErrorCode.OPERATE_AWS_REST_EXCEPTION, "update device shadow error!", updateThingShadowRequest.toString(), statusCode);
        }
    }

    public void removeShadow(String deviceId, String shadowName) {
        AWSIotData awsIotData = getAWSIotData();
        DeleteThingShadowRequest request = new DeleteThingShadowRequest();
        request.setThingName(deviceId);
        request.setShadowName(shadowName);
        DeleteThingShadowResult deleteThingShadowResult = awsIotData.deleteThingShadow(request);
        int statusCode = deleteThingShadowResult.getSdkHttpMetadata().getHttpStatusCode();
        if (statusCode != HttpStatus.OK.value()) {
            throw ExceptionMessageUtil.getException(IotMiddleErrorCode.OPERATE_AWS_REST_EXCEPTION, "delete device shadow error!", request.toString(), statusCode);
        }
    }


    /**
     * 创建aws设备
     *
     * @param iotInsertDeviceDto
     * @param attributeMap
     * @return
     */
    public CreateThingResult createAwsDevice(IotInsertDeviceDto iotInsertDeviceDto, Map attributeMap) {
        AWSIot awsIot = getAwsIot();
        CreateThingRequest createThingRequest = new CreateThingRequest();
        createThingRequest.setThingName(iotInsertDeviceDto.getDeviceId());
        createThingRequest.setThingTypeName(iotInsertDeviceDto.getProductKey());
        AttributePayload attributePayload = new AttributePayload();
        attributePayload.setAttributes(attributeMap);
        createThingRequest.setAttributePayload(attributePayload);
        CreateThingResult thing = awsIot.createThing(createThingRequest);
        return thing;
    }

    /**
     * 更新aws设备
     *
     * @param deviceId
     * @param productKey   pId
     * @param attributeMap
     */
    public void updateAwsDevice(String deviceId, String productKey, Map attributeMap) {
        AWSIot awsIot = getAwsIot();
        UpdateThingRequest updateThingRequest = new UpdateThingRequest();
        updateThingRequest.setThingName(deviceId);
        updateThingRequest.setThingTypeName(productKey);
        AttributePayload attributePayload = new AttributePayload();
        attributePayload.setAttributes(attributeMap);
        updateThingRequest.setAttributePayload(attributePayload);
        awsIot.updateThing(updateThingRequest);
    }


}
