package com.chervon.iot.middle.service.impl;

import com.amazonaws.services.iot.AWSIot;
import com.amazonaws.services.iot.model.AttachPolicyRequest;
import com.amazonaws.services.iot.model.CreateKeysAndCertificateRequest;
import com.amazonaws.services.iot.model.CreateKeysAndCertificateResult;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.chervon.iot.middle.domain.pojo.DeviceCertificate;
import com.chervon.iot.middle.mapper.DeviceCertificateMapper;
import com.chervon.iot.middle.service.AwsIotService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import com.chervon.iot.middle.service.DeviceCertificateService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 服务接口实现
 *
 * <AUTHOR>
 * @since 2024-01-09 10:50:24
 * @description 设备证书关系
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class DeviceCertificateServiceImpl extends ServiceImpl<DeviceCertificateMapper, DeviceCertificate> implements DeviceCertificateService {

    @Autowired
    private AwsIotService awsIotService;
    /**
     * 将线程池注入进来
     */
    @Autowired
    private ThreadPoolTaskExecutor threadPoolTaskExecutor;

    @Override
    public List<DeviceCertificate> applyForCertificate(List<String> listDeviceId) {
        if (CollectionUtils.isEmpty(listDeviceId)) {
            return new ArrayList<>();
        }
        final LambdaQueryWrapper<DeviceCertificate> queryWrapper = new LambdaQueryWrapper<DeviceCertificate>()
                .in(DeviceCertificate::getDeviceId, listDeviceId);
        final List<DeviceCertificate> listCertificates = list(queryWrapper);
        //库里全部申请过，直接返回
        if (listCertificates.size() == listDeviceId.size()) {
            return listCertificates;
        }
        Map<String, DeviceCertificate> deviceCertificateMap = null;
        if (!CollectionUtils.isEmpty(listCertificates)) {
            deviceCertificateMap = listCertificates.stream().collect(Collectors.toMap(DeviceCertificate::getDeviceId, Function.identity(), (k1, k2) -> k2));
        } else {
            deviceCertificateMap = new HashMap<>();
        }
        List<DeviceCertificate> listResult = new ArrayList<>(listDeviceId.size());
        for (String deviceId : listDeviceId) {
            final DeviceCertificate deviceCertificate = deviceCertificateMap.get(deviceId);
            if (!Objects.isNull(deviceCertificate)) {
                listResult.add(deviceCertificate);
                continue;
            }
            //创建设备证书
            AWSIot awsIot = awsIotService.getAwsIot();
            CreateKeysAndCertificateRequest createCertRequest = new CreateKeysAndCertificateRequest();
            createCertRequest.setSetAsActive(true);
            CreateKeysAndCertificateResult certResult = awsIot.createKeysAndCertificate(createCertRequest);
            String deviceCertificatePem = certResult.getCertificatePem();
            String deviceCertificateArn = certResult.getCertificateArn();
            String devicePrivateKey = certResult.getKeyPair().getPrivateKey();

            // 将证书和策略附加在一起
            AttachPolicyRequest attachPolicyRequest = new AttachPolicyRequest();
            attachPolicyRequest.setPolicyName(AwsIotService.awsProperties.getPolicyName());
            attachPolicyRequest.setTarget(deviceCertificateArn);
            awsIot.attachPolicy(attachPolicyRequest);
            //构建保存对象
            DeviceCertificate certRecordSave = new DeviceCertificate();
            certRecordSave.setDeviceId(deviceId);
            certRecordSave.setCertificateId(certResult.getCertificateId());
            certRecordSave.setCertificatePem(deviceCertificatePem);
            certRecordSave.setCertificateArn(deviceCertificateArn);
            certRecordSave.setPrivateKey(devicePrivateKey);
            //保存入库
            save(certRecordSave);
            //添加返回结果
            listResult.add(certRecordSave);
        }
        return listResult;
    }

    /**
     * 异步申请证书
     * @param listDeviceId
     */
    @Override
    public void asyncApplyForCertificate(List<String> listDeviceId) {
        if (CollectionUtils.isEmpty(listDeviceId)) {
            return;
        }
        threadPoolTaskExecutor.execute(() -> {
            try {
                log.info("开始申请证书: {}", listDeviceId);
                long startTime = System.currentTimeMillis();
                doApplyCertificate(listDeviceId);
                long endTime = System.currentTimeMillis();
                log.info("证书申请完成:->耗时毫秒：{}，申请数量：{}", endTime - startTime,listDeviceId.size());
            } catch (Exception e) {
                log.error("申请证书发生异常：申请设备信息：{}，异常：{},",listDeviceId,e);
            }
        });
    }

    private void doApplyCertificate(List<String> listDeviceId) {
        final LambdaQueryWrapper<DeviceCertificate> queryWrapper = new LambdaQueryWrapper<DeviceCertificate>()
                .in(DeviceCertificate::getDeviceId, listDeviceId)
                .select(DeviceCertificate::getDeviceId,DeviceCertificate::getId);
        final List<DeviceCertificate> listCertificates = list(queryWrapper);
        //库里全部申请过，直接返回
        if (listCertificates.size() == listDeviceId.size()) {
            return;
        }
        Map<String, Long> deviceCertificateMap = null;
        if (!CollectionUtils.isEmpty(listCertificates)) {
            deviceCertificateMap = listCertificates.stream().collect(Collectors.toMap(DeviceCertificate::getDeviceId, DeviceCertificate::getId, (k1, k2) -> k2));
        } else {
            deviceCertificateMap = new HashMap<>();
        }
        for (String deviceId : listDeviceId) {
            if (deviceCertificateMap.containsKey(deviceId)) {
                continue;
            }
            // 创建设备证书
            AWSIot awsIot = awsIotService.getAwsIot();
            CreateKeysAndCertificateRequest createCertRequest = new CreateKeysAndCertificateRequest();
            createCertRequest.setSetAsActive(true);
            CreateKeysAndCertificateResult certResult = awsIot.createKeysAndCertificate(createCertRequest);
            String deviceCertificatePem = certResult.getCertificatePem();
            String deviceCertificateArn = certResult.getCertificateArn();
            String devicePrivateKey = certResult.getKeyPair().getPrivateKey();

            // 将证书和策略附加在一起
            AttachPolicyRequest attachPolicyRequest = new AttachPolicyRequest();
            attachPolicyRequest.setPolicyName(AwsIotService.awsProperties.getPolicyName());
            attachPolicyRequest.setTarget(deviceCertificateArn);
            awsIot.attachPolicy(attachPolicyRequest);
            //构建保存对象
            DeviceCertificate certRecordSave = new DeviceCertificate();
            certRecordSave.setDeviceId(deviceId);
            certRecordSave.setCertificateId(certResult.getCertificateId());
            certRecordSave.setCertificatePem(deviceCertificatePem);
            certRecordSave.setCertificateArn(deviceCertificateArn);
            certRecordSave.setPrivateKey(devicePrivateKey);
            //保存入库
            save(certRecordSave);
        }
    }
}