package com.chervon.technology.api;

import com.chervon.technology.api.dto.LastRnPackageDto;
import com.chervon.technology.api.vo.LastRnPackageInfoVo;

/**
 * <AUTHOR>
 * @date 2022/7/26 16:47
 */
public interface RemoteProductRnService {

    /**
     * 根据产品id查找指定app类型的最新rn包信息
     *
     * @param req    入参
     * @param userId 用户id
     * @return rn包信息
     */
    LastRnPackageInfoVo lastInfo(LastRnPackageDto req, Long userId);

}
