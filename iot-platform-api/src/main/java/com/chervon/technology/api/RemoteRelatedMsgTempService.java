package com.chervon.technology.api;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022/11/30 16:07
 */
public interface RemoteRelatedMsgTempService {

    /**
     * 根据建议id列表搜索关联的消息模板id
     *
     * @param suggestionIds 建议id集合
     * @return 消息模板id集合
     */
    Map<Long, List<Long>> listBySuggestionIds(List<Long> suggestionIds);

    /**
     * 校验建议id是否被引用
     *
     * @param suggestionId 建议id
     * @return 是否被引用
     */
    boolean checkSuggestionUsed(Long suggestionId);
}
