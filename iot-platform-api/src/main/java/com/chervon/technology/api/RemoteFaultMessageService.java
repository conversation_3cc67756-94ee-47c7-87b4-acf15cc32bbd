package com.chervon.technology.api;

import com.chervon.common.core.domain.BaseRemoteReqDto;
import com.chervon.common.core.domain.PageResult;
import com.chervon.technology.api.dto.FaultMessageRecordSearchDto;
import com.chervon.technology.api.dto.FaultMessageResultCountDto;
import com.chervon.technology.api.vo.FaultMessageRecordVo;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2022-09-28 19:22
 **/
public interface RemoteFaultMessageService {
    /**
     * 获取消息模板被引用数
     * @param messageTemplateId 消息模板Id
     * @return 被引用次数
     */
    Long getMessageTemplateUsedTimes(Long messageTemplateId);

	/**
	 * 批量统计消息模板引用行数
	 * @param listTemplateId 消息模板id
	 * @return
	 */
	Map<Long, Long> selectMessageTemplateCount(List<Long> listTemplateId);
    /**
     * 告警消息配置分页列表
     *      消息中心
     *
     * @return 被引用次数
     */
    PageResult<FaultMessageRecordVo> pageRecord(BaseRemoteReqDto<FaultMessageRecordSearchDto> req);

	/**
	 * 告警消息配置
	 * 消息中心
	 *
	 */
	List<FaultMessageRecordVo> listRecord(BaseRemoteReqDto<FaultMessageRecordSearchDto> faultMessageSearchDto);


	/**
	 * 告警消息统计变更
	 *
	 */
	void updateFaultMsgCount(List<FaultMessageResultCountDto> countDtoList);
}
