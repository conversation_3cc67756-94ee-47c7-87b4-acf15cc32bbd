package com.chervon.technology.api;

import com.chervon.technology.api.vo.SnowBlowerUsageVo;

public interface RemoteDeviceUsageService {

    /**
     * 上次使用情况
     *
     * @param deviceId 设备id
     * @return 使用情况
     */
    SnowBlowerUsageVo getLastDeviceUsage(String deviceId);

//    /**
//     * 保存设备使用数据
//     * @param deviceId 设备id
//     * @return 保存记录数
//     */
//    void recordUsageSnowBlower(String deviceId);
}
