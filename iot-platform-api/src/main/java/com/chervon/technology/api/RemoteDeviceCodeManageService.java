package com.chervon.technology.api;

import com.chervon.common.core.domain.PageResult;
import com.chervon.technology.api.dto.DeviceCodeAddDto;
import com.chervon.technology.api.dto.EditDeviceCodeStatusDto;
import com.chervon.technology.api.dto.SearchDeviceCodeDto;
import com.chervon.technology.api.vo.DeviceCodeExcel;
import com.chervon.technology.api.vo.DeviceCodeVo;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/2/1 16:59
 */
public interface RemoteDeviceCodeManageService {

    /**
     * 设备多码添加
     *
     * @param deviceAdd 设备多码添加信息
     */
    void addDeviceCode(String lang, DeviceCodeAddDto deviceAdd);

    /**
     * 编辑设备多码状态
     *
     * @param editDeviceCodeStatusDto 编辑设备多码状态Dto
     */
    void editDeviceCodeStatus(String lang, EditDeviceCodeStatusDto editDeviceCodeStatusDto);

    /**
     * 上传多码文文件
     *
     * @param deviceCodeFileArr 多码文件二进制数组
     * @param productId         产品Id
     * @return 上传错误列表
     */
    List<String> uploadDeviceCodeFile(String lang, byte[] deviceCodeFileArr, Long productId);

    /**
     * 分页获取设备多码详情
     *
     * @param searchDeviceCodeDto 设备多码搜索Dto
     * @return 设备多码分页结果
     */
    PageResult<DeviceCodeVo> getListDevice(String lang, SearchDeviceCodeDto searchDeviceCodeDto);

    /**
     * 导出CSV表格
     *
     * @param searchDeviceCode 查询条件
     * @return
     */
    List<DeviceCodeExcel> export(String lang, SearchDeviceCodeDto searchDeviceCode);

}
