package com.chervon.technology.api;

import com.chervon.common.core.domain.PageResult;
import com.chervon.technology.api.core.BaseRemoteReqDto;
import com.chervon.technology.api.dto.charging.SmsChargingDetailPageDto;
import com.chervon.technology.api.dto.charging.SmsChargingPageDto;
import com.chervon.technology.api.vo.charging.SmsChargingDetailExcel;
import com.chervon.technology.api.vo.charging.SmsChargingDetailVo;
import com.chervon.technology.api.vo.charging.SmsChargingExcel;
import com.chervon.technology.api.vo.charging.SmsChargingVo;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/2/25 14:15
 */
public interface RemoteSmsChargingService {

    /**
     * 短信计费分页查询
     *
     * @param req 分页查询条件
     * @return 分页数据
     */
    PageResult<SmsChargingVo> smsChargingPage(BaseRemoteReqDto<SmsChargingPageDto> req);

    /**
     * 短信计费列表查询
     *
     * @param req 列表查询条件
     * @return 列表数据
     */
    List<SmsChargingExcel> smsChargingList(BaseRemoteReqDto<SmsChargingPageDto> req);

    /**
     * 短信计费详情分页查询
     *
     * @param req 分页查询条件
     * @return 分页数据
     */
    PageResult<SmsChargingDetailVo> smsChargingDetailPage(BaseRemoteReqDto<SmsChargingDetailPageDto> req);

    /**
     * 短信计费详情列表查询
     *
     * @param req 列表查询条件
     * @return 列表数据
     */
    List<SmsChargingDetailExcel> smsChargingDetailList(BaseRemoteReqDto<SmsChargingDetailPageDto> req);
}
