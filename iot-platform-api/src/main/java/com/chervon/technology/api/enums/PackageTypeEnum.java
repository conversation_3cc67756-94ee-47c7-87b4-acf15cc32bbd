package com.chervon.technology.api.enums;

/**
 * 固件包类型枚举
 * <AUTHOR>
 * @date 20:20 2022/7/28
 **/
public enum PackageTypeEnum {

    FULL_PACKAGE(1),
    DELTA_PACKAGE(0);


    private Integer value;

    PackageTypeEnum(Integer value) {
        this.value = value;
    }

    public Integer getValue() {
        return value;
    }

    public static PackageTypeEnum build(Integer value) {
        if (value.equals(1)) {
            return PackageTypeEnum.FULL_PACKAGE;
        } else {
            return PackageTypeEnum.DELTA_PACKAGE;
        }
    }
}
