package com.chervon.technology.api.vo;


import com.chervon.common.core.web.domain.BaseEntity;
import com.chervon.technology.api.enums.DeviceOnlineStatusEnum;
import com.chervon.technology.api.enums.DeviceStatusEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2022.05.20
 * app端需要的设备信息
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class DeviceRpcVo extends BaseEntity implements Serializable {
    private static final long serialVersionUID = 4252332272053346042L;
    /**
     * 产品id
     */
    @ApiModelProperty("设备所属产品ID")
    private Long productId;
    /**
     * 设备出厂时烧录的ID，唯一标识：设备类型+mes码
     */
    @ApiModelProperty("设备出厂时烧录的ID，唯一标识：设备类型+mes码")
    private String deviceId;
    /**
     * 设备SN
     */
    @ApiModelProperty("设备SN")
    private String sn;
    /**
     * 设备名称
     */
    @ApiModelProperty("设备名称")
    private String deviceName;
    /**
     * 设备昵称
     */
    @ApiModelProperty("设备昵称")
    private String nickName;
    /**
     * 设备图片地址在S3中存储Key
     */
    @ApiModelProperty("设备图片地址在S3中存储Key")
    private String deviceIcon;
    /**
     * 图片类型：0图片上传，1图片链接
     */
    @ApiModelProperty("图片类型：0图片上传，1图片链接")
    private Integer iconType;
    /**
     * 设备版本号(设备固件客户版本)
     */
    @ApiModelProperty("设备版本号(设备固件客户版本)")
    private String version;
    /**
     * 是否在线：0离线 1在线
     */
    @ApiModelProperty("是否在线：0离线 1在线")
    private DeviceOnlineStatusEnum isOnline;

    /**
     * 上次离线时间
     */
    @ApiModelProperty("上次离线时间")
    private LocalDateTime lastLogoutTime;

    /**
     * 设备状态：DISABLE 停用 NORMAL 正常
     */
    @ApiModelProperty("设备状态：DISABLE 停用 NORMAL 正常")
    private DeviceStatusEnum status;

    /**
     * 商品型号Model#
     */
    @ApiModelProperty("商品型号Model#")
    private String commodityModel;
    /**
     * 通讯方式，wifiAndBle：Wifi+BLE，4GAndBle：4G+BLE，ble：BLE，DOrT：D/T，wifi：WIFI，4G：4G，lan：LAN，notNetworked：不联网
     */
    @ApiModelProperty("通讯方式:wifi > 4G > BLE > DT> LAN >noNetworked")
    private String communicateMode;
    /**
     * 设备类型，directConnectedDevice：直连设备，gatewayDevice：网关设备，gatewaySubDevice：网关子设备，notIotDevice：非Iot设备，oldIotDevice：老iot设备
     */
    @ApiModelProperty("设备类型，directConnectedDevice：直连设备，gatewayDevice：网关设备，gatewaySubDevice：网关子设备，notIotDevice：非Iot设备，oldIotDevice：老iot设备")
    private String productType;

    private String isReal;

    /**
     * 设备激活时间
     */
    @ApiModelProperty("设备激活时间")
    private LocalDateTime activationTime;


    @ApiModelProperty("产品型号")
    private String model;

    @ApiModelProperty("所属品类ID")
    private String categoryId;

    @ApiModelProperty("品类名称")
    private String categoryName;
}
