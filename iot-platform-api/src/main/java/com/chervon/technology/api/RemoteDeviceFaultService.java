package com.chervon.technology.api;

import com.chervon.technology.api.vo.DeviceFaultVo;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2024-03-19 11:05
 **/
public interface RemoteDeviceFaultService {

	/**
	 * 获取设备当前故障信息
	 * @param deviceId 设备id
	 * @return 故障列表
	 */
	List<DeviceFaultVo> getDeviceFaultList(String deviceId);

	/**
	 * 获取设备当前故障信息
	 * @param deviceId 设备id
	 * @return 故障列表
	 */
	List<DeviceFaultVo> getDeviceFaultMessage(String deviceId);

	/**
	 * 更新设备故障状态
	 * @param deviceId 设备id
	 * @param faultCode 故障代码
	 * @param status 故障状态
	 * @return 是否有状态变更
	 */
	boolean updateDeviceFaultStatus(String deviceId,String faultCode ,Integer status);
}
