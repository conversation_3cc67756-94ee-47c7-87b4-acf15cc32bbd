package com.chervon.technology.api.exception;

import com.chervon.common.core.error.IError;

/**
 * 技术平台通用错误信息提示编号（未来统一异常类型ServiceException,不走配置中心获取故障码信息，走静态配置）
 */
public enum TechnologyErrorCodeEnum implements IError {
    PRODUCT_NOT_EXIST("1040012002", "Product information does not exist"),
    ;

    TechnologyErrorCodeEnum(String errorCode, String errorReason) {
        this.errorCode = errorCode;
        this.errorReason = errorReason;
    }

    /**
     * 编号
     */
    private final String errorCode;
    /**
     * 描述
     */
    private final String errorReason;

    public String getCode() {
        return errorCode;
    }

    @Override
    public String getMessage() {
        return errorReason;
    }


}
