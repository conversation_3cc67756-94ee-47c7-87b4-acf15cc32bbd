package com.chervon.technology.api;

import com.chervon.common.core.domain.PageResult;
import com.chervon.technology.api.dto.DeviceJobResultDto;
import com.chervon.technology.api.dto.ota.*;
import com.chervon.technology.api.enums.OtaJobReleaseOperation;
import com.chervon.technology.api.vo.ota.*;

import java.util.List;

/**
 * 升级相关rpc接口
 *
 * <AUTHOR>
 * @date 11:05 2022/8/4
 **/
public interface RemoteOtaJobService {

    /**
     * 获取设备当前总成状态
     *
     * @param deviceJobResultDto:
     * @return com.chervon.technology.api.vo.ota.DeviceJobResultVo
     * <AUTHOR>
     * @date 11:07 2022/8/4
     **/
    List<ComponentResultDto> getDeviceComponentResult(DeviceJobResultDto deviceJobResultDto);


    /**
     * app端确认设备升级
     **/
    void confirmDeviceUpgrade(DeviceJobResultDto deviceJobResultDto);

    /**
     * 获取设备升级app显示历史
     *
     * @param deviceId:
     * @return java.util.List<com.chervon.technology.api.vo.ota.OtaHistoryVo>
     * <AUTHOR>
     * @date 14:54 2022/8/4
     **/
    List<OtaHistoryVo> getOtaHistory(String deviceId,Long userId);

    /**
     * 分页获取任务发布配置列表
     *
     * @param jobConfigListDto 搜索条件
     * @return 搜索结果
     */
    PageResult<JobConfigListVo> pageConfig(JobConfigListDto jobConfigListDto);

    /**
     * 分页获取任务发布发布列表
     *
     * @param jobReleaseListDto 搜索条件
     * @return 搜索结果
     */
    PageResult<JobReleaseListVo> pageRelease(JobReleaseListDto jobReleaseListDto);

    /**
     * 分页获取任务发布发布列
     *
     * @param jobReleaseListDto 搜索条件
     * @return 搜索结果
     */
    List<JobReleaseListVo> listRelease(JobReleaseListDto jobReleaseListDto);

    /**
     * 获取任务详情
     *
     * @param jobId: 任务id
     * @return com.chervon.technology.api.vo.ota.JobDetailVo
     * <AUTHOR>
     * @date 17:30 2022/7/29
     **/
    JobDetailVo getDetail(Long jobId, String lang);

    /**
     * 获取任务发布详情
     *
     * @param jobId: 任务id
     * @return com.chervon.technology.api.vo.ota.JobReleaseDetailVo
     * <AUTHOR>
     * @date 17:30 2022/7/29
     **/
    JobReleaseDetailVo getReleaseDetail(Long jobId);

    /**
     * 任务发布配置
     *
     * @param jobConfigDto: 任务id
     * @param lang 语言
     * @return
     * <AUTHOR>
     * @date 17:30 2022/7/29
     **/
    void configJobRelease(JobConfigDto jobConfigDto, String lang);

    /**
     * 更新任务发布状态
     *
     * @param jobReleaseStatusDto: job状态更新dto
     * @return
     * <AUTHOR>
     * @date 17:30 2022/7/29
     **/
    void updateStatus(JobReleaseStatusDto jobReleaseStatusDto);

    /**
     * 查看升级结果
     *
     * @param otaResultDto: job结果查询dto
     * @return com.chervon.common.core.domain.R
     * <AUTHOR>
     * @date 17:30 2022/7/29
     **/
    OtaResultVo getOtaResult(OtaResultDto otaResultDto);

    /**
     * 获取任务下的分组名称列表
     *
     * @param jobId:
     * @return java.util.List<java.lang.String>
     * <AUTHOR>
     * @date 17:20 2022/8/17
     **/
    List<String> getGroups(Long jobId);

    /**
     * 获取发布拒绝原因
     *
     * @param jobId:
     * @param status:
     * @return java.lang.String
     * <AUTHOR>
     * @date 19:49 2022/9/1
     **/
    String getRefuseReason(Long jobId, OtaJobReleaseOperation status);

    /**
     * 通过分组名称获取正在使用的任务id列表
     * <AUTHOR>
     * @date 14:53 2022/9/27
     * @param groupName:
     * @return java.util.List<java.lang.Long>
     **/
    List<Long> getJobIdsByGroupName(String groupName);
}
