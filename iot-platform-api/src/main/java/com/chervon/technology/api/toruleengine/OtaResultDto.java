package com.chervon.technology.api.toruleengine;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @Author：flynn.wang
 * @Date：2023/10/23 9:33
 */
@ApiModel("规则引擎升级任务结果DTO")
@Data
public class OtaResultDto implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("消息Id")
    private String messageId;

    @ApiModelProperty("升级状态时间（毫秒）")
    private String timestamp;

    @ApiModelProperty("升级任务结果状态：SUCCEEDED：成功 FAILED：失败 IN_PROGRESS：进行中")
    private String jobStatus;

    @ApiModelProperty("总成固件包升级结果dto")
    List<PackageResultDto> packageResults;
}
