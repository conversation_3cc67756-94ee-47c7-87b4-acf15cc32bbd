package com.chervon.operation.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.chervon.common.core.constant.CommonConstant;
import com.chervon.common.core.domain.MultiLanguageVo;
import com.chervon.common.core.domain.PageResult;
import com.chervon.common.core.utils.ConvertUtil;
import com.chervon.configuration.api.core.MultiLanguageBo;
import com.chervon.configuration.api.service.RemoteMultiLanguageService;
import com.chervon.operation.api.dto.MessageTemplateIdDto;
import com.chervon.operation.api.dto.MessageTemplateListDto;
import com.chervon.operation.api.exception.OperationErrorCode;
import com.chervon.operation.api.vo.MessageTemplateDetailVo;
import com.chervon.operation.api.vo.MessageTemplateListVo;
import com.chervon.operation.config.ExceptionMessageUtil;
import com.chervon.operation.config.MultiLanguageUtil;
import com.chervon.operation.config.OperationCommon;
import com.chervon.operation.domain.dataobject.MessageTemplate;
import com.chervon.operation.domain.dto.message.template.MessageTemplateAddDto;
import com.chervon.operation.domain.dto.message.template.MessageTemplateEditDto;
import com.chervon.operation.mapper.MessageTemplateMapper;
import com.chervon.operation.service.MessageTemplateService;
import com.chervon.technology.api.RemoteFaultMessageService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2022-09-07 16:00
 **/
@Slf4j
@Service
@AllArgsConstructor
public class MessageTemplateServiceImpl extends ServiceImpl<MessageTemplateMapper, MessageTemplate>
        implements MessageTemplateService {
    @DubboReference
    private RemoteMultiLanguageService remoteMultiLanguageService;
    @DubboReference
    private RemoteFaultMessageService remoteFaultMessageService;


    /**
     * 获取消息模板被告警消息调用的次数
     * 按照产品定义规则引擎不算做实际被使用次数
     *
     * @param messageTemplateId 消息模板ID
     * @return 被引用次数
     */
    public Long getUsedTimes(Long messageTemplateId) {
        return remoteFaultMessageService.getMessageTemplateUsedTimes(messageTemplateId);
    }

    @Override
    public void add(MessageTemplateAddDto messageTemplateAddDto) {
        MessageTemplate target = new MessageTemplate();
        // 将模板名称新增为多语言
        MultiLanguageBo nameMultiLanguageBo = remoteMultiLanguageService.simpleCreateMultiLanguage(
                OperationCommon.APPLICATION_NAME, messageTemplateAddDto.getName(),
                LocaleContextHolder.getLocale().getLanguage());
        target.setName(nameMultiLanguageBo.getLangId());
        // 将模板标题新增为多语言
        MultiLanguageBo titleMultiLanguageBo = remoteMultiLanguageService.simpleCreateMultiLanguage(
                OperationCommon.APPLICATION_NAME, messageTemplateAddDto.getTitle(),
                LocaleContextHolder.getLocale().getLanguage());
        target.setTitle(titleMultiLanguageBo.getLangId());
        // 将模板内容新增为多语言
        MultiLanguageBo contentMultiLanguageBo = remoteMultiLanguageService.simpleCreateMultiLanguage(
                OperationCommon.APPLICATION_NAME, messageTemplateAddDto.getContent(),
                LocaleContextHolder.getLocale().getLanguage());
        target.setContent(contentMultiLanguageBo.getLangId());
        target.setType(messageTemplateAddDto.getType());
        target.setMessageDisplayType(messageTemplateAddDto.getMessageDisplayType());
        this.save(target);
    }

    @Override
    public void edit(MessageTemplateEditDto messageTemplateEditDto) {
        MessageTemplate target = this.getById(messageTemplateEditDto.getId());
        if (null == target) {
            throw ExceptionMessageUtil.getException(OperationErrorCode.MESSAGE_TEMPLATE_NOT_EXIST, messageTemplateEditDto.getId());
        }
        // 模板名称更新多语言
        remoteMultiLanguageService.simpleUpdateMultiLanguage(OperationCommon.APPLICATION_NAME,
                messageTemplateEditDto.getName().getLangId(), messageTemplateEditDto.getName().getMessage(),
                LocaleContextHolder.getLocale().getLanguage());
        // 模板标题更新多语言
        remoteMultiLanguageService.simpleUpdateMultiLanguage(OperationCommon.APPLICATION_NAME,
                messageTemplateEditDto.getTitle().getLangId(), messageTemplateEditDto.getTitle().getMessage(),
                LocaleContextHolder.getLocale().getLanguage());
        // 模板内容更新多语言
        remoteMultiLanguageService.simpleUpdateMultiLanguage(OperationCommon.APPLICATION_NAME,
                messageTemplateEditDto.getContent().getLangId(), messageTemplateEditDto.getContent().getMessage(),
                LocaleContextHolder.getLocale().getLanguage());
        target.setType(messageTemplateEditDto.getType());
        target.setMessageDisplayType(messageTemplateEditDto.getMessageDisplayType());
        this.updateById(target);
    }

    @Override
    public void delete(MessageTemplateIdDto messageTemplateIdDto) {
        MessageTemplate target = this.getById(messageTemplateIdDto.getId());
        if (null == target) {
            throw ExceptionMessageUtil.getException(OperationErrorCode.MESSAGE_TEMPLATE_NOT_EXIST, messageTemplateIdDto.getId());
        }
        cleanLanguages(target);
        Long usedTimes = getUsedTimes(messageTemplateIdDto.getId());
        if (usedTimes > CommonConstant.ZERO) {
            throw ExceptionMessageUtil.getException(OperationErrorCode.MESSAGE_TEMPLATE_BEING_USED, usedTimes);
        }
        this.removeById(messageTemplateIdDto.getId());
    }

    /**
     * 清理多语言
     * @param target
     */
    private void cleanLanguages(MessageTemplate target) {
        List<Long> langs = new ArrayList<>();
        if (target.getName() != null) {
            langs.add(target.getName());
        }
        if (target.getTitle() != null) {
            langs.add(target.getTitle());
        }
        if (target.getContent() != null) {
            langs.add(target.getContent());
        }
        if (langs.size() > 0) {
            remoteMultiLanguageService.deleteByLangIds(langs);
        }
    }

    private List<Long> findTitleLangIds(String title) {
        List<Long> res = new ArrayList<>();
        if (StringUtils.isBlank(title)) {
            return res;
        }
        List<MessageTemplate> list = this.list();
        List<Long> titleLangIds = list.stream().map(MessageTemplate::getTitle).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(titleLangIds)) {
            return res;
        }
        Map<@NotBlank String, @NotNull List<MultiLanguageBo>> map = remoteMultiLanguageService.listByTextLike(new HashMap<String, List<Long>>() {{
            put(title, titleLangIds);
        }}, LocaleContextHolder.getLocale().getLanguage());
        return map.get(title).stream().map(MultiLanguageBo::getLangId).collect(Collectors.toList());
    }

    private List<Long> findNameLangIds(String name) {
        List<Long> res = new ArrayList<>();
        if (StringUtils.isBlank(name)) {
            return res;
        }
        List<MessageTemplate> list = this.list();
        List<Long> titleLangIds = list.stream().map(MessageTemplate::getName).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(titleLangIds)) {
            return res;
        }
        Map<@NotBlank String, @NotNull List<MultiLanguageBo>> map = remoteMultiLanguageService.listByTextLike(new HashMap<String, List<Long>>() {{
            put(name, titleLangIds);
        }}, LocaleContextHolder.getLocale().getLanguage());
        return map.get(name).stream().map(MultiLanguageBo::getLangId).collect(Collectors.toList());
    }

    @Override
    public PageResult<MessageTemplateListVo> page(MessageTemplateListDto messageTemplateListDto) {
        List<Long> titleLangIds = findTitleLangIds(messageTemplateListDto.getTitle());
        List<Long> nameLangIds = findNameLangIds(messageTemplateListDto.getName());
        // 如果没有匹配的多语言id，则返回空
        if ((StringUtils.isNotBlank(messageTemplateListDto.getTitle()) && CollectionUtils.isEmpty(titleLangIds)) || (StringUtils.isNotBlank(messageTemplateListDto.getName()) && CollectionUtils.isEmpty(nameLangIds))) {
            return new PageResult<>(messageTemplateListDto.getPageNum(), messageTemplateListDto.getPageSize(), 0);
        }
        LambdaQueryWrapper<MessageTemplate> wrapper = new LambdaQueryWrapper<MessageTemplate>()
                .like(null != messageTemplateListDto.getId(), MessageTemplate::getId, messageTemplateListDto.getId())
                .eq(null != messageTemplateListDto.getType(), MessageTemplate::getType, messageTemplateListDto.getType())
                .eq(null != messageTemplateListDto.getMessageDisplayType(), MessageTemplate::getMessageDisplayType, messageTemplateListDto.getMessageDisplayType())
                .in(StringUtils.isNotBlank(messageTemplateListDto.getName()), MessageTemplate::getName, nameLangIds)
                .in(StringUtils.isNotBlank(messageTemplateListDto.getTitle()), MessageTemplate::getTitle, titleLangIds)
                .orderByDesc(MessageTemplate::getCreateTime);
        Page<MessageTemplate> page = this.getBaseMapper().selectPage(new Page<>(messageTemplateListDto.getPageNum(),
                messageTemplateListDto.getPageSize()), wrapper);
        PageResult<MessageTemplateListVo> result =
                new PageResult<>(page.getCurrent(), page.getSize(), page.getTotal());
        List<MessageTemplateListVo> messageTemplateListVoList = new ArrayList<>();

        //多语言批量查询
        List<MessageTemplate> messageTemplates=page.getRecords();

        //模板名称多语言
        List<String> nameLangIdStrs = messageTemplates.stream().map(x->String.valueOf(x.getName())).collect(Collectors.toList());
        Map<Long, String> nameLangMaps = remoteMultiLanguageService.listByIds(nameLangIdStrs).stream().collect(Collectors.toMap(MultiLanguageBo::getLangId,MultiLanguageBo::getLangCode));
        //模板标题多语言
        List<String> titleLangIdStrs = messageTemplates.stream().map(x->String.valueOf(x.getTitle())).collect(Collectors.toList());
        Map<Long, String> titleLangMaps = remoteMultiLanguageService.listByIds(titleLangIdStrs).stream().collect(Collectors.toMap(MultiLanguageBo::getLangId,MultiLanguageBo::getLangCode));
        //模板内容多语言
        List<String> contentLangIdStrs = messageTemplates.stream().map(x->String.valueOf(x.getContent())).collect(Collectors.toList());
        Map<Long, String> contentLangMaps = remoteMultiLanguageService.listByIds(contentLangIdStrs).stream().collect(Collectors.toMap(MultiLanguageBo::getLangId,MultiLanguageBo::getLangCode));

        for (MessageTemplate messageTemplate : messageTemplates) {
            MessageTemplateListVo messageTemplateListVo = ConvertUtil.convert(messageTemplate, MessageTemplateListVo.class);
            // 获取模板名称多语言
            String langCode = nameLangMaps.get(messageTemplate.getName());
            messageTemplateListVo.setName(new MultiLanguageVo(messageTemplate.getName(),
                    MultiLanguageUtil.getByLangCode(langCode, LocaleContextHolder.getLocale().getLanguage())));
            // 获取模板标题多语言
            String titleCode = titleLangMaps.get(messageTemplate.getTitle());
            messageTemplateListVo.setTitle(new MultiLanguageVo(messageTemplate.getTitle(),
                    MultiLanguageUtil.getByLangCode(titleCode, LocaleContextHolder.getLocale().getLanguage())));
            // 获取模板内容多语言
            String contentCode = contentLangMaps.get(messageTemplate.getContent());
            messageTemplateListVo.setContent( new MultiLanguageVo(messageTemplate.getContent(),
                   MultiLanguageUtil.getByLangCode(contentCode, LocaleContextHolder.getLocale().getLanguage())));
            messageTemplateListVo.setUsedTimes(Math.toIntExact(getUsedTimes(messageTemplateListVo.getId())));
            messageTemplateListVoList.add(messageTemplateListVo);
        }
        result.setList(messageTemplateListVoList);
        return result;
    }

    @Override
    public MessageTemplateDetailVo detail(MessageTemplateIdDto messageTemplateIdDto) {
        MessageTemplate messageTemplate = this.getById(messageTemplateIdDto.getId());
        if (null == messageTemplate) {
            throw ExceptionMessageUtil.getException(OperationErrorCode.MESSAGE_TEMPLATE_NOT_EXIST, messageTemplateIdDto.getId());
        }
        MessageTemplateDetailVo result = ConvertUtil.convert(messageTemplate, MessageTemplateDetailVo.class);
        // 获取模板名称多语言
        MultiLanguageBo nameBo = remoteMultiLanguageService.getById(messageTemplate.getName().toString());
        String langCode = nameBo.getLangCode() == null ? "" : nameBo.getLangCode();
        MultiLanguageVo nameVo = new MultiLanguageVo(nameBo.getLangId(),
                com.chervon.operation.config.MultiLanguageUtil.getByLangCode(langCode, LocaleContextHolder.getLocale().getLanguage()));
        result.setName(nameVo);
        // 获取模板标题多语言
        MultiLanguageBo titleBo = remoteMultiLanguageService.getById(messageTemplate.getTitle().toString());
        String titleCode = titleBo.getLangCode() == null ? "" : titleBo.getLangCode();
        MultiLanguageVo titleVo = new MultiLanguageVo(titleBo.getLangId(),
                com.chervon.operation.config.MultiLanguageUtil.getByLangCode(titleCode, LocaleContextHolder.getLocale().getLanguage()));
        result.setTitle(titleVo);
        // 获取模板内容多语言
        MultiLanguageBo contentBo = remoteMultiLanguageService.getById(messageTemplate.getContent().toString());
        String contentCode = contentBo.getLangCode() == null ? "" : contentBo.getLangCode();
        MultiLanguageVo contentVo = new MultiLanguageVo(contentBo.getLangId(),
                com.chervon.operation.config.MultiLanguageUtil.getByLangCode(contentCode, LocaleContextHolder.getLocale().getLanguage()));
        result.setContent(contentVo);
        return result;
    }
}
