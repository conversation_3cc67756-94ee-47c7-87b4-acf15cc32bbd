package com.chervon.operation.service.impl;

import com.alibaba.excel.EasyExcel;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.chervon.common.core.domain.PageResult;
import com.chervon.common.core.enums.ApplicationEnum;
import com.chervon.common.core.utils.DateTimeZoneUtil;
import com.chervon.common.core.utils.SnowFlake;
import com.chervon.common.mybatis.config.BaseDo;
import com.chervon.configuration.api.core.MultiLanguageBo;
import com.chervon.configuration.api.service.RemoteMultiLanguageService;
import com.chervon.operation.api.exception.OperationErrorCode;
import com.chervon.operation.config.ExceptionMessageUtil;
import com.chervon.operation.domain.bo.DictBo;
import com.chervon.operation.domain.bo.DictNodeBo;
import com.chervon.operation.domain.dataobject.*;
import com.chervon.operation.domain.dto.CommonOperationProductDto;
import com.chervon.operation.domain.dto.CommonOperationProductPageDto;
import com.chervon.operation.domain.dto.faq.common.*;
import com.chervon.operation.domain.vo.faq.FaqVo;
import com.chervon.operation.domain.vo.faq.common.CommonFaqExcel;
import com.chervon.operation.domain.vo.faq.common.CommonFaqManagePageVo;
import com.chervon.operation.domain.vo.faq.common.CommonFaqVo;
import com.chervon.operation.mapper.CommonFaqMapper;
import com.chervon.operation.service.*;
import com.chervon.technology.api.RemoteTechProductOperationService;
import com.chervon.technology.api.vo.CommonProductVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StopWatch;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.chervon.common.core.constant.CommonConstant.FAQ_ANSWER_SIZE;
import static com.chervon.operation.api.enums.GroupTypeEnum.USER_GROUP;
import static com.chervon.operation.api.exception.OperationErrorCode.*;

/**
 * <AUTHOR>
 * @date 2022/10/25 11:21
 */
@Service
@Slf4j
public class CommonFaqServiceImpl extends ServiceImpl<CommonFaqMapper, CommonFaq> implements CommonFaqService {

    @Autowired
    private FaqService faqService;

    @DubboReference
    private RemoteMultiLanguageService remoteMultiLanguageService;

    @DubboReference
    private RemoteTechProductOperationService remoteTechProductOperationService;

    @Autowired
    private CategoryService categoryService;


    @Autowired
    private ProductFaqService productFaqService;

    @Autowired
    private DictService dictService;

    @Autowired
    private GroupService groupService;

    private static final String FAQ_TYPE_CODE = "faqTypeCode";

    private static final String COMMON_FAQ_STATE = "commonFaqState";

    private List<Long> findTitleLangIds(String title) {
        List<Long> res = new ArrayList<>();
        if (StringUtils.isBlank(title)) {
            return res;
        }
        List<CommonFaq> list = this.list();
        List<Long> titleLangIds = list.stream().map(CommonFaq::getTitleLangId).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(titleLangIds)) {
            return res;
        }
        Map<@NotBlank String, @NotNull List<MultiLanguageBo>> map = remoteMultiLanguageService.listByTextLike(new HashMap<String, List<Long>>() {{
            put(title, titleLangIds);
        }}, LocaleContextHolder.getLocale().getLanguage());
        return map.get(title).stream().map(MultiLanguageBo::getLangId).collect(Collectors.toList());
    }

    @Override
    public PageResult<CommonFaqManagePageVo> managePage(CommonFaqManagePageDto req) {
        PageResult<CommonFaqManagePageVo> res = new PageResult<>(req.getPageNum(), req.getPageNum());
        List<Long> titleLangIds = findTitleLangIds(req.getTitle());
        // 如果没有匹配的多语言id，则返回空
        if (StringUtils.isNotBlank(req.getTitle()) && CollectionUtils.isEmpty(titleLangIds)) {
            return res;
        }
        IPage<CommonFaq> page = this.getBaseMapper().selectManagePage(new Page<>(req.getPageNum(), req.getPageSize()), req, titleLangIds);
        if (CollectionUtils.isEmpty(page.getRecords())) {
            return res;
        }
        res.setTotal(page.getTotal());
        List<Faq> faqList = faqService.listByIds(page.getRecords().stream().map(CommonFaq::getFaqId).filter(Objects::nonNull).distinct().collect(Collectors.toList()));
        Map<Long, Faq> map = faqList.stream().collect(Collectors.toMap(BaseDo::getId, Function.identity()));
        res.setList(page.getRecords().stream().map(e -> {
            CommonFaqManagePageVo vo = new CommonFaqManagePageVo();
            BeanUtils.copyProperties(e, vo);
            vo.setCommonFaqId(e.getId());
            FaqVo item = new FaqVo();
            Faq faq = map.getOrDefault(e.getFaqId(), new Faq());
            BeanUtils.copyProperties(faq, item);
            if (StringUtils.isNotBlank(faq.getTitleLangCode())) {
                item.setTitle(com.chervon.operation.config.MultiLanguageUtil.getByLangCode(faq.getTitleLangCode(), LocaleContextHolder.getLocale().getLanguage()));
            }
            if (StringUtils.isNotBlank(faq.getAnswerLangCode())) {
                item.setAnswer(com.chervon.operation.config.MultiLanguageUtil.getByLangCode(faq.getAnswerLangCode(), LocaleContextHolder.getLocale().getLanguage()));
            }
            vo.setFaq(item);
            vo.setCreateBy(e.getCreateBy());
            vo.setUpdateBy(e.getUpdateBy());
            vo.setCreateTime(e.getCreateTime());
            vo.setUpdateTime(e.getUpdateTime());
            return vo;
        }).collect(Collectors.toList()));
        return res;
    }



    @Transactional(rollbackFor = Exception.class)
    @Override
    public void add(CommonFaqDto req) {
        check(req);
        Faq faq = faqService.add(req.getFaq());
        CommonFaq one = new CommonFaq();
        one.setFaqId(faq.getId());
        one.setTitleLangId(faq.getTitleLangId());
        this.save(one);
    }

    private void check(CommonFaqDto req) {
        if (req.getFaq() == null) {
            throw ExceptionMessageUtil.getException(OperationErrorCode.OPERATION_COMMON_FAQ_NULL);
        }

    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void edit(CommonFaqDto req) {
        if (req.getCommonFaqId() == null) {
            throw ExceptionMessageUtil.getException(OperationErrorCode.OPERATION_COMMON_FAQ_ID_NULL);
        }
        CommonFaq common = this.getById(req.getCommonFaqId());
        if (common == null) {
            throw ExceptionMessageUtil.getException(OperationErrorCode.OPERATION_COMMON_FAQ_NULL);
        }
        check(req);
        req.getFaq().setFaqId(common.getFaqId());
        faqService.edit(req.getFaq());

    }

    @Override
    public CommonFaqVo detail(Long commonFaqId) {
        if (commonFaqId == null) {
            throw ExceptionMessageUtil.getException(OperationErrorCode.OPERATION_COMMON_FAQ_ID_NULL);
        }
        CommonFaq common = this.getById(commonFaqId);
        if (common == null) {
            throw ExceptionMessageUtil.getException(OperationErrorCode.OPERATION_COMMON_FAQ_NULL);
        }
        CommonFaqVo res = new CommonFaqVo();
        res.setCommonFaqId(common.getId());

        FaqVo item = new FaqVo();
        Faq faq = faqService.getById(common.getFaqId());
        if (faq != null) {
            BeanUtils.copyProperties(faq, item);
            item.setTitle(com.chervon.operation.config.MultiLanguageUtil.getByLangCode(faq.getTitleLangCode(), LocaleContextHolder.getLocale().getLanguage()));
            item.setAnswer(com.chervon.operation.config.MultiLanguageUtil.getByLangCode(faq.getAnswerLangCode(), LocaleContextHolder.getLocale().getLanguage()));
        }
        res.setFaq(item);
        return res;
    }

    @Override
    public PageResult<CommonProductVo> productPage(CommonOperationProductPageDto req) {
        if (req.getCommonId() == null) {
            throw ExceptionMessageUtil.getException(OperationErrorCode.OPERATION_COMMON_FAQ_ID_NULL);
        }
        CommonFaq common = this.getById(req.getCommonId());
        if (common == null) {
            throw ExceptionMessageUtil.getException(OperationErrorCode.OPERATION_COMMON_FAQ_NULL);
        }
        PageResult<CommonProductVo> res = new PageResult<>(req.getPageNum(), req.getPageSize());

        List<Long> productIds = productFaqService.listProductIdByCommonId(req.getCommonId());
        if (CollectionUtils.isEmpty(productIds)) {
            return res;
        }
        PageResult<CommonProductVo> page = remoteTechProductOperationService.page(LocaleContextHolder.getLocale().getLanguage(), productIds, req.getProductId(), req.getCategoryId(), req.getModel(), req.getCommodityModel(), req.getProductName(), req.getPageNum(), req.getPageSize());
        if (CollectionUtils.isEmpty(page.getList())) {
            return res;
        }
        //查询品类多语言
        List<Long> categoryIds = page.getList().stream().map(CommonProductVo::getCategoryId).collect(Collectors.toList());
        List<Category> categories = new ArrayList<>();
        if (!CollectionUtils.isEmpty(categoryIds)) {
            categories = categoryService.listByIds(categoryIds);
        }
        //查询产品多语言
        Map<Long, String> collect = categories.stream().collect(Collectors.toMap(BaseDo::getId, Category::getCategoryName));
        List<String> ids = new ArrayList<>();
        ids.addAll(collect.values());
        ids.addAll(page.getList().stream().map(CommonProductVo::getProductName).collect(Collectors.toList()));
        List<MultiLanguageBo> bos = new ArrayList<>();
        if (!CollectionUtils.isEmpty(ids)) {
            bos = remoteMultiLanguageService.listByIds(ids);
        }
        Map<Long, String> map = bos.stream().collect(Collectors.toMap(MultiLanguageBo::getLangId, MultiLanguageBo::getLangCode));
        page.getList().forEach(e -> {
            String nameId = collect.get(e.getCategoryId());
            String nameCode = map.get(Long.parseLong(nameId));
            e.setCategoryName(com.chervon.operation.config.MultiLanguageUtil.getByLangCode(Optional.ofNullable(nameCode).orElse(""), LocaleContextHolder.getLocale().getLanguage()));
            String productNameCode =StringUtils.isEmpty(e.getProductName())?null:map.get(Long.parseLong(e.getProductName()));
            e.setProductName(com.chervon.operation.config.MultiLanguageUtil.getByLangCode(Optional.ofNullable(productNameCode).orElse(""), LocaleContextHolder.getLocale().getLanguage()));

        });
        return page;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void productAdd(CommonOperationProductDto req) {
        if (req.getCommonId() == null) {
            throw ExceptionMessageUtil.getException(OperationErrorCode.OPERATION_COMMON_FAQ_ID_NULL);
        }
        CommonFaq common = this.getById(req.getCommonId());
        if (common == null) {
            throw ExceptionMessageUtil.getException(OperationErrorCode.OPERATION_COMMON_FAQ_NULL);
        }
        if (CollectionUtils.isEmpty(req.getRelatedProduct())) {
            return;
        }
        // 在产品faq添加通用部分
       addCommonToProduct(common, req.getRelatedProduct().stream().map(String::valueOf).distinct().collect(Collectors.toList()));
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void productDelete(CommonOperationProductDto req) {
        if (req.getCommonId() == null) {
            throw ExceptionMessageUtil.getException(OperationErrorCode.OPERATION_COMMON_FAQ_ID_NULL);
        }
        CommonFaq common = this.getById(req.getCommonId());
        if (common == null) {
            throw ExceptionMessageUtil.getException(OperationErrorCode.OPERATION_COMMON_FAQ_NULL);
        }
        if (CollectionUtils.isEmpty(req.getRelatedProduct())) {
            return;
        }
        // 在产品faq删除通用部分
        deleteCommonFromProduct(common, req.getRelatedProduct().stream().map(String::valueOf).collect(Collectors.toList()));
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void delete(Long commonFaqId) {
        if (commonFaqId == null) {
            throw ExceptionMessageUtil.getException(OperationErrorCode.OPERATION_COMMON_FAQ_ID_NULL);
        }
        CommonFaq common = this.getById(commonFaqId);
        if (common == null) {
            throw ExceptionMessageUtil.getException(OperationErrorCode.OPERATION_COMMON_FAQ_NULL);
        } else {
            cleanLanguage(common);
        }
        //判断是否绑定了产品
        if (productFaqService.countProductIdByCommonId(commonFaqId)>0) {
            throw ExceptionMessageUtil.getException(OperationErrorCode.OPERATION_COMMON_FAQ_RELATED_PRODUCT_CANNOT_DELETE);
        }
        faqService.removeById(common.getFaqId());
        removeById(common.getId());
    }

    private void cleanLanguage(CommonFaq common) {
        // 清理多语言
        List<Long> langIds = new ArrayList<>();
        if (common.getTitleLangId() != null) {
            langIds.add(common.getTitleLangId());
        }
        Faq faq = faqService.getById(common.getFaqId());
        if (faq != null) {
            if(faq.getAnswerLangId() != null) {
                langIds.add(faq.getAnswerLangId());
            }
            if(faq.getTitleLangId() != null) {
                langIds.add(faq.getTitleLangId());
            }
        }
        if (langIds.size() > 0) {
            remoteMultiLanguageService.deleteByLangIds(langIds);
        }
    }


    private void addCommonToProduct(CommonFaq common, List<String> pIds) {
        //校验参数产品id集合是否被绑定过
        productFaqService.checkBoundByCommonIdAndPIds(common.getId(),pIds);
        //组装批量保存
        List<ProductFaq> data = new ArrayList<>();
        pIds.forEach(e -> {
            ProductFaq instance = new ProductFaq();
            instance.setProductId(Long.parseLong(e));
            instance.setCommonId(common.getId());
            instance.setFaqId(common.getFaqId());
            instance.setSequence(0);
            data.add(instance);
        });
        productFaqService.saveBatch(data);

    }


    private void deleteCommonFromProduct(CommonFaq common, List<String> pIds) {
        if (!CollectionUtils.isEmpty(pIds)) {
            productFaqService.remove(new LambdaQueryWrapper<ProductFaq>()
                    .eq(ProductFaq::getCommonId, common.getId())
                    .in(ProductFaq::getProductId, pIds));
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<String> importCommonFaq(MultipartFile file) {
        StopWatch sw = new StopWatch();
        List<String> res = new ArrayList<>();
        List<CommonFaqRead> data;
        try {
            sw.start("read data");
            data = EasyExcel.read(file.getInputStream()).head(CommonFaqRead.class).sheet().doReadSync();
            sw.stop();
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw ExceptionMessageUtil.getException(OPERATION_COMMON_FAQ_READ_EXCEL_ERROR);
        }
        if (CollectionUtils.isEmpty(data)) {
            throw ExceptionMessageUtil.getException(OPERATION_COMMON_FAQ_READ_EXCEL_EMPTY);
        }
        if (data.size() > 5000) {
            throw ExceptionMessageUtil.getException(OPERATION_COMMON_FAQ_READ_EXCEL_MORE_THAN_5000);
        }
        sw.start("valid");
        if (!validImport(data, res)) {
            return res;
        }
        sw.stop();
        List<String> update = new ArrayList<>();
        List<String> collect = data.stream().map(CommonFaqRead::getInstanceId).collect(Collectors.toList());
        List<Faq> orFaq = new ArrayList<>();
        sw.start("load old faq");
        if (!CollectionUtils.isEmpty(collect)) {
            orFaq = faqService.list(new LambdaQueryWrapper<Faq>().in(Faq::getInstanceId, collect));
        }
        sw.stop();
        Map<Long, Faq> orFaqMap = orFaq.stream().collect(Collectors.toMap(Faq::getInstanceId, Function.identity()));

        Map<Long, String> mUpdate = new HashMap<>();
        Map<String, String> mCreate = new HashMap<>();
        sw.start("build data");
        final int[] flag = {1};
        List<Faq> list = data.stream().map(e -> {
            Faq faq = new Faq();
            faq.setTitle(e.getTitle());
            faq.setAnswer(e.getAnswer());
            faq.setTypeCode(e.getTypeCode());
            if (StringUtils.isNotBlank(e.getInstanceId())) {
                Faq f = orFaqMap.get(Long.parseLong(e.getInstanceId()));
                if (f != null) {
                    update.add(String.valueOf(f.getId()));
                    faq.setId(f.getId());
                    if (f.getTitleLangId() != null) {
                        mUpdate.put(f.getTitleLangId(), e.getTitle());
                    } else {
                        mCreate.put(flag[0] + "", e.getTitle());
                        faq.setTitle(flag[0] + "");
                        flag[0]++;
                    }
                    if (f.getAnswerLangId() != null) {
                        mUpdate.put(f.getAnswerLangId(), e.getAnswer());
                    } else {
                        mCreate.put(flag[0] + "", e.getAnswer());
                        faq.setAnswer(flag[0] + "");
                        flag[0]++;
                    }
                } else {
                    faq.setInstanceId(SnowFlake.nextId());
                    mCreate.put(flag[0] + "", e.getTitle());
                    faq.setTitle(flag[0] + "");
                    flag[0]++;
                    mCreate.put(flag[0] + "", e.getAnswer());
                    faq.setAnswer(flag[0] + "");
                    flag[0]++;
                }
            } else {
                faq.setInstanceId(SnowFlake.nextId());
                mCreate.put(flag[0] + "", e.getTitle());
                faq.setTitle(flag[0] + "");
                flag[0]++;
                mCreate.put(flag[0] + "", e.getAnswer());
                faq.setAnswer(flag[0] + "");
                flag[0]++;
            }
            return faq;
        }).collect(Collectors.toList());
        sw.stop();
        sw.start("update");
        if (mUpdate.size() > 0) {
            remoteMultiLanguageService.simpleUpdateMultiLanguages(ApplicationEnum.OPERATION_PLATFORM.getName(), mUpdate, LocaleContextHolder.getLocale().getLanguage());
        }
        sw.stop();
        if (mCreate.size() > 0) {
            sw.start("create language");
            Map<String, MultiLanguageBo> boMap = remoteMultiLanguageService.simpleCreateMultiLanguages(ApplicationEnum.OPERATION_PLATFORM.getName(), mCreate, LocaleContextHolder.getLocale().getLanguage());
            sw.stop();
            list.forEach(e -> {
                if (e.getTitleLangId() == null) {
                    MultiLanguageBo bo = boMap.get(e.getTitle());
                    e.setTitleLangId(bo.getLangId());
                    e.setTitleLangCode(bo.getLangCode());
                    e.setTitle(mCreate.get(e.getTitle()));

                }
                if (e.getAnswerLangId() == null) {
                    MultiLanguageBo bo = boMap.get(e.getAnswer());
                    e.setAnswerLangId(bo.getLangId());
                    e.setAnswerLangCode(bo.getLangCode());
                    e.setAnswer(mCreate.get(e.getAnswer()));
                }
            });
        }
        sw.start("faq save or update");
        faqService.saveOrUpdateBatch(list);
        sw.stop();
        List<CommonFaq> insert = new ArrayList<>();
        list.forEach(e -> {
            if (!update.contains(String.valueOf(e.getId()))) {
                insert.add(new CommonFaq() {{
                    this.setFaqId(e.getId());
                    this.setTitleLangId(e.getTitleLangId());
                }});
            }
        });
        sw.start("common faq save");
        if (insert.size() > 0) {
            this.saveBatch(insert);
        }
        sw.stop();
        log.error(sw.prettyPrint());
        return res;
    }

    private boolean validImport(List<CommonFaqRead> data, List<String> res) {
        List<DictBo> dictList = dictService.listByDictName(LocaleContextHolder.getLocale().getLanguage(), Collections.singletonList(FAQ_TYPE_CODE));
        Map<String, Map<String, String>> dictMap = new HashMap<>();
        dictList.forEach(e -> dictMap.put(e.getDictName(), e.getNodes().stream().filter(i -> StringUtils.isNotBlank(i.getDescription())).collect(Collectors.toMap(DictNodeBo::getDescription, DictNodeBo::getLabel))));
        Map<String, String> typeCode = dictMap.getOrDefault(FAQ_TYPE_CODE, new HashMap<>());
        List<Integer> idString = new ArrayList<>();
        Map<String, List<Integer>> idMap = new HashMap<>();
        List<Integer> titleNullFlag = new ArrayList<>();
        List<Integer> answerNullFlag = new ArrayList<>();
        List<Integer> typeCodeNullFlag = new ArrayList<>();
        List<Integer> typeCodeFlag = new ArrayList<>();
        List<Integer> titleSizeFlag = new ArrayList<>();
        List<Integer> answerSizeFlag = new ArrayList<>();

        for (int i = 2, j = data.size() + 2; i < j; i++) {
            CommonFaqRead read = data.get(i - 2);

            if (StringUtils.isNotBlank(read.getInstanceId())) {
                try {
                    Long.parseLong(read.getInstanceId());
                } catch (Exception e) {
                    idString.add(i);
                }
                if (idMap.get(read.getInstanceId()) == null) {
                    List<Integer> ids = new ArrayList<>();
                    ids.add(i);
                    idMap.put(read.getInstanceId(), ids);
                } else {
                    idMap.get(read.getInstanceId()).add(i);
                }
            }

            if (StringUtils.isBlank(read.getTypeCode())) {
                typeCodeNullFlag.add(i);
            } else if (!typeCode.containsKey(read.getTypeCode())) {
                typeCodeFlag.add(i);
            } else {
                read.setTypeCode(typeCode.get(read.getTypeCode()));
            }

            // 20230619修改标题导入字数限制从128到3000
            if (StringUtils.isBlank(read.getTitle())) {
                titleNullFlag.add(i);
            } else if (read.getTitle().length() > 3000) {
                titleSizeFlag.add(i);
            }

            if (StringUtils.isBlank(read.getAnswer())) {
                answerNullFlag.add(i);
            } else if (read.getAnswer().length() > FAQ_ANSWER_SIZE) {
                answerSizeFlag.add(i);
            }

        }
        // 检测id是否为数字
        if (idString.size() > 0) {
            res.add("第" + idString.stream().map(String::valueOf).collect(Collectors.joining("，")) + "行，【问题ID】非法");
        }
        // 检测id是否唯一
        for (Map.Entry<String, List<Integer>> entry : idMap.entrySet()) {
            List<Integer> value = entry.getValue();
            if (value.size() != 1) {
                res.add("第" + value.stream().map(String::valueOf).collect(Collectors.joining("，")) + "行，【问题ID】重复");
            }
        }
        // 检测typeCode是否必填
        if (typeCodeNullFlag.size() > 0) {
            res.add("第" + typeCodeNullFlag.stream().map(String::valueOf).collect(Collectors.joining("，")) + "行，【问题类型】为空");
        }
        // 检测typeCode是否正确
        if (typeCodeFlag.size() > 0) {
            res.add("第" + typeCodeFlag.stream().map(String::valueOf).collect(Collectors.joining("，")) + "行，【问题类型】不正确");
        }
        // 检测title是否必填
        if (titleNullFlag.size() > 0) {
            res.add("第" + titleNullFlag.stream().map(String::valueOf).collect(Collectors.joining("，")) + "行，【问题】为空");
        }
        // 检测title长度
        if (titleSizeFlag.size() > 0) {
            res.add("第" + titleSizeFlag.stream().map(String::valueOf).collect(Collectors.joining("，")) + "行，【问题】内容超过" + 3000 + "个字符");
        }
        // 检测answer是否必填
        if (answerNullFlag.size() > 0) {
            res.add("第" + answerNullFlag.stream().map(String::valueOf).collect(Collectors.joining("，")) + "行，【解决办法】为空");
        }
        // 检测answer长度
        if (answerSizeFlag.size() > 0) {
            res.add("第" + answerSizeFlag.stream().map(String::valueOf).collect(Collectors.joining("，")) + "行，【解决办法】内容超过" + FAQ_ANSWER_SIZE + "个字符");
        }

        if (!res.isEmpty()) {
            return false;
        }

        List<Long> ids = data.stream().filter(e -> StringUtils.isNotBlank(e.getInstanceId())).map(e -> Long.parseLong(e.getInstanceId())).distinct().collect(Collectors.toList());
        if (!ids.isEmpty()) {
            List<Faq> faqs = faqService.list(new LambdaQueryWrapper<Faq>().in(Faq::getInstanceId, ids));
            List<Long> existIds = faqs.stream().map(Faq::getInstanceId).collect(Collectors.toList());
            for (int i = 2, j = data.size() + 2; i < j; i++) {
                CommonFaqRead read = data.get(i - 2);
                if (StringUtils.isBlank(read.getInstanceId())) {
                    continue;
                }
                if (!existIds.contains(Long.parseLong(read.getInstanceId()))) {
                    res.add("第" + i + "行，【问题ID】不存在");
                }
            }
            if (!res.isEmpty()) {
                return false;
            }
            Map<Long, Long> instanceIdIdMap = faqs.stream().collect(Collectors.toMap(Faq::getInstanceId, BaseDo::getId));
            List<CommonFaq> commonFaqs = this.list(new LambdaQueryWrapper<CommonFaq>().in(CommonFaq::getFaqId, instanceIdIdMap.values()));
            Map<Long, CommonFaq> commonFaqMap = commonFaqs.stream().collect(Collectors.toMap(CommonFaq::getFaqId, Function.identity()));
            for (int i = 2, j = data.size() + 2; i < j; i++) {
                CommonFaqRead read = data.get(i - 2);
                if (StringUtils.isBlank(read.getInstanceId())) {
                    continue;
                }
                CommonFaq commonFaq = commonFaqMap.get(instanceIdIdMap.get(Long.parseLong(read.getInstanceId())));
                if (commonFaq == null) {
                    res.add("第" + i + "行，【问题ID】不存在");
                }
            }
        }
        return res.isEmpty();
    }

    @Override
    public List<CommonFaqExcel> listData(CommonFaqManagePageDto req) {
        List<CommonFaqExcel> res = new ArrayList<>();
        List<Long> titleLangIds = findTitleLangIds(req.getTitle());
        // 如果没有匹配的多语言id，则返回空
        if (StringUtils.isNotBlank(req.getTitle()) && CollectionUtils.isEmpty(titleLangIds)) {
            return res;
        }
        List<CommonFaq> list = this.getBaseMapper().selectManageList(req, titleLangIds);
        if (CollectionUtils.isEmpty(list)) {
            return res;
        }

        List<Faq> faqList = faqService.listByIds(list.stream().map(CommonFaq::getFaqId).filter(Objects::nonNull).distinct().collect(Collectors.toList()));
        Map<Long, Faq> map = faqList.stream().collect(Collectors.toMap(BaseDo::getId, Function.identity()));
        List<DictBo> dictList = dictService.listByDictName(LocaleContextHolder.getLocale().getLanguage(), Arrays.asList(COMMON_FAQ_STATE, FAQ_TYPE_CODE));
        Map<String, DictBo> collect = dictList.stream().collect(Collectors.toMap(DictBo::getDictName, Function.identity()));
        return list.stream().map(e -> {
            CommonFaqExcel vo = new CommonFaqExcel();
            Faq faq = map.getOrDefault(e.getFaqId(), new Faq());
            BeanUtils.copyProperties(faq, vo);
            vo.setTitle(com.chervon.operation.config.MultiLanguageUtil.getByLangCode(Optional.ofNullable(faq.getTitleLangCode()).orElse(""), LocaleContextHolder.getLocale().getLanguage()));
            vo.setAnswer(com.chervon.operation.config.MultiLanguageUtil.getByLangCode(Optional.ofNullable(faq.getAnswerLangCode()).orElse(""), LocaleContextHolder.getLocale().getLanguage()));
            vo.setCreateTime(DateTimeZoneUtil.format(e.getCreateTime(), req.getZone()));
            vo.setUpdateTime(DateTimeZoneUtil.format(e.getUpdateTime(), req.getZone()));
            vo.setCreateBy(e.getCreateBy());
            vo.setUpdateBy(e.getUpdateBy());

            // 设置问题类型
            vo.setType(collect.get(FAQ_TYPE_CODE).getNodes()
                    .stream()
                    .filter(i -> StringUtils.equals(i.getLabel(), faq.getTypeCode()))
                    .findFirst()
                    .orElse(new DictNodeBo())
                    .getDescription());
            return vo;
        }).collect(Collectors.toList());
    }

    @Override
    public List<Long> productIdList(Long commonFaqId) {
        if (commonFaqId == null) {
            throw ExceptionMessageUtil.getException(OperationErrorCode.OPERATION_COMMON_FAQ_ID_NULL);
        }
        CommonFaq common = this.getById(commonFaqId);
        if (common == null) {
            throw ExceptionMessageUtil.getException(OperationErrorCode.OPERATION_COMMON_FAQ_NULL);
        }
        return productFaqService.listProductIdByCommonId(commonFaqId);
    }

}
