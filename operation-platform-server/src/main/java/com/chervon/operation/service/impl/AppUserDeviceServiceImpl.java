package com.chervon.operation.service.impl;

import com.chervon.common.core.domain.PageResult;
import com.chervon.common.core.utils.UUIDUtils;
import com.chervon.common.mybatis.config.BaseDo;
import com.chervon.configuration.api.core.MultiLanguageBo;
import com.chervon.configuration.api.service.RemoteMultiLanguageService;
import com.chervon.iot.app.api.RemoteAppDeviceService;
import com.chervon.iot.app.api.vo.UserIdDeviceIdVo;
import com.chervon.operation.domain.bo.DictBo;
import com.chervon.operation.domain.bo.DictNodeBo;
import com.chervon.operation.domain.dataobject.Brand;
import com.chervon.operation.domain.dataobject.Category;
import com.chervon.operation.domain.dto.app.AppUserDevicePageDto;
import com.chervon.operation.domain.vo.app.AppUserDeviceExcel;
import com.chervon.operation.domain.vo.app.AppUserDeviceVo;
import com.chervon.operation.service.AppUserDeviceService;
import com.chervon.operation.service.BrandService;
import com.chervon.operation.service.CategoryService;
import com.chervon.operation.service.DictService;
import com.chervon.technology.api.RemoteDeviceIdService;
import com.chervon.technology.api.dto.DeviceProductDto;
import com.chervon.usercenter.api.service.RemoteAppUserService;
import com.chervon.usercenter.api.vo.AppUserVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/8/20 15:05
 */
@Service
@Slf4j
public class AppUserDeviceServiceImpl implements AppUserDeviceService {

    @DubboReference
    private RemoteDeviceIdService remoteDeviceIdService;

    @DubboReference
    private RemoteAppDeviceService remoteAppDeviceService;

    @DubboReference
    private RemoteAppUserService remoteAppUserService;

    @DubboReference
    private RemoteMultiLanguageService remoteMultiLanguageService;

    @Autowired
    private CategoryService categoryService;

    @Autowired
    private BrandService brandService;

    @Autowired
    private DictService dictService;

    @Override
    public PageResult<AppUserDeviceVo> page(AppUserDevicePageDto req) {
        PageResult<AppUserDeviceVo> res = new PageResult<>(req.getPageNum(), req.getPageSize());
        // 目前所有设备都是来源app
        if (StringUtils.isNotBlank(req.getDeviceSource()) && !StringUtils.equals(req.getDeviceSource(), "app")) {
            return res;
        }

        List<Long> userIds =new ArrayList<>();
        List<String> deviceIds =new ArrayList<>();
        if(StringUtils.isNotBlank(req.getBindDeviceId())||StringUtils.isNotBlank(req.getBindDeviceSn())||!Objects.isNull(req.getBindDeviceCategoryId())
                ||!Objects.isNull(req.getBindDeviceBrandId())||StringUtils.isNotBlank(req.getModel())||StringUtils.isNotBlank(req.getCommodityModel())){
            deviceIds = remoteDeviceIdService.listDeviceIdByProductInfo(req.getBindDeviceId(), req.getBindDeviceSn(), req.getBindDeviceCategoryId(), req.getBindDeviceBrandId(), req.getModel(), req.getCommodityModel());
            if (CollectionUtils.isEmpty(deviceIds)) {
                return res;
            }
        }
        if(StringUtils.isNotBlank(req.getUserId())||!CollectionUtils.isEmpty(deviceIds)){
             userIds = remoteAppDeviceService.listUserIdByUserIdAndDeviceIds(req.getUserId(), deviceIds);
            if (CollectionUtils.isEmpty(userIds)) {
                return res;
            }
        }

        PageResult<AppUserVo> appUsers = remoteAppUserService.pageAppUserByEmailAndUserIds(req.getEmail(), userIds, req.getPageNum(), req.getPageSize());
        if (CollectionUtils.isEmpty(appUsers.getList())) {
            return res;
        }
        res.setTotal(appUsers.getTotal());
        List<AppUserDeviceVo> list = new ArrayList<>();
        res.setList(list);
        appUsers.getList().forEach(e -> {
            AppUserDeviceVo appUserDeviceVo = new AppUserDeviceVo();
            appUserDeviceVo.setId(UUIDUtils.randomShortUUID());
            appUserDeviceVo.setUserId(e.getUserId());
            appUserDeviceVo.setEmail(e.getEmail());
            list.add(appUserDeviceVo);
        });

        List<UserIdDeviceIdVo> returnDeviceIds = remoteAppDeviceService.listDeviceIdByUserIds(list.stream().map(u ->
                Long.valueOf(u.getUserId())).collect(Collectors.toList()));
        Map<Long, List<UserIdDeviceIdVo>> userIdDeviceGroup = returnDeviceIds.stream()
                .collect(Collectors.groupingBy(UserIdDeviceIdVo::getUserId));

        List<DeviceProductDto> deviceProducts = remoteDeviceIdService.listDeviceProductByDeviceIds(returnDeviceIds.stream().map(UserIdDeviceIdVo::getDeviceId).distinct().collect(Collectors.toList()));
        Map<String, DeviceProductDto> collect = deviceProducts.stream().collect(Collectors.toMap(DeviceProductDto::getBindDeviceId, Function.identity()));
        list.forEach(e -> {
            List<UserIdDeviceIdVo> userIdDeviceIds = userIdDeviceGroup.getOrDefault(Long.valueOf(e.getUserId()), new ArrayList<>());
            userIdDeviceIds.forEach(n -> {
                DeviceProductDto deviceProduct = collect.getOrDefault(n.getDeviceId(), new DeviceProductDto());
                // 目前所有设备都是来源app
                e.getDeviceSource().add("app");
                e.getBindDeviceId().add(deviceProduct.getBindDeviceId());
                e.getBindDeviceSn().add(deviceProduct.getBindDeviceSn());
                e.getShareType().add(n.getShareType());
                e.getBindDeviceCategoryId().add(deviceProduct.getBindDeviceCategoryId());
                e.getBindDeviceBrandId().add(deviceProduct.getBindDeviceBrandId());
                e.getModel().add(deviceProduct.getModel());
                e.getCommodityModel().add(deviceProduct.getCommodityModel());
            });
        });
        log.info("page res:{}", res);
        return res;
    }

    @Override
    public List<AppUserDeviceExcel> listData(AppUserDevicePageDto req) {
        List<AppUserDeviceExcel> res = new ArrayList<>();
        // 目前所有设备都是来源app
        if (StringUtils.isNotBlank(req.getDeviceSource()) && !StringUtils.equals(req.getDeviceSource(), "app")) {
            return res;
        }
        List<String> deviceIds = remoteDeviceIdService.listDeviceIdByProductInfo(req.getBindDeviceId(), req.getBindDeviceSn(), req.getBindDeviceCategoryId(), req.getBindDeviceBrandId(), req.getModel(), req.getCommodityModel());
        if (CollectionUtils.isEmpty(deviceIds)) {
            return res;
        }
        List<Long> userIds = remoteAppDeviceService.listUserIdByUserIdAndDeviceIds(req.getUserId(), deviceIds);
        if (CollectionUtils.isEmpty(userIds)) {
            return res;
        }
        List<AppUserVo> appUsers = remoteAppUserService.listAppUserByEmailAndUserIds(req.getEmail(), userIds);
        if (CollectionUtils.isEmpty(appUsers)) {
            return res;
        }

        List<UserIdDeviceIdVo> returnDeviceIds = remoteAppDeviceService
                .listDeviceIdByUserIds(appUsers.stream().map(u ->
                        u.getUserId() != null ? Long.valueOf(u.getUserId()) : null
                ).collect(Collectors.toList()));

        Map<Long, List<String>> userIdDeviceGroup = returnDeviceIds.stream()
                .collect(Collectors.groupingBy(UserIdDeviceIdVo::getUserId, Collectors.mapping(UserIdDeviceIdVo::getDeviceId, Collectors.toList())));

        List<DeviceProductDto> deviceProducts = remoteDeviceIdService.listDeviceProductByDeviceIds(returnDeviceIds.stream().map(UserIdDeviceIdVo::getDeviceId).distinct().collect(Collectors.toList()));
        Map<String, DeviceProductDto> collect = deviceProducts.stream().collect(Collectors.toMap(DeviceProductDto::getBindDeviceId, Function.identity()));

        List<Category> categories = categoryService.listByIds(deviceProducts.stream().map(DeviceProductDto::getBindDeviceCategoryId).distinct().collect(Collectors.toList()));
        Map<Long, String> categoryIdLangIdMap = categories.stream().collect(Collectors.toMap(BaseDo::getId, Category::getCategoryName));
        List<MultiLanguageBo> categoryLang = remoteMultiLanguageService.listByIds(categories.stream().map(Category::getCategoryName).filter(Objects::nonNull).distinct().collect(Collectors.toList()));
        Map<String, String> categoryIdMap = categoryLang.stream().collect(Collectors.toMap(e -> e.getLangId() + "", MultiLanguageBo::getLangCode));

        List<Brand> brands = brandService.listByIds(deviceProducts.stream().map(DeviceProductDto::getBindDeviceBrandId).distinct().collect(Collectors.toList()));
        Map<Long, String> brandIdLangIdMap = brands.stream().collect(Collectors.toMap(BaseDo::getId, Brand::getBrandName));
        List<MultiLanguageBo> brandLang = remoteMultiLanguageService.listByIds(brands.stream().map(Brand::getBrandName).filter(Objects::nonNull).distinct().collect(Collectors.toList()));
        Map<String, String> brandIdMap = brandLang.stream().collect(Collectors.toMap(e -> e.getLangId() + "", MultiLanguageBo::getLangCode));

        List<DictBo> dictList = dictService.listByDictName(LocaleContextHolder.getLocale().getLanguage(), Collections.singletonList("deviceSource"));
        Map<String, DictBo> dictMap = dictList.stream().collect(Collectors.toMap(DictBo::getDictName, Function.identity()));
        String appTypeDescription = dictMap.get("deviceSource").getNodes().stream()
                .filter(i -> StringUtils.equals(i.getLabel(), "app"))
                .findFirst()
                .orElse(new DictNodeBo())
                .getDescription();

        appUsers.forEach(e -> {
            List<String> userIdDeviceIds = userIdDeviceGroup.getOrDefault(e.getUserId() != null ? Long.valueOf(e.getUserId()) : null, new ArrayList<>());
            userIdDeviceIds.forEach(n -> {
                AppUserDeviceExcel excel = new AppUserDeviceExcel();
                excel.setEmail(e.getEmail());
                excel.setUserId(e.getUserId() != null ? Long.valueOf(e.getUserId()) : null);
                DeviceProductDto deviceProduct = collect.getOrDefault(n, new DeviceProductDto());
                excel.setBindDeviceId(deviceProduct.getBindDeviceId());
                excel.setBindDeviceSn(deviceProduct.getBindDeviceSn());
                String categoryLangId = categoryIdLangIdMap.getOrDefault(deviceProduct.getBindDeviceCategoryId(), "");
                String categoryLangCode = categoryIdMap.getOrDefault(categoryLangId, "");
                if (categoryLangCode != null) {
                    excel.setBindDeviceCategory(com.chervon.operation.config.MultiLanguageUtil.getByLangCode(categoryLangCode, LocaleContextHolder.getLocale().getLanguage()));
                }
                String brandLangId = brandIdLangIdMap.getOrDefault(deviceProduct.getBindDeviceBrandId(), "");
                String brandLangCode = brandIdMap.getOrDefault(brandLangId, "");
                if (brandLangCode != null) {
                    excel.setBindDeviceBrand(com.chervon.operation.config.MultiLanguageUtil.getByLangCode(brandLangCode, LocaleContextHolder.getLocale().getLanguage()));
                }
                excel.setModel(deviceProduct.getModel());
                excel.setCommodityModel(deviceProduct.getCommodityModel());
                excel.setDeviceSource(appTypeDescription);
                res.add(excel);
            });
        });
        return res;
    }
}
