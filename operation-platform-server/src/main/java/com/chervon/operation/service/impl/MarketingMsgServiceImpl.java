package com.chervon.operation.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.chervon.common.core.constant.CommonConstant;
import com.chervon.common.core.domain.PageResult;
import com.chervon.common.core.enums.ApplicationEnum;
import com.chervon.common.core.utils.DateTimeZoneUtil;
import com.chervon.common.mybatis.util.LoginUserUtil;
import com.chervon.configuration.api.core.MultiLanguageBo;
import com.chervon.configuration.api.service.RemoteMultiLanguageService;
import com.chervon.iot.app.api.RemoteUserSettingService;
import com.chervon.iot.app.api.vo.UserSettingBo;
import com.chervon.message.api.RemoteMessageService;
import com.chervon.message.api.dto.MessageDto;
import com.chervon.message.api.enums.OsType;
import com.chervon.message.api.enums.PushMethodEnum;
import com.chervon.operation.api.dto.MessagePushResultCountDto;
import com.chervon.operation.api.exception.OperationErrorCode;
import com.chervon.operation.config.ExceptionMessageUtil;
import com.chervon.operation.config.MarketingMsgOperationEnum;
import com.chervon.operation.config.MarketingMsgStateEnum;
import com.chervon.operation.domain.dataobject.AppRecord;
import com.chervon.operation.domain.dataobject.Groups;
import com.chervon.operation.domain.dataobject.MarketingMsg;
import com.chervon.operation.domain.dto.app.MarketingMsgDto;
import com.chervon.operation.domain.dto.app.MarketingMsgManagePageDto;
import com.chervon.operation.domain.dto.app.MarketingMsgOperationDto;
import com.chervon.operation.domain.dto.app.MarketingMsgReleasePageDto;
import com.chervon.operation.domain.vo.app.MarketingMsgManagePageVo;
import com.chervon.operation.domain.vo.app.MarketingMsgReleasePageVo;
import com.chervon.operation.domain.vo.app.MarketingMsgVo;
import com.chervon.operation.domain.vo.message.MessageRecordResultVo;
import com.chervon.operation.domain.vo.message.MessageRecordSearchVo;
import com.chervon.operation.mapper.MarketingMsgMapper;
import com.chervon.operation.rpc.RemoteGroupServiceImpl;
import com.chervon.operation.service.AppRecordService;
import com.chervon.operation.service.GroupService;
import com.chervon.operation.service.MarketingMsgService;
import com.chervon.operation.service.XxlJobService;
import com.chervon.usercenter.api.service.RemoteAppUserService;
import com.chervon.usercenter.api.vo.AppUserVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

import static com.chervon.operation.api.exception.OperationErrorCode.*;
import static com.chervon.operation.config.MarketingMsgOperationEnum.*;
import static com.chervon.operation.config.MarketingMsgStateEnum.*;

/**
 * <AUTHOR>
 * @date 2022/8/20 18:54
 */
@SuppressWarnings("Duplicates")
@Service
@Slf4j
public class MarketingMsgServiceImpl extends ServiceImpl<MarketingMsgMapper, MarketingMsg> implements MarketingMsgService {
    @Autowired
    private AppRecordService appRecordService;
    @Autowired
    private RemoteGroupServiceImpl remoteGroupServiceImpl;
    @Autowired
    private XxlJobService xxlJobService;

    @DubboReference
    private RemoteMultiLanguageService remoteMultiLanguageService;
    @DubboReference
    private RemoteMessageService remoteMessageService;
    @DubboReference
    private RemoteAppUserService remoteAppUserService;
    @DubboReference
    private RemoteUserSettingService remoteUserSettingService;

    @Autowired
    private GroupService groupService;

    private List<Long> findTitleLangIds(String title) {
        List<Long> res = new ArrayList<>();
        if (StringUtils.isEmpty(title)) {
            return res;
        }
        List<MarketingMsg> list = this.list();
        List<Long> titleLangIds = list.stream().map(MarketingMsg::getTitleLangId).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(titleLangIds)) {
            return res;
        }
        Map<@NotBlank String, @NotNull List<MultiLanguageBo>> map = remoteMultiLanguageService.listByTextLike(new HashMap<String, List<Long>>() {{
            put(title, titleLangIds);
        }}, LocaleContextHolder.getLocale().getLanguage());
        return map.get(title).stream().map(MultiLanguageBo::getLangId).collect(Collectors.toList());
    }

    @Override
    public PageResult<MarketingMsgManagePageVo> managePage(MarketingMsgManagePageDto req) {
        PageResult<MarketingMsgManagePageVo> res = new PageResult<>(req.getPageNum(), req.getPageNum());
        List<Long> titleLangIds = findTitleLangIds(req.getTitle());
        // 如果没有匹配的多语言id，则返回空
        if (StringUtils.isNotBlank(req.getTitle()) && CollectionUtils.isEmpty(titleLangIds)) {
            return res;
        }
        if (!CollectionUtils.isEmpty(req.getPushTypeCode())) {
            req.setPushTypeCodes(String.join("|", req.getPushTypeCode()));
        }
        IPage<MarketingMsg> page = this.getBaseMapper().selectManagePage(new Page<>(req.getPageNum(), req.getPageSize()), req, titleLangIds);
        if (CollectionUtils.isEmpty(page.getRecords())) {
            return res;
        }
        res.setTotal(page.getTotal());
        List<MarketingMsgManagePageVo> list = new ArrayList<>();
        page.getRecords().forEach(e -> {
            MarketingMsgManagePageVo vo = new MarketingMsgManagePageVo();
            BeanUtils.copyProperties(e, vo);
            vo.setMarketingMsgId(e.getId());
            vo.setTitle(com.chervon.operation.config.MultiLanguageUtil.getByLangCode(Optional.ofNullable(e.getTitleLangCode()).orElse(""), LocaleContextHolder.getLocale().getLanguage()));
            if (StringUtils.isNotBlank(e.getPushTypeCode())) {
                vo.setPushTypeCodes(new ArrayList<>(Arrays.asList(e.getPushTypeCode().split(","))));
            }
            // 设置操作
            handleManagePageOperation(e, vo);
            list.add(vo);
        });
        res.setList(list);
        return res;
    }

    private void handleManagePageOperation(MarketingMsg marketingMsg, MarketingMsgManagePageVo vo) {
        MarketingMsgStateEnum statusE = MarketingMsgStateEnum.getFromCode(marketingMsg.getStatusCode());
        Arrays.stream(statusE.getOperations()).forEach(e -> {
                    switch (e) {
                        case VIEW:
                            vo.setCanView(true);
                            break;
                        case UPDATE:
                            vo.setCanUpdate(true);
                            break;
                        case DELETE:
                            vo.setCanDelete(true);
                            break;
                        case COPY:
                            vo.setCanCopy(true);
                            break;
                        case APPLY_RELEASE:
                            vo.setCanApplyRelease(true);
                            break;
                        case CANCEL_APPLY_RELEASE:
                            vo.setCanCancelApplyRelease(true);
                            break;
                        case APPLY_STOP_RELEASE:
                            vo.setCanApplyStopRelease(true);
                            break;
                        case CANCEL_APPLY_STOP_RELEASE:
                            vo.setCanCancelApplyStopRelease(true);
                            break;
                        case VIEW_REFUSE_RELEASE_REASON:
                            vo.setCanViewRefuseReleaseReason(true);
                            break;
                        case VIEW_REFUSE_STOP_RELEASE_REASON:
                            vo.setCanViewRefuseStopReleaseReason(true);
                            break;
                        case VIEW_REFUSE_TEST_REASON:
                            vo.setCanViewRefuseTestReason(true);
                            break;
                        default:
                    }
                }
        );
    }

    @Override
    public PageResult<MarketingMsgReleasePageVo> releasePage(MarketingMsgReleasePageDto req) {
        PageResult<MarketingMsgReleasePageVo> res = new PageResult<>(req.getPageNum(), req.getPageNum());
        List<Long> titleLangIds = findTitleLangIds(req.getTitle());
        // 如果没有匹配的多语言id，则返回空
        if (StringUtils.isNotBlank(req.getTitle()) && CollectionUtils.isEmpty(titleLangIds)) {
            return res;
        }
        if (!CollectionUtils.isEmpty(req.getPushTypeCode())) {
            req.setPushTypeCodes(String.join("|", req.getPushTypeCode()));
        }
        IPage<MarketingMsg> page = this.getBaseMapper().selectReleasePage(new Page<>(req.getPageNum(), req.getPageSize()), req, titleLangIds);
        if (CollectionUtils.isEmpty(page.getRecords())) {
            return res;
        }
        res.setTotal(page.getTotal());
        List<MarketingMsgReleasePageVo> list = new ArrayList<>();
        page.getRecords().forEach(e -> {
            MarketingMsgReleasePageVo vo = new MarketingMsgReleasePageVo();
            BeanUtils.copyProperties(e, vo);
            vo.setMarketingMsgId(e.getId());
            vo.setTitle(com.chervon.operation.config.MultiLanguageUtil.getByLangCode(Optional.ofNullable(e.getTitleLangCode()).orElse(""), LocaleContextHolder.getLocale().getLanguage()));
            if (StringUtils.isNotBlank(e.getPushTypeCode())) {
                vo.setPushTypeCodes(new ArrayList<>(Arrays.asList(e.getPushTypeCode().split(","))));
            }
            // 设置操作
            handleReleasePageOperation(e, vo);
            list.add(vo);
        });
        res.setList(list);
        return res;
    }


    @Override
    public PageResult<MessageRecordResultVo> recordPage(MessageRecordSearchVo messageRecordVo) {
        PageResult<MessageRecordResultVo> res = new PageResult<>(messageRecordVo.getPageNum(), messageRecordVo.getPageNum());
        List<Long> titleLangIds = findTitleLangIds(messageRecordVo.getTitle());
        // 如果没有匹配的多语言id，则返回空
        if (StringUtils.isNotBlank(messageRecordVo.getTitle()) && CollectionUtils.isEmpty(titleLangIds)) {
            return null;
        }
        MarketingMsgReleasePageDto marketingMsgReleasePageDto = new MarketingMsgReleasePageDto();

        marketingMsgReleasePageDto.setMsgId(messageRecordVo.getMsgId());
        if (StringUtils.isNotEmpty(messageRecordVo.getPushTypeCode())) {
            marketingMsgReleasePageDto.setPushTypeCodes(messageRecordVo.getPushTypeCode());
        }
        IPage<MarketingMsg> page = this.getBaseMapper().selectReleasePage(new Page<>(messageRecordVo.getPageNum(), messageRecordVo.getPageSize()), marketingMsgReleasePageDto, titleLangIds);
        if (CollectionUtils.isEmpty(page.getRecords())) {
            return null;
        }
        res.setTotal(page.getTotal());
        List<MessageRecordResultVo> list = new ArrayList<>();
        page.getRecords().forEach(e -> {
            MessageRecordResultVo vo = new MessageRecordResultVo();
            BeanUtils.copyProperties(e, vo);
            vo.setMsgId(e.getId());
            vo.setMessageType(1);
            vo.setTitle(com.chervon.operation.config.MultiLanguageUtil.getByLangCode(Optional.ofNullable(e.getTitleLangCode()).orElse(""), LocaleContextHolder.getLocale().getLanguage()));
            if (StringUtils.isNotBlank(e.getPushTypeCode())) {
                vo.setPushTypeCodes(new ArrayList<>(Arrays.asList(e.getPushTypeCode().split(","))));
            }
            Integer pushSuccessNum = e.getPushSuccessNum() == null ? 0 : e.getPushSuccessNum();
            Integer pushFailNum = e.getPushFailNum() == null ? 0 : e.getPushFailNum();
            vo.setPushAllNum(pushSuccessNum + pushFailNum);
            list.add(vo);
        });
        res.setList(list);
        return res;
    }


    @Override
    public List<MessageRecordResultVo> recordList(MessageRecordSearchVo messageRecordVo) {
        List<Long> titleLangIds = findTitleLangIds(messageRecordVo.getTitle());
        // 如果没有匹配的多语言id，则返回空
        if (StringUtils.isNotBlank(messageRecordVo.getTitle()) && CollectionUtils.isEmpty(titleLangIds)) {
            return null;
        }
        MarketingMsgReleasePageDto marketingMsgReleasePageDto = new MarketingMsgReleasePageDto();

        marketingMsgReleasePageDto.setMsgId(messageRecordVo.getMsgId());
        if (StringUtils.isNotEmpty(messageRecordVo.getPushTypeCode())) {
            marketingMsgReleasePageDto.setPushTypeCodes(messageRecordVo.getPushTypeCode());
        }
        List<MarketingMsg> marketingMsgList = this.getBaseMapper().selectReleaseList(marketingMsgReleasePageDto, titleLangIds);
        if (CollectionUtils.isEmpty(marketingMsgList)) {
            return null;
        }
        List<MessageRecordResultVo> list = new ArrayList<>();
        marketingMsgList.forEach(e -> {
            MessageRecordResultVo vo = new MessageRecordResultVo();
            BeanUtils.copyProperties(e, vo);
            vo.setMsgId(e.getId());
            vo.setMessageType(1);
            vo.setTitle(com.chervon.operation.config.MultiLanguageUtil.getByLangCode(Optional.ofNullable(e.getTitleLangCode()).orElse(""), LocaleContextHolder.getLocale().getLanguage()));
            if (StringUtils.isNotBlank(e.getPushTypeCode())) {
                vo.setPushTypeCodes(new ArrayList<>(Arrays.asList(e.getPushTypeCode().split(","))));
            }
            Integer pushSuccessNum = e.getPushSuccessNum() == null ? 0 : e.getPushSuccessNum();
            Integer pushFailNum = e.getPushFailNum() == null ? 0 : e.getPushFailNum();
            vo.setPushAllNum(pushSuccessNum + pushFailNum);
            list.add(vo);
        });
        return list;
    }

    private void handleReleasePageOperation(MarketingMsg marketingMsg, MarketingMsgReleasePageVo vo) {
        MarketingMsgStateEnum statusE = MarketingMsgStateEnum.getFromCode(marketingMsg.getStatusCode());
        Arrays.stream(statusE.getOperations()).forEach(e -> {
                    switch (e) {
                        case VIEW:
                            vo.setCanView(true);
                            break;
                        case ENSURE_RELEASE:
                            vo.setCanEnsureRelease(true);
                            break;
                        case REFUSE_RELEASE:
                            vo.setCanRefuseRelease(true);
                            break;
                        case ENSURE_STOP_RELEASE:
                            vo.setCanEnsureStopRelease(true);
                            break;
                        case REFUSE_STOP_RELEASE:
                            vo.setCanRefuseStopRelease(true);
                            break;
                        case ENSURE_TEST:
                            vo.setCanEnsureTest(true);
                            break;
                        case REFUSE_TEST:
                            vo.setCanRefuseTest(true);
                            break;
                        case VIEW_REFUSE_RELEASE_REASON:
                            vo.setCanViewRefuseReleaseReason(true);
                            break;
                        case VIEW_REFUSE_STOP_RELEASE_REASON:
                            vo.setCanViewRefuseStopReleaseReason(true);
                            break;
                        case VIEW_REFUSE_TEST_REASON:
                            vo.setCanViewRefuseTestReason(true);
                            break;
                        default:
                    }
                }
        );
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void save(MarketingMsgDto req) {
        check(req);
        long count = this.count(new LambdaQueryWrapper<MarketingMsg>().eq(MarketingMsg::getTitle, req.getTitle()));
        if (count > 0) {
            throw ExceptionMessageUtil.getException(OPERATION_APP_MARKETING_MSG_TITLE_EXIST, req.getTitle());
        }
        MarketingMsg marketingMsg = new MarketingMsg();
        MultiLanguageBo title = remoteMultiLanguageService.simpleCreateMultiLanguage(ApplicationEnum.OPERATION_PLATFORM.getName(), req.getTitle(), LocaleContextHolder.getLocale().getLanguage());
        marketingMsg.setTitle(req.getTitle());
        marketingMsg.setTitleLangId(title.getLangId());
        marketingMsg.setTitleLangCode(title.getLangCode());
        if (StringUtils.isNotBlank(req.getContent())) {
            MultiLanguageBo content = remoteMultiLanguageService.simpleCreateMultiLanguage(ApplicationEnum.OPERATION_PLATFORM.getName(), req.getContent(), LocaleContextHolder.getLocale().getLanguage());
            marketingMsg.setContent(req.getContent());
            marketingMsg.setContentLangId(content.getLangId());
            marketingMsg.setContentLangCode(content.getLangCode());
        }
        marketingMsg.setStatusCode(WILL_RELEASE.getCode());
        store(marketingMsg, req);
    }

    private void check(MarketingMsgDto req) {
        if (StringUtils.isBlank(req.getTitle())) {
            throw ExceptionMessageUtil.getException(OPERATION_APP_MARKETING_MSG_TITLE_NULL);
        }
        if (CollectionUtils.isEmpty(req.getTestGroup())) {
            throw ExceptionMessageUtil.getException(OPERATION_APP_MARKETING_MSG_TEST_GROUP_EMPTY);
        }
        req.getTestGroup().forEach(e -> {
            long groupCount = groupService.count(new LambdaQueryWrapper<Groups>().eq(Groups::getGroupName, e));
            if (groupCount <= 0) {
                throw ExceptionMessageUtil.getException(OPERATION_APP_MARKETING_MSG_TEST_GROUP_NOT_EXIST, e);
            }
        });
        if (!CollectionUtils.isEmpty(req.getPrdGroup())) {
            req.getPrdGroup().forEach(e -> {
                long groupCount = groupService.count(new LambdaQueryWrapper<Groups>().eq(Groups::getGroupName, e));
                if (groupCount <= 0) {
                    throw ExceptionMessageUtil.getException(OPERATION_APP_MARKETING_MSG_PRD_GROUP_NOT_EXIST, e);
                }
            });
        }
        if (CollectionUtils.isEmpty(req.getPushTypeCodes())) {
            throw ExceptionMessageUtil.getException(OPERATION_APP_MARKETING_MSG_PUSH_TYPE_EMPTY);
        }
        if (req.getStartType() == null || !Arrays.asList(1, 2).contains(req.getStartType())) {
            throw ExceptionMessageUtil.getException(OPERATION_APP_MARKETING_MSG_START_ERROR);
        } else if (req.getStartType() == 2 && (StringUtils.isBlank(req.getStartZone()) || StringUtils.isBlank(req.getStartTime()))) {
            throw ExceptionMessageUtil.getException(OPERATION_APP_MARKETING_MSG_START_ERROR);
        }
        if (req.getEndType() == null || !Arrays.asList(1, 2).contains(req.getEndType())) {
            throw ExceptionMessageUtil.getException(OPERATION_APP_MARKETING_MSG_END_ERROR);
        } else if (req.getEndType() == 2 && (StringUtils.isBlank(req.getEndZone()) || StringUtils.isBlank(req.getEndTime()))) {
            throw ExceptionMessageUtil.getException(OPERATION_APP_MARKETING_MSG_END_ERROR);
        }
        if (StringUtils.isBlank(req.getPushRateCode())) {
            throw ExceptionMessageUtil.getException(OPERATION_APP_MARKETING_MSG_PUSH_RATE_NULL);
        }
        checkConfig(req.getStartType(), req.getStartZone(), req.getStartTime(), req.getEndType(), req.getEndZone(), req.getEndTime());
        if (StringUtils.isBlank(req.getContent())) {
            req.setContent("");
        }
    }

    private void checkConfig(Integer startType, String startZone, String startTime, Integer endType, String endZone, String endTime) {
        if (endType == 1) {
            // 结束时间是永久
            return;
        }
        LocalDateTime endL = LocalDateTime.parse(endTime, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        ZonedDateTime endZ = ZonedDateTime.of(endL, ZoneId.of(endZone));
        ZonedDateTime startZ;
        if (startType == 1) {
            startZ = ZonedDateTime.now();
        } else {
            LocalDateTime startL = LocalDateTime.parse(startTime, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
            startZ = ZonedDateTime.of(startL, ZoneId.of(startZone));
        }
        if (!endZ.isAfter(startZ)) {
            throw ExceptionMessageUtil.getException(OPERATION_APP_MARKETING_MSG_END_BEFORE_START);
        }
    }

    private void store(MarketingMsg marketingMsg, MarketingMsgDto req) {
        marketingMsg.setRutePath(req.getRutePath());
        if (!CollectionUtils.isEmpty(req.getPrdGroup())) {
            marketingMsg.setPrdGroup(req.getPrdGroup().stream().map(String::valueOf).collect(Collectors.joining(",")));
        } else {
            marketingMsg.setPrdGroup("");
        }
        marketingMsg.setTestGroup(req.getTestGroup().stream().map(String::valueOf).collect(Collectors.joining(",")));
        marketingMsg.setPushTypeCode(req.getPushTypeCodes().stream().map(String::valueOf).collect(Collectors.joining(",")));
        marketingMsg.setPushRateCode(req.getPushRateCode());
        // 入库消息的开始和结束类型都是固定时间，开始为立即则时间为utc创建时间，结束为永久则时间为utc2099-12-31 23:59:59
        if (req.getStartType() == 1) {
            marketingMsg.setStartZone("+0");
            marketingMsg.setStartTime(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        } else {
            marketingMsg.setStartZone(req.getStartZone());
            marketingMsg.setStartTime(req.getStartTime());
        }
        marketingMsg.setRealStartTime(DateTimeZoneUtil.getRealTime(marketingMsg.getStartTime(), marketingMsg.getStartZone()));
        if (req.getEndType() == 1) {
            marketingMsg.setEndZone("+0");
            marketingMsg.setEndTime("2099-12-31 23:59:59");
        } else {
            marketingMsg.setEndZone(req.getEndZone());
            marketingMsg.setEndTime(req.getEndTime());
        }
        marketingMsg.setRealEndTime(DateTimeZoneUtil.getRealTime(marketingMsg.getEndTime(), marketingMsg.getEndZone()));
        marketingMsg.setFromId(req.getFromId());
        this.saveOrUpdate(marketingMsg);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void update(MarketingMsgDto req) {
        if (req.getMarketingMsgId() == null) {
            throw ExceptionMessageUtil.getException(OPERATION_APP_MARKETING_MSG_ID_NULL);
        }
        check(req);
        MarketingMsg marketingMsg = new MarketingMsg();
        MarketingMsg one = this.getById(req.getMarketingMsgId());
        if (one == null) {
            throw ExceptionMessageUtil.getException(OPERATION_APP_MARKETING_MSG_NULL, req.getMarketingMsgId());
        }
        long count = this.count(new LambdaQueryWrapper<MarketingMsg>().eq(MarketingMsg::getTitle, req.getTitle()).ne(MarketingMsg::getId, req.getMarketingMsgId()));
        if (count > 0) {
            throw ExceptionMessageUtil.getException(OPERATION_APP_MARKETING_MSG_TITLE_EXIST, req.getTitle());
        }
        checkOperation(one.getStatusCode(), MarketingMsgOperationEnum.UPDATE.getName());
        marketingMsg.setId(one.getId());
        remoteMultiLanguageService.simpleUpdateMultiLanguage(ApplicationEnum.OPERATION_PLATFORM.getName(), one.getTitleLangId(), req.getTitle(), LocaleContextHolder.getLocale().getLanguage());
        marketingMsg.setTitle(req.getTitle());
        if (one.getContentLangId() != null) {
            remoteMultiLanguageService.simpleUpdateMultiLanguage(ApplicationEnum.OPERATION_PLATFORM.getName(), one.getContentLangId(), req.getContent(), LocaleContextHolder.getLocale().getLanguage());
        } else {
            MultiLanguageBo content = remoteMultiLanguageService.simpleCreateMultiLanguage(ApplicationEnum.OPERATION_PLATFORM.getName(), req.getContent(), LocaleContextHolder.getLocale().getLanguage());
            marketingMsg.setContentLangId(content.getLangId());
            marketingMsg.setContentLangCode(content.getLangCode());
        }
        marketingMsg.setContent(req.getContent());
        if (MarketingMsgStateEnum.getFromCode(one.getStatusCode()) == STOP_RELEASED) {
            marketingMsg.setStatusCode(WILL_RELEASE.getCode());
            marketingMsg.setStopRelease(0);
            marketingMsg.setReleaseRefused(0);
            marketingMsg.setTestRefused(0);
        }
        store(marketingMsg, req);
    }

    @Override
    public void updateMsgCount(List<MessagePushResultCountDto> countDtos) {
        countDtos.forEach(countDto -> {
            if (countDto.getSystemMessageId().matches("\\d+")) {
                this.getBaseMapper().updateMsgCount(countDto);
            }
        });
    }


    @Override
    public MarketingMsgVo detail(Long id) {
        if (id == null) {
            return new MarketingMsgVo();
        }
        MarketingMsg one = this.getById(id);
        if (one == null) {
            return new MarketingMsgVo();
        }
        MarketingMsgVo res = new MarketingMsgVo();
        BeanUtils.copyProperties(one, res);
        res.setMarketingMsgId(one.getId());
        res.setTitle(com.chervon.operation.config.MultiLanguageUtil.getByLangCode(Optional.ofNullable(one.getTitleLangCode()).orElse(""), LocaleContextHolder.getLocale().getLanguage()));
        res.setContent(com.chervon.operation.config.MultiLanguageUtil.getByLangCode(Optional.ofNullable(one.getContentLangCode()).orElse(""), LocaleContextHolder.getLocale().getLanguage()));
        if (StringUtils.isNotBlank(one.getPrdGroup())) {
            res.setPrdGroup(new ArrayList<>(Arrays.asList(one.getPrdGroup().split(","))));
        }
        if (StringUtils.isNotBlank(one.getTestGroup())) {
            res.setTestGroup(new ArrayList<>(Arrays.asList(one.getTestGroup().split(","))));
        }
        if (StringUtils.isNotBlank(one.getPushTypeCode())) {
            res.setPushTypeCodes(new ArrayList<>(Arrays.asList(one.getPushTypeCode().split(","))));
        }
        return res;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void copy(MarketingMsgDto req) {
        if (req.getFromId() == null) {
            throw ExceptionMessageUtil.getException(OPERATION_APP_MARKETING_MSG_FROM_ID_NULL);
        }
        save(req);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void delete(Long id) {
        if (id == null) {
            throw ExceptionMessageUtil.getException(OperationErrorCode.OPERATION_APP_MARKETING_MSG_ID_NULL);
        }
        MarketingMsg marketingMsg = this.getById(id);
        if (marketingMsg == null) {
            throw ExceptionMessageUtil.getException(OPERATION_APP_MARKETING_MSG_NULL, id);
        }
        cleanLanguages(marketingMsg);
        checkOperation(marketingMsg.getStatusCode(), DELETE.getName());
        this.removeById(marketingMsg.getId());
        // 根据ID模糊查询XXL-JOB任务，删除该{任务ID}以及{任务ID+end}的任务
        List<Integer> jobIdList = xxlJobService.listJobInfoIdByDesc(id.toString());
        if (!CollectionUtils.isEmpty(jobIdList)) {
            xxlJobService.removeJobInfoByIdList(jobIdList);
        }
    }

    /**
     * 清理多语言
     * @param marketingMsg
     */
    private void cleanLanguages(MarketingMsg marketingMsg) {
        List<Long> langs = new ArrayList<>();
        if (marketingMsg.getTitleLangId() != null) {
            langs.add(marketingMsg.getTitleLangId());
        }
        if (marketingMsg.getContentLangId() != null) {
            langs.add(marketingMsg.getContentLangId());
        }
        if (langs.size() > 0) {
            remoteMultiLanguageService.deleteByLangIds(langs);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void operation(MarketingMsgOperationDto req) {
        if (req.getMarketingMsgId() == null) {
            throw ExceptionMessageUtil.getException(OPERATION_APP_MARKETING_MSG_ID_NULL);
        }
        MarketingMsg marketingMsg = this.getById(req.getMarketingMsgId());
        if (marketingMsg == null) {
            throw ExceptionMessageUtil.getException(OPERATION_APP_MARKETING_MSG_NULL, req.getMarketingMsgId());
        }
        if (marketingMsg.getTestRefused() == null) {
            marketingMsg.setTestRefused(0);
        }
        if (marketingMsg.getReleaseRefused() == null) {
            marketingMsg.setReleaseRefused(0);
        }
        if (marketingMsg.getStopRelease() == null) {
            marketingMsg.setStopRelease(0);
        }

        checkOperation(marketingMsg.getStatusCode(), req.getOperation());

        MarketingMsgOperationEnum operation = MarketingMsgOperationEnum.getFromName(req.getOperation());
        // 定义修改对象
        MarketingMsg one = new MarketingMsg();
        one.setId(req.getMarketingMsgId());
        // 定义记录
        AppRecord record = new AppRecord();
        record.setType(3);
        record.setInstanceId(marketingMsg.getId());
        record.setSourceStatus(marketingMsg.getStatusCode());
        switch (operation) {
            case APPLY_RELEASE:
                // 校验发布配置
                checkConfig(2, marketingMsg.getStartZone(), marketingMsg.getStartTime(), 2, marketingMsg.getEndZone(), marketingMsg.getEndTime());
                one.setStatusCode(TEST_CHECK_ING.getCode());
                // 记录申请
                one.setApplyBy(LoginUserUtil.getSign());
                one.setApplyTime(LocalDateTime.now());
                record.setOperation(APPLY_RELEASE.getName());
                record.setTargetStatus(TEST_CHECK_ING.getCode());
                break;
            case CANCEL_APPLY_RELEASE:
                if (marketingMsg.getStopRelease() > 0) {
                    one.setStatusCode(STOP_RELEASED.getCode());
                    record.setTargetStatus(STOP_RELEASED.getCode());
                } else if (marketingMsg.getReleaseRefused() > 0) {
                    one.setStatusCode(RELEASE_REFUSED.getCode());
                    record.setTargetStatus(RELEASE_REFUSED.getCode());
                } else if (marketingMsg.getTestRefused() > 0) {
                    one.setStatusCode(TEST_REFUSED.getCode());
                    record.setTargetStatus(TEST_REFUSED.getCode());
                } else {
                    one.setStatusCode(WILL_RELEASE.getCode());
                    record.setTargetStatus(WILL_RELEASE.getCode());
                }
                // 记录申请
                one.setApplyBy(LoginUserUtil.getSign());
                one.setApplyTime(LocalDateTime.now());
                record.setOperation(CANCEL_APPLY_RELEASE.getName());
                break;
            case ENSURE_RELEASE:
                one.setStatusCode(RELEASED.getCode());
                // 记录审核
                one.setApprovedBy(LoginUserUtil.getSign());
                one.setApprovedTime(LocalDateTime.now());
                record.setOperation(ENSURE_RELEASE.getName());
                record.setTargetStatus(RELEASED.getCode());
                checkAfterRelease(marketingMsg);
                break;
            case REFUSE_RELEASE:
                one.setStatusCode(RELEASE_REFUSED.getCode());
                one.setReleaseRefused(1);

                one.setTestRefused(0);
                one.setStopRelease(0);
                // 记录审核
                one.setApprovedBy(LoginUserUtil.getSign());
                one.setApprovedTime(LocalDateTime.now());
                record.setOperation(REFUSE_RELEASE.getName());
                record.setDescription(req.getReason());
                record.setTargetStatus(RELEASE_REFUSED.getCode());
                break;
            case ENSURE_TEST:
                one.setStatusCode(RELEASE_VERIFY_ING.getCode());
                // 记录审核
                one.setApprovedBy(LoginUserUtil.getSign());
                one.setApprovedTime(LocalDateTime.now());
                record.setOperation(ENSURE_TEST.getName());
                record.setTargetStatus(RELEASE_VERIFY_ING.getCode());
                break;
            case REFUSE_TEST:
                one.setStatusCode(TEST_REFUSED.getCode());
                one.setTestRefused(1);

                one.setReleaseRefused(0);
                one.setStopRelease(0);
                // 记录审核
                one.setApprovedBy(LoginUserUtil.getSign());
                one.setApprovedTime(LocalDateTime.now());
                record.setOperation(REFUSE_TEST.getName());
                record.setDescription(req.getReason());
                record.setTargetStatus(TEST_REFUSED.getCode());
                break;
            case APPLY_STOP_RELEASE:
                // 校验发布配置
                one.setStatusCode(STOP_RELEASE_VERIFY_ING.getCode());
                // 记录申请
                one.setApplyBy(LoginUserUtil.getSign());
                one.setApplyTime(LocalDateTime.now());
                record.setOperation(APPLY_STOP_RELEASE.getName());
                record.setTargetStatus(STOP_RELEASE_VERIFY_ING.getCode());
                break;
            case CANCEL_APPLY_STOP_RELEASE:
                one.setStatusCode(RELEASED.getCode());
                // 记录申请
                one.setApplyBy(LoginUserUtil.getSign());
                one.setApplyTime(LocalDateTime.now());
                record.setTargetStatus(RELEASED.getCode());
                record.setOperation(CANCEL_APPLY_STOP_RELEASE.getName());
                break;
            case ENSURE_STOP_RELEASE:
                one.setStatusCode(STOP_RELEASED.getCode());
                one.setStopRelease(1);

                one.setReleaseRefused(0);
                one.setTestRefused(0);
                // 记录审核
                one.setApprovedBy(LoginUserUtil.getSign());
                one.setApprovedTime(LocalDateTime.now());
                record.setOperation(ENSURE_STOP_RELEASE.getName());
                record.setTargetStatus(STOP_RELEASED.getCode());
                break;
            case REFUSE_STOP_RELEASE:
                one.setStatusCode(STOP_RELEASE_REFUSED.getCode());
                // 记录审核
                one.setApprovedBy(LoginUserUtil.getSign());
                one.setApprovedTime(LocalDateTime.now());
                record.setOperation(REFUSE_STOP_RELEASE.getName());
                record.setDescription(req.getReason());
                record.setTargetStatus(STOP_RELEASE_REFUSED.getCode());
                break;
            default:
        }
        this.updateById(one);
        appRecordService.save(record);
    }

    private void checkOperation(String status, String operation) {
        MarketingMsgStateEnum statusE = MarketingMsgStateEnum.getFromCode(status);
        MarketingMsgOperationEnum operationE = MarketingMsgOperationEnum.getFromName(operation);
        if (!Arrays.stream(statusE.getOperations()).collect(Collectors.toList()).contains(operationE)) {
            throw ExceptionMessageUtil.getException(OPERATION_APP_MARKETING_MSG_OPERATE_REJECTED, operation);
        }
    }

    @Override
    public String view(MarketingMsgOperationDto req) {
        if (req.getMarketingMsgId() == null) {
            throw ExceptionMessageUtil.getException(OPERATION_APP_MARKETING_MSG_ID_NULL);
        }
        MarketingMsg marketingMsg = this.getById(req.getMarketingMsgId());
        if (marketingMsg == null) {
            throw ExceptionMessageUtil.getException(OPERATION_APP_MARKETING_MSG_NULL, req.getMarketingMsgId());
        }

        checkOperation(marketingMsg.getStatusCode(), req.getOperation());

        MarketingMsgOperationEnum operation = MarketingMsgOperationEnum.getFromName(req.getOperation());
        LambdaQueryWrapper<AppRecord> queryWrapper = new LambdaQueryWrapper<AppRecord>()
                .eq(AppRecord::getInstanceId, marketingMsg.getId())
                .eq(AppRecord::getType, 3)
                .orderByDesc(AppRecord::getCreateTime)
                .last("limit 1");
        if (operation == VIEW_REFUSE_RELEASE_REASON) {
            queryWrapper.eq(AppRecord::getOperation, REFUSE_RELEASE.getName());
        } else if (operation == VIEW_REFUSE_TEST_REASON) {
            queryWrapper.eq(AppRecord::getOperation, REFUSE_TEST.getName());
        } else if (operation == VIEW_REFUSE_STOP_RELEASE_REASON) {
            queryWrapper.eq(AppRecord::getOperation, REFUSE_STOP_RELEASE.getName());
        } else {
            throw ExceptionMessageUtil.getException(OPERATION_APP_MARKETING_MSG_OPERATE_REJECTED, req.getOperation());
        }
        AppRecord record = appRecordService.getOne(queryWrapper);
        if (record == null) {
            return "";
        }
        return record.getDescription();
    }

    /**
     * 发布营销消息之后判度是否推送或者新建XXL定时任务
     *
     * @param marketingMsg 营销消息DataObject
     */
    private void checkAfterRelease(MarketingMsg marketingMsg) {
        // 如果当前时间已经操过发布过期时间，则退出
        if (LocalDateTime.now().isAfter(marketingMsg.getRealEndTime())) {
            return;
        }
        // 推送频率是仅一次推送
        if (Objects.equals(marketingMsg.getPushRateCode(), CommonConstant.ZERO.toString())) {
            // 设置的推送开始时间已经过了，则立即推送
            if (LocalDateTime.now().isAfter(marketingMsg.getRealStartTime())) {
                pushMessage(marketingMsg);
            } else {
                // 推送开始时间未到，则创建定时任务
                // 开始时间为定时,则在发布后 1.删除原有任务 2.创建CRON为仅一次的定时任务
                // 根据ID模糊查询XXL-JOB任务，删除该{任务ID}以及{任务ID+end}的任务
                List<Integer> idList = xxlJobService.listJobInfoIdByDesc(marketingMsg.getId().toString());
                if (!CollectionUtils.isEmpty(idList)) {
                    xxlJobService.removeJobInfoByIdList(idList);
                }
                LocalDateTime executorDate = marketingMsg.getRealStartTime();
                xxlJobService.addExecutorTask(marketingMsg.getId().toString(), executorDate, "marketingMsgPushExecutor", false, marketingMsg.getId());
            }
        } else if (Objects.equals(marketingMsg.getPushRateCode(), CommonConstant.TWO.toString())) {
            // 推送频率为每天推送,则在发布后 1.删除原有任务 2.创建定时任务
            // 根据ID模糊查询XXL-JOB任务，删除该{任务ID}以及{任务ID+end}的任务
            List<Integer> idList = xxlJobService.listJobInfoIdByDesc(marketingMsg.getId().toString());
            if (!CollectionUtils.isEmpty(idList)) {
                xxlJobService.removeJobInfoByIdList(idList);
            }
            if (LocalDateTime.now().isAfter(marketingMsg.getRealStartTime())) {
                try {
                    pushMessage(marketingMsg);
                } catch (Exception e) {
                    log.error("MarketingMsgServiceImpl#checkAfterRelease -> 推送类型为每天一次,起始时间为立即的消息{},推送时发生错误{}", marketingMsg.getId(), e.getMessage());
                }
            }
            LocalDateTime executorDate = marketingMsg.getRealStartTime();
            xxlJobService.addExecutorTask(marketingMsg.getId().toString(), executorDate, "marketingMsgPushExecutor", true, marketingMsg.getId());
        }
        // 如果结束类型是定时结束，则创建定时更新状态任务
        if (!"+0".equals(marketingMsg.getEndZone()) || !"2099-12-31 23:59:59".equals(marketingMsg.getEndTime())) {
            LocalDateTime executorDate = marketingMsg.getRealEndTime();
            xxlJobService.addExecutorTask("end" + marketingMsg.getId(), executorDate, "updateMarketingMsgStatusToFinished", false, marketingMsg.getId());
        }
    }

    @Override
    public void pushMessage(MarketingMsg marketingMsg) {
        // 生产分组用户
        Set<Long> produceUserIds;
        if (StringUtils.isNotBlank(marketingMsg.getPrdGroup())) {
            produceUserIds = new HashSet<>(remoteGroupServiceImpl.listUserId(marketingMsg.getPrdGroup()));
        } else {
            // 如果生产分组为空则推送给所有用户
            produceUserIds = new HashSet<>(remoteAppUserService.listAllUserIds());
        }
        // 测试分组用户
        Set<Long> userIds = new HashSet<>(remoteGroupServiceImpl.listUserId(marketingMsg.getTestGroup()));
        // 两个用户ID列表取并集
        userIds.addAll(produceUserIds);
        List<MessageDto> messages = new ArrayList<>();
        List<Integer> pushTypes = new ArrayList<>();
        List<String> pushTypeStrList = Arrays.asList(marketingMsg.getPushTypeCode().split(","));
        pushTypeStrList.forEach(pushTypeStr -> pushTypes.add(PushMethodEnum.valueOf(pushTypeStr).getPushTypes()));
        Map<String, AppUserVo> appUserVoMap = remoteAppUserService.listAppUserMap(new ArrayList<>(userIds));
        Map<Long, UserSettingBo> userSettingBoMap = remoteUserSettingService.listUserSettingBoMap(new ArrayList<>(userIds));
        // 判断开始时间是否为立即
        for (Long userId : userIds) {
            MessageDto messageDto = new MessageDto();
            messageDto.setUserId(userId.toString());
            messageDto.setSystemMessageId(marketingMsg.getId().toString());
            messageDto.setPushTypes(pushTypes);
            messageDto.setMessageType(CommonConstant.ONE);
            // 将路由地址发送在payLoadData中
            if (StringUtils.isNotBlank(marketingMsg.getRutePath())) {
                HashMap<String, String> hashMap = new HashMap<>(CommonConstant.ONE);
                hashMap.put("rutePath", marketingMsg.getRutePath());
                messageDto.setPayloadData(hashMap);
            }
            // 推送消息设备类型(user-center user表中获取)
            AppUserVo appUserVo = appUserVoMap.get(userId.toString());
            if (null == appUserVo) {
                log.warn("appUserVo({})不存在", userId);
                continue;
            } else if (StringUtils.isEmpty(appUserVo.getAppTypeCode())) {
                log.warn("appUserVo({})APP类型为空", userId);
                continue;
            } else {
                messageDto.setDeviceType(OsType.valueOf(appUserVo.getAppTypeCode().toUpperCase()));
                messageDto.setPhone(null == appUserVo.getPhone() ? null : appUserVo.getPhone());
                messageDto.setEmail(appUserVo.getEmail());
            }
            // 推送消息用户Token(iot-app user_setting表中的push_token字段)
            UserSettingBo userSettingBo = userSettingBoMap.get(userId);
            if (userSettingBo == null) {
                log.warn("userSettingBo({})不存在", userId);
                continue;
            } else if (pushTypes.contains(CommonConstant.ZERO) && StringUtils.isEmpty(userSettingBo.getPushToken())) {
                log.warn("推送方式包含墓碑,但是user({})的push_token为空", userId);
                continue;
            } else {
                messageDto.setToken(userSettingBo.getPushToken());
            }
            // 推送消息开关(iot-app user_setting表中的marketing_message_switch字段)
            if (userSettingBo.getMarketingMessageSwitch() == null || !userSettingBo.getMarketingMessageSwitch().equals(CommonConstant.ONE)) {
                log.warn("用户({})营销消息开关是关闭状态", userId);
                continue;
            } else {
                messageDto.setPushSwitch(userSettingBo.getMarketingMessageSwitch());
            }
            // 消息标题,内容
            if (StringUtils.isEmpty(userSettingBo.getLanguage())) {
                log.warn("用户({})默认语言为空", userSettingBo.getUserId());
                continue;
            }
            String title = com.chervon.operation.config.MultiLanguageUtil.getByLangCode(marketingMsg.getTitleLangCode(), userSettingBo.getLanguage());
            if (StringUtils.isEmpty(title)) {
                log.warn("用户({})默认语言({})标题为空", userSettingBo.getUserId(), userSettingBo.getLanguage());
                continue;
            }
            messageDto.setTitle(title);
            if (StringUtils.isNotEmpty(marketingMsg.getContentLangCode())) {
                messageDto.setContent(com.chervon.operation.config.MultiLanguageUtil.getByLangCode(marketingMsg.getContentLangCode(),
                        userSettingBo.getLanguage()));
            }
            messages.add(messageDto);
        }
        log.info("MarketingMsgServiceImpl#pushMessage -> rpc调用RemoteMessageService发送消息:{}", messages);
        remoteMessageService.pushMessage(messages);
    }

    /**
     * 推送列表中消息给一个用户
     * APP启动后推送一次逻辑
     * 与SysMsgServiceImpl#pushMessageToOneUser逻辑保持一致
     *
     * @param appUserVo     用户RPC类 userId,appTypeCode
     * @param userSettingBo 用户设置RPC类 userId,pushToken,language,marketingMessageSwitch
     * @param marketingMsgs 营销消息列表
     */
    private void pushMessageToOneUser(AppUserVo appUserVo, UserSettingBo userSettingBo, List<MarketingMsg> marketingMsgs) {
        List<MessageDto> messages = new ArrayList<>();
        for (MarketingMsg marketingMsg : marketingMsgs) {
            List<Integer> pushTypes = new ArrayList<>();
            List<String> pushTypeStrList = Arrays.asList(marketingMsg.getPushTypeCode().split(","));
            pushTypeStrList.forEach(pushTypeStr -> pushTypes.add(PushMethodEnum.valueOf(pushTypeStr).getPushTypes()));
            // 判断开始时间是否为立即
            MessageDto messageDto = new MessageDto();
            messageDto.setUserId(appUserVo.getUserId());
            // PHONE
            messageDto.setEmail(appUserVo.getEmail());
            messageDto.setSystemMessageId(marketingMsg.getId().toString());
            messageDto.setPushTypes(pushTypes);
            // 营销消息=1
            messageDto.setMessageType(CommonConstant.ONE);
            // 将路由地址发送在payLoadData中
            if (StringUtils.isNotBlank(marketingMsg.getRutePath())) {
                HashMap<String, String> hashMap = new HashMap<>(CommonConstant.ONE);
                hashMap.put("rutePath", marketingMsg.getRutePath());
                messageDto.setPayloadData(hashMap);
            }
            // 从appUserVo获取操作系统类型
            messageDto.setDeviceType(OsType.valueOf(appUserVo.getAppTypeCode().toUpperCase()));
            // 判断用户设置(iot-app.user_setting表的push_token, market_message_switch, language字段)
            if (pushTypes.contains(CommonConstant.ZERO) && StringUtils.isEmpty(userSettingBo.getPushToken())) {
                log.warn("MarketingMsgServiceImpl#pushMessageToOneUser -> MarketingMsg({})推送方式包含墓碑,但是user({})的push_token为空", marketingMsg.getId(), appUserVo.getUserId());
                continue;
            } else {
                messageDto.setToken(userSettingBo.getPushToken());
            }
            messageDto.setPushSwitch(userSettingBo.getMarketingMessageSwitch());
            // 消息标题,内容
            String title = com.chervon.operation.config.MultiLanguageUtil.getByLangCode(marketingMsg.getTitleLangCode(), userSettingBo.getLanguage());
            if (StringUtils.isEmpty(title)) {
                log.warn("MarketingMsgServiceImpl#pushMessageToOneUser -> MarketingMsg({}),用户({})默认语言({})标题为空", marketingMsg.getId(), userSettingBo.getUserId(), userSettingBo.getLanguage());
                continue;
            }
            messageDto.setTitle(title);
            if (StringUtils.isNotEmpty(marketingMsg.getContentLangCode())) {
                messageDto.setContent(com.chervon.operation.config.MultiLanguageUtil.getByLangCode(marketingMsg.getContentLangCode(), userSettingBo.getLanguage()));
            }
            messages.add(messageDto);
        }
        if (!CollectionUtils.isEmpty(messages)) {
            log.info("MarketingMsgServiceImpl#pushMessageToOneUser -> rpc调用RemoteMessageService发送消息:{}", messages);
            remoteMessageService.pushMessage(messages);
        }
    }

    @Override
    public void checkAfterAppStarted(AppUserVo appUserVo, UserSettingBo userSettingBo, List<String> groupNames) {
        LocalDateTime localDateTime = LocalDateTime.now();
        LambdaQueryWrapper<MarketingMsg> wrapper = new LambdaQueryWrapper<MarketingMsg>()
                // 推送频率为APP启动后一次
                .eq(MarketingMsg::getPushRateCode, CommonConstant.ONE)
                // 发布状态为已发布
                .in(MarketingMsg::getStatusCode, Arrays.asList(RELEASED, STOP_RELEASE_VERIFY_ING, STOP_RELEASE_REFUSED))
                .le(MarketingMsg::getRealStartTime, localDateTime)
                .ge(MarketingMsg::getRealEndTime, localDateTime);
        List<MarketingMsg> marketingMsgs = this.list(wrapper);
        log.info("MarketingMsgServiceImpl#checkAfterAppStarted -> APP启动后一次满足时间删选条件的Id列表为: {}", marketingMsgs.stream().map(MarketingMsg::getId).collect(Collectors.toList()));
        if (CollectionUtils.isEmpty(marketingMsgs)) {
            // 如果没有需要推送的消息
            return;
        }
        List<MarketingMsg> marketingMsgToBePushedList = new ArrayList<>();
        for (MarketingMsg marketingMsg : marketingMsgs) {
            boolean isPush = false;
            if (StringUtils.isEmpty(marketingMsg.getPrdGroup())) {
                isPush = true;
            } else {
                if (CollectionUtils.isEmpty(groupNames)) {
                    // 避免空指针
                    continue;
                }
                // 根据英文逗号,切割字符串,遍历查看是否有匹配
                String[] prdGroupNameFromDbList = marketingMsg.getPrdGroup().split(",");
                first:
                for (String groupNameFromDb : prdGroupNameFromDbList) {
                    for (String groupName : groupNames) {
                        if (Objects.equals(groupName, groupNameFromDb)) {
                            isPush = true;
                            break first;
                        }
                    }
                }
            }
            if (!isPush && StringUtils.isNotEmpty(marketingMsg.getTestGroup()) && !CollectionUtils.isEmpty(groupNames)) {
                // 根据英文逗号,切割字符串,遍历查看是否有匹配
                String[] testGroupNameFromDbList = marketingMsg.getTestGroup().split(",");
                second:
                for (String groupNameFromDb : testGroupNameFromDbList) {
                    for (String groupName : groupNames) {
                        if (Objects.equals(groupName, groupNameFromDb)) {
                            isPush = true;
                            break second;
                        }
                    }
                }
            }
            if (isPush) {
                marketingMsgToBePushedList.add(marketingMsg);
            }
        }
        if (!CollectionUtils.isEmpty(marketingMsgToBePushedList)) {
            log.info("MarketingMsgServiceImpl#checkAfterAppStarted -> APP用户{}登录之后需要的营销推送消息Id列表:{}", appUserVo.getUserId(), marketingMsgToBePushedList.stream().map(MarketingMsg::getId).collect(Collectors.toList()));
            try{
                Thread.sleep(2000L);
            } catch (InterruptedException e) {
                log.error("sleep interrupted exception");
            }
            pushMessageToOneUser(appUserVo, userSettingBo, marketingMsgToBePushedList);
        }
    }
}
