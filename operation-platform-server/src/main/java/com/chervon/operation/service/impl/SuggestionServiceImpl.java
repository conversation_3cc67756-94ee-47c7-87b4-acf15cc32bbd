package com.chervon.operation.service.impl;

import com.alibaba.excel.EasyExcel;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.chervon.common.core.domain.PageResult;
import com.chervon.common.core.enums.ApplicationEnum;
import com.chervon.common.core.utils.BeanCopyUtils;
import com.chervon.common.core.utils.DateTimeZoneUtil;
import com.chervon.common.mybatis.config.BaseDo;
import com.chervon.configuration.api.core.MultiLanguageBo;
import com.chervon.configuration.api.service.RemoteMultiLanguageService;
import com.chervon.operation.api.exception.OperationErrorCode;
import com.chervon.operation.api.vo.SuggestionVo;
import com.chervon.operation.config.ExceptionMessageUtil;
import com.chervon.operation.domain.dataobject.MessageTemplate;
import com.chervon.operation.domain.dataobject.Suggestion;
import com.chervon.operation.domain.dto.suggestion.SuggestionDto;
import com.chervon.operation.domain.dto.suggestion.SuggestionPageDto;
import com.chervon.operation.domain.dto.suggestion.SuggestionRead;
import com.chervon.operation.domain.vo.suggestion.SuggestionExcel;
import com.chervon.operation.domain.vo.suggestion.SuggestionPageVo;
import com.chervon.operation.mapper.SuggestionMapper;
import com.chervon.operation.service.MessageTemplateService;
import com.chervon.operation.service.SuggestionService;
import com.chervon.operation.service.translate.TranslateUtils;
import com.chervon.technology.api.RemoteRelatedMsgTempService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.chervon.common.core.constant.CommonConstant.LINK_SIZE;
import static com.chervon.common.core.constant.CommonConstant.TEXT_SIZE;
import static com.chervon.operation.api.exception.OperationErrorCode.*;

/**
 * <AUTHOR>
 * @date 2022/11/30 14:18
 */
@Service
@Slf4j
public class SuggestionServiceImpl extends ServiceImpl<SuggestionMapper, Suggestion> implements SuggestionService {

    @DubboReference
    private RemoteMultiLanguageService remoteMultiLanguageService;

    @Autowired
    private TranslateUtils translateUtils;
    @DubboReference
    private RemoteRelatedMsgTempService remoteRelatedMsgTempService;

    private final MessageTemplateService messageTemplateService;

    public SuggestionServiceImpl(MessageTemplateService messageTemplateService) {
        this.messageTemplateService = messageTemplateService;
    }

    private List<Long> findTitleLangIds(String title) {
        List<Long> res = new ArrayList<>();
        if (StringUtils.isBlank(title)) {
            return res;
        }
        List<Suggestion> list = this.list();
        List<Long> titleLangIds = list.stream().map(Suggestion::getTitleLangId).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(titleLangIds)) {
            return res;
        }
        Map<@NotBlank String, @NotNull List<MultiLanguageBo>> map = remoteMultiLanguageService.listByTextLike(new HashMap<String, List<Long>>() {{
            put(title, titleLangIds);
        }}, LocaleContextHolder.getLocale().getLanguage());
        return map.get(title).stream().map(MultiLanguageBo::getLangId).collect(Collectors.toList());
    }

    @Override
    public SuggestionVo getBySuggestionId(Long id) {
        final Suggestion suggestion = getById(id);
        if(suggestion == null){
            return null;
        }else{
            final SuggestionVo suggestionVo = BeanCopyUtils.copy(suggestion, SuggestionVo.class);
            translateUtils.translate(suggestionVo);
            return suggestionVo;
        }
    }

    @Override
    public List<SuggestionVo> getListByIds(List<Long> listId) {
        LambdaQueryWrapper queryWrapper=new LambdaQueryWrapper<Suggestion>()
                .in(Suggestion::getId, listId)
                .select(Suggestion::getId, Suggestion::getTitle,
                        Suggestion::getContent,
                        Suggestion::getTitleLangId,
                        Suggestion::getContentLangId);
        final List<Suggestion> list = list(queryWrapper);
        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyList();
        } else {
            List<SuggestionVo> suggestionVoList = new ArrayList<>();
            for(Suggestion suggestion : list) {
                SuggestionVo suggestionVo = BeanCopyUtils.copy(suggestion, SuggestionVo.class);
                suggestionVo.setSuggestionId(suggestion.getId());
                suggestionVoList.add(suggestionVo);
            }
            translateUtils.translateListBatch(suggestionVoList);
            return suggestionVoList;
        }
    }

    @Override
    public List<SuggestionVo> getListContentByIds(List<Long> listId) {
        LambdaQueryWrapper queryWrapper=new LambdaQueryWrapper<Suggestion>()
                .in(Suggestion::getId, listId)
                .select(Suggestion::getId,Suggestion::getContentLangId);
        final List<Suggestion> list = list(queryWrapper);
        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyList();
        } else {
            List<SuggestionVo> suggestionVoList = new ArrayList<>();
            for(Suggestion suggestion : list) {
                SuggestionVo suggestionVo = BeanCopyUtils.copy(suggestion, SuggestionVo.class);
                suggestionVo.setSuggestionId(suggestion.getId());
                suggestionVoList.add(suggestionVo);
            }
            return suggestionVoList;
        }
    }

    /**
     * 根据处理建议获取处理内容（指定的所有语言）
     * @param listSuggestionId 处理建议集合列表
     * @param languages 语种列表
     * @return Map<SuggestionId,Map<language,content>>
     */
    @Override
    public Map<Long, Map<String, String>> listContentForLanguages(List<Long> listSuggestionId,List<String> languages) {
        List<SuggestionVo> suggestionVos = getListContentByIds(listSuggestionId);
        List<String> contentLangIds = suggestionVos.stream().map(m -> m.getContentLangId().toString()).collect(Collectors.toList());
        // key-多语言id value-(key-多语言 value-多语言内容)
        Map<String, Map<String, String>> langMapMap = remoteMultiLanguageService.batchGetLanguageContentByListLang(contentLangIds,languages);
        // key-消息模板ID, value-(key-多语言类型 value-多语言内容)
        Map<Long, Map<String, String>> result = new HashMap<>();
        for(SuggestionVo suggestionVo:suggestionVos){
            final Map<String, String> mapLangContent = langMapMap.get(suggestionVo.getContentLangId().toString());
            if(!CollectionUtils.isEmpty(mapLangContent)){
                result.put(suggestionVo.getSuggestionId(),mapLangContent);
            }
        }
        return result;
    }

    @Override
    public PageResult<SuggestionPageVo> page(SuggestionPageDto req) {
        PageResult<SuggestionPageVo> res = new PageResult<>(req.getPageNum(), req.getPageNum());
        List<Long> titleLangIds = findTitleLangIds(req.getTitle());
        // 如果没有匹配的多语言id，则返回空
        if (StringUtils.isNotBlank(req.getTitle()) && CollectionUtils.isEmpty(titleLangIds)) {
            return res;
        }
        IPage<Suggestion> page = this.getBaseMapper().selectSuggestionPage(new Page<>(req.getPageNum(), req.getPageSize()), req, titleLangIds);
        if (CollectionUtils.isEmpty(page.getRecords())) {
            return res;
        }
        res.setTotal(page.getTotal());
        Map<Long, List<Long>> map = remoteRelatedMsgTempService.listBySuggestionIds(page.getRecords().stream().map(BaseDo::getId).collect(Collectors.toList()));
        List<Long> msgTempIds = new ArrayList<>();
        map.forEach((k, v) -> msgTempIds.addAll(v));
        Map<Long, Long> msgTempMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(msgTempIds)) {
            msgTempMap = messageTemplateService.list(new LambdaQueryWrapper<MessageTemplate>().in(MessageTemplate::getId, msgTempIds).select(MessageTemplate::getId, MessageTemplate::getTitle))
                    .stream().collect(Collectors.toMap(BaseDo::getId, MessageTemplate::getTitle));
        }
        List<MultiLanguageBo> titleBos = new ArrayList<>();
        if (!CollectionUtils.isEmpty(msgTempMap)) {
            titleBos = remoteMultiLanguageService.listByIds(msgTempMap.values().stream().map(String::valueOf).collect(Collectors.toList()));
        }
        Map<Long, String> boMap = titleBos.stream().collect(Collectors.toMap(MultiLanguageBo::getLangId, MultiLanguageBo::getLangCode));
        List<SuggestionPageVo> data = new ArrayList<>();
        for (Suggestion e : page.getRecords()) {
            SuggestionPageVo vo = new SuggestionPageVo();
            BeanUtils.copyProperties(e, vo);
            vo.setSuggestionId(e.getId());
            vo.setTitle(com.chervon.operation.config.MultiLanguageUtil.getByLangCode(Optional.ofNullable(e.getTitleLangCode()).orElse(""), LocaleContextHolder.getLocale().getLanguage()));
            vo.setContent(com.chervon.operation.config.MultiLanguageUtil.getByLangCode(Optional.ofNullable(e.getContentLangCode()).orElse(""), LocaleContextHolder.getLocale().getLanguage()));
            List<Long> longs = map.get(e.getId());
            if (!CollectionUtils.isEmpty(longs)) {
                for (Long l : new HashSet<>(longs)) {
                    Long aLong = msgTempMap.get(l);
                    if (aLong != null) {
                        vo.getMsg().add(com.chervon.operation.config.MultiLanguageUtil.getByLangCode(Optional.ofNullable(boMap.get(aLong)).orElse(""), LocaleContextHolder.getLocale().getLanguage()));
                    }
                }
            }
            data.add(vo);
        }
        res.setList(data);
        return res;
    }

    private void check(SuggestionDto req) {
        if (StringUtils.isBlank(req.getTitle())) {
            throw ExceptionMessageUtil.getException(OPERATION_SUGGESTION_TITLE_NULL);
        }
        if (StringUtils.isBlank(req.getContent())) {
            throw ExceptionMessageUtil.getException(OPERATION_SUGGESTION_CONTENT_NULL);
        }
        if (StringUtils.isNotBlank(req.getExtra()) && !(req.getExtra().startsWith("http://") || req.getExtra().startsWith("https://"))) {
            throw ExceptionMessageUtil.getException(OPERATION_SUGGESTION_EXTRA_ILLEGAL, req.getExtra());
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void add(SuggestionDto req) {
        check(req);
        Suggestion suggestion = new Suggestion();
        suggestion.setTitle(req.getTitle());
        suggestion.setContent(req.getContent());
        suggestion.setExtra(req.getExtra());
        Map<String, String> map = new HashMap<String, String>() {{
            put("1", req.getTitle());
            put("2", req.getContent());
        }};
        Map<String, MultiLanguageBo> boMap = remoteMultiLanguageService.simpleCreateMultiLanguages(ApplicationEnum.OPERATION_PLATFORM.getName(), map, LocaleContextHolder.getLocale().getLanguage());
        MultiLanguageBo bo = boMap.getOrDefault("1", new MultiLanguageBo());
        suggestion.setTitleLangId(bo.getLangId());
        suggestion.setTitleLangCode(bo.getLangCode());
        bo = boMap.getOrDefault("2", new MultiLanguageBo());
        suggestion.setContentLangId(bo.getLangId());
        suggestion.setContentLangCode(bo.getLangCode());
        this.save(suggestion);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void edit(SuggestionDto req) {
        if (req.getSuggestionId() == null) {
            throw ExceptionMessageUtil.getException(OperationErrorCode.OPERATION_SUGGESTION_ID_NULL);
        }
        Suggestion suggestion = this.getById(req.getSuggestionId());
        if (suggestion == null) {
            throw ExceptionMessageUtil.getException(OperationErrorCode.OPERATION_SUGGESTION_NULL, req.getSuggestionId());
        }
        check(req);
        Suggestion one = new Suggestion();
        one.setId(suggestion.getId());
        one.setTitle(req.getTitle());
        one.setContent(req.getContent());
        one.setExtra(req.getExtra());
        Map<String, String> cMap = new HashMap<>();
        Map<Long, String> uMap = new HashMap<>();
        if (StringUtils.isBlank(suggestion.getTitleLangCode())) {
            cMap.put("1", req.getTitle());
        } else {
            uMap.put(suggestion.getTitleLangId(), req.getTitle());
        }
        if (StringUtils.isBlank(suggestion.getContentLangCode())) {
            cMap.put("2", req.getTitle());
        } else {
            uMap.put(suggestion.getContentLangId(), req.getContent());
        }
        if (cMap.size() > 0) {
            Map<String, MultiLanguageBo> boMap = remoteMultiLanguageService.simpleCreateMultiLanguages(ApplicationEnum.OPERATION_PLATFORM.getName(), cMap, LocaleContextHolder.getLocale().getLanguage());
            MultiLanguageBo bo1 = boMap.get("1");
            if (bo1 != null) {
                one.setTitleLangId(bo1.getLangId());
                one.setTitleLangCode(bo1.getLangCode());
            }
            MultiLanguageBo bo2 = boMap.get("2");
            if (bo2 != null) {
                one.setContentLangId(bo2.getLangId());
                one.setContentLangCode(bo2.getLangCode());
            }
        }
        if (uMap.size() > 0) {
            remoteMultiLanguageService.simpleUpdateMultiLanguages(ApplicationEnum.OPERATION_PLATFORM.getName(), uMap, LocaleContextHolder.getLocale().getLanguage());
        }
        this.updateById(one);
    }

    @Override
    public SuggestionVo detail(Long suggestionId) {
        if (suggestionId == null) {
            throw ExceptionMessageUtil.getException(OperationErrorCode.OPERATION_SUGGESTION_ID_NULL);
        }
        Suggestion suggestion = this.getById(suggestionId);
        if (suggestion == null) {
            throw ExceptionMessageUtil.getException(OperationErrorCode.OPERATION_SUGGESTION_NULL, suggestionId);
        }
        SuggestionVo res = new SuggestionVo();
        res.setSuggestionId(suggestion.getId());
        res.setTitleLangId(suggestion.getTitleLangId());
        res.setContentLangId(suggestion.getContentLangId());
        res.setTitle(com.chervon.operation.config.MultiLanguageUtil.getByLangCode(Optional.ofNullable(suggestion.getTitleLangCode()).orElse(""), LocaleContextHolder.getLocale().getLanguage()));
        res.setContent(com.chervon.operation.config.MultiLanguageUtil.getByLangCode(Optional.ofNullable(suggestion.getContentLangCode()).orElse(""), LocaleContextHolder.getLocale().getLanguage()));
        res.setExtra(suggestion.getExtra());
        Map<Long, List<Long>> map = remoteRelatedMsgTempService.listBySuggestionIds(Collections.singletonList(suggestionId));
        if (!CollectionUtils.isEmpty(map)) {
            Set<Long> msgTempIds = new HashSet<>(map.get(suggestionId));
            if (!CollectionUtils.isEmpty(msgTempIds)) {
                Map<Long, Long> msgTempMap = new HashMap<>();
                if (!CollectionUtils.isEmpty(msgTempIds)) {
                    msgTempMap = messageTemplateService.list(new LambdaQueryWrapper<MessageTemplate>().in(MessageTemplate::getId, msgTempIds).select(MessageTemplate::getId, MessageTemplate::getTitle))
                            .stream().collect(Collectors.toMap(BaseDo::getId, MessageTemplate::getTitle));
                }
                List<MultiLanguageBo> titleBos = new ArrayList<>();
                if (!CollectionUtils.isEmpty(msgTempMap)) {
                    titleBos = remoteMultiLanguageService.listByIds(msgTempMap.values().stream().map(String::valueOf).collect(Collectors.toList()));
                }
                Map<Long, String> boMap = titleBos.stream().collect(Collectors.toMap(MultiLanguageBo::getLangId, MultiLanguageBo::getLangCode));
                for (Long l : msgTempIds) {
                    Long aLong = msgTempMap.get(l);
                    if (aLong != null) {
                        res.getMsg().add(com.chervon.operation.config.MultiLanguageUtil.getByLangCode(Optional.ofNullable(boMap.get(aLong)).orElse(""), LocaleContextHolder.getLocale().getLanguage()));
                    }
                }
            }
        }
        return res;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void delete(Long suggestionId) {
        if (suggestionId == null) {
            throw ExceptionMessageUtil.getException(OperationErrorCode.OPERATION_SUGGESTION_ID_NULL);
        }
        Suggestion suggestion = this.getById(suggestionId);
        if (suggestion == null) {
            throw ExceptionMessageUtil.getException(OperationErrorCode.OPERATION_SUGGESTION_NULL, suggestionId);
        }
        // 清理多语言
        List<String> langCodes = new ArrayList<>();
        if (StringUtils.isNotEmpty(suggestion.getTitleLangCode())) {
            langCodes.add(suggestion.getTitleLangCode());
        }
        if (StringUtils.isNotEmpty(suggestion.getContentLangCode())) {
            langCodes.add(suggestion.getContentLangCode());
        }
        if (langCodes.size() > 0) {
            remoteMultiLanguageService.deleteByLangCodes(langCodes);
        }
        if (remoteRelatedMsgTempService.checkSuggestionUsed(suggestionId)) {
            throw ExceptionMessageUtil.getException(OperationErrorCode.OPERATION_SUGGESTION_CANNOT_DELETE);
        }
        this.removeById(suggestion.getId());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<String> importSuggestion(MultipartFile file) {
        List<String> res = new ArrayList<>();
        List<SuggestionRead> data;
        try {
            data = EasyExcel.read(file.getInputStream()).head(SuggestionRead.class).sheet().doReadSync();
        } catch (Exception e) {
            throw ExceptionMessageUtil.getException(OPERATION_SUGGESTION_READ_EXCEL_ERROR);
        }
        if (CollectionUtils.isEmpty(data)) {
            throw ExceptionMessageUtil.getException(OPERATION_SUGGESTION_READ_EXCEL_EMPTY);
        }
        if (data.size() > 5000) {
            throw ExceptionMessageUtil.getException(OPERATION_SUGGESTION_READ_EXCEL_MORE_THAN_5000);
        }
        if (!validImport(data, res)) {
            return res;
        }
        List<String> collect = data.stream().map(SuggestionRead::getSuggestionId).collect(Collectors.toList());
        List<Suggestion> orFaq = new ArrayList<>();
        if (!CollectionUtils.isEmpty(collect)) {
            orFaq = this.list(new LambdaQueryWrapper<Suggestion>().in(Suggestion::getId, collect));
        }
        Map<Long, Suggestion> orFaqMap = orFaq.stream().collect(Collectors.toMap(Suggestion::getId, Function.identity()));

        Map<Long, String> mUpdate = new HashMap<>();
        Map<String, String> mCreate = new HashMap<>();
        final int[] flag = {1};
        List<Suggestion> list = data.stream().map(e -> {
            Suggestion faq = new Suggestion();
            faq.setTitle(e.getTitle());
            faq.setContent(e.getContent());
            faq.setExtra(e.getExtra());
            if (StringUtils.isNotBlank(e.getSuggestionId())) {
                Suggestion f = orFaqMap.get(Long.parseLong(e.getSuggestionId()));
                if (f != null) {
                    faq.setId(f.getId());
                    if (f.getTitleLangId() != null) {
                        mUpdate.put(f.getTitleLangId(), e.getTitle());
                    } else {
                        mCreate.put(flag[0] + "", e.getTitle());
                        faq.setTitle(flag[0] + "");
                        flag[0]++;
                    }
                    if (f.getContentLangId() != null) {
                        mUpdate.put(f.getContentLangId(), e.getContent());
                    } else {
                        mCreate.put(flag[0] + "", e.getContent());
                        faq.setContent(flag[0] + "");
                        flag[0]++;
                    }
                } else {
                    mCreate.put(flag[0] + "", e.getTitle());
                    faq.setTitle(flag[0] + "");
                    flag[0]++;
                    mCreate.put(flag[0] + "", e.getContent());
                    faq.setContent(flag[0] + "");
                    flag[0]++;
                }
            } else {
                mCreate.put(flag[0] + "", e.getTitle());
                faq.setTitle(flag[0] + "");
                flag[0]++;
                mCreate.put(flag[0] + "", e.getContent());
                faq.setContent(flag[0] + "");
                flag[0]++;
            }
            return faq;
        }).collect(Collectors.toList());
        if (mUpdate.size() > 0) {
            remoteMultiLanguageService.simpleUpdateMultiLanguages(ApplicationEnum.OPERATION_PLATFORM.getName(), mUpdate, LocaleContextHolder.getLocale().getLanguage());
        }
        if (mCreate.size() > 0) {
            Map<String, MultiLanguageBo> boMap = remoteMultiLanguageService.simpleCreateMultiLanguages(ApplicationEnum.OPERATION_PLATFORM.getName(), mCreate, LocaleContextHolder.getLocale().getLanguage());
            list.forEach(e -> {
                if (e.getTitleLangId() == null) {
                    MultiLanguageBo bo = boMap.get(e.getTitle());
                    e.setTitleLangId(bo.getLangId());
                    e.setTitleLangCode(bo.getLangCode());
                    e.setTitle(mCreate.get(e.getTitle()));
                }
                if (e.getContentLangId() == null) {
                    MultiLanguageBo bo = boMap.get(e.getContent());
                    e.setContentLangId(bo.getLangId());
                    e.setContentLangCode(bo.getLangCode());
                    e.setContent(mCreate.get(e.getContent()));
                }
            });
        }
        this.saveOrUpdateBatch(list);
        return res;
    }

    private boolean validImport(List<SuggestionRead> data, List<String> res) {
        List<Integer> idString = new ArrayList<>();
        Map<String, List<Integer>> idMap = new HashMap<>();
        List<Integer> titleNullFlag = new ArrayList<>();
        List<Integer> contentNullFlag = new ArrayList<>();
        List<Integer> extraFlag = new ArrayList<>();
        List<Integer> titleSizeFlag = new ArrayList<>();
        List<Integer> extraSizeFlag = new ArrayList<>();

        for (int i = 2, j = data.size() + 2; i < j; i++) {
            SuggestionRead read = data.get(i - 2);

            if (StringUtils.isNotBlank(read.getSuggestionId())) {
                try {
                    Long.parseLong(read.getSuggestionId());
                } catch (Exception e) {
                    idString.add(i);
                }
                if (idMap.get(read.getSuggestionId()) == null) {
                    List<Integer> ids = new ArrayList<>();
                    ids.add(i);
                    idMap.put(read.getSuggestionId(), ids);
                } else {
                    idMap.get(read.getSuggestionId()).add(i);
                }
            }

            if (StringUtils.isBlank(read.getTitle())) {
                titleNullFlag.add(i);
            } else if (read.getTitle().length() > TEXT_SIZE) {
                titleSizeFlag.add(i);
            }

            if (StringUtils.isBlank(read.getContent())) {
                contentNullFlag.add(i);
            }

            if (StringUtils.isNotBlank(read.getExtra())) {
                if (read.getExtra().length() > LINK_SIZE) {
                    extraSizeFlag.add(i);
                }
                if (!(read.getExtra().startsWith("http://") || read.getExtra().startsWith("https://"))) {
                    extraFlag.add(i);
                }
            }

        }
        // 检测id是否为数字
        if (idString.size() > 0) {
            res.add("第" + idString.stream().map(String::valueOf).collect(Collectors.joining("，")) + "行，【建议ID】非法");
        }
        // 检测id是否唯一
        for (Map.Entry<String, List<Integer>> entry : idMap.entrySet()) {
            List<Integer> value = entry.getValue();
            if (value.size() != 1) {
                res.add("第" + value.stream().map(String::valueOf).collect(Collectors.joining("，")) + "行，【建议ID】重复");
            }
        }
        // 检测title是否必填
        if (titleNullFlag.size() > 0) {
            res.add("第" + titleNullFlag.stream().map(String::valueOf).collect(Collectors.joining("，")) + "行，【处理建议标题】为空");
        }
        // 检测title长度
        if (titleSizeFlag.size() > 0) {
            res.add("第" + titleSizeFlag.stream().map(String::valueOf).collect(Collectors.joining("，")) + "行，【处理建议标题】内容超过" + TEXT_SIZE + "个字符");
        }
        // 检测content是否必填
        if (contentNullFlag.size() > 0) {
            res.add("第" + contentNullFlag.stream().map(String::valueOf).collect(Collectors.joining("，")) + "行，【处理建议】为空");
        }
        // 检测extra长度
        if (extraSizeFlag.size() > 0) {
            res.add("第" + extraSizeFlag.stream().map(String::valueOf).collect(Collectors.joining("，")) + "行，【附加内容】内容超过" + LINK_SIZE + "个字符");
        }
        // 检测extra是否以https://或者http://开头
        if (extraFlag.size() > 0) {
            res.add("第" + extraFlag.stream().map(String::valueOf).collect(Collectors.joining("，")) + "行，【附加内容】不是以https://或者http://开头");
        }
        if (!res.isEmpty()) {
            return false;
        }

        List<Long> ids = data.stream().filter(e -> StringUtils.isNotBlank(e.getSuggestionId())).map(e -> Long.parseLong(e.getSuggestionId())).distinct().collect(Collectors.toList());
        if (!ids.isEmpty()) {
            List<Suggestion> faqs = this.list(new LambdaQueryWrapper<Suggestion>().in(Suggestion::getId, ids));
            List<Long> existIds = faqs.stream().map(Suggestion::getId).collect(Collectors.toList());
            for (int i = 2, j = data.size() + 2; i < j; i++) {
                SuggestionRead read = data.get(i - 2);
                if (StringUtils.isBlank(read.getSuggestionId())) {
                    continue;
                }
                if (!existIds.contains(Long.parseLong(read.getSuggestionId()))) {
                    res.add("第" + i + "行，【建议ID】不存在");
                }
            }
        }
        return res.isEmpty();
    }

    @Override
    public List<SuggestionExcel> listData(SuggestionPageDto req) {
        List<Long> titleLangIds = findTitleLangIds(req.getTitle());
        // 如果没有匹配的多语言id，则返回空
        if (StringUtils.isNotBlank(req.getTitle()) && CollectionUtils.isEmpty(titleLangIds)) {
            return new ArrayList<>();
        }
        List<Suggestion> list = this.getBaseMapper().selectSuggestionList(req, titleLangIds);
        if (CollectionUtils.isEmpty(list)) {
            return new ArrayList<>();
        }

        Map<Long, List<Long>> map = remoteRelatedMsgTempService.listBySuggestionIds(list.stream().map(BaseDo::getId).collect(Collectors.toList()));
        List<Long> msgTempIds = new ArrayList<>();
        map.forEach((k, v) -> msgTempIds.addAll(v));
        Map<Long, Long> msgTempMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(msgTempIds)) {
            msgTempMap = messageTemplateService.list(new LambdaQueryWrapper<MessageTemplate>().in(MessageTemplate::getId, msgTempIds).select(MessageTemplate::getId, MessageTemplate::getTitle))
                    .stream().collect(Collectors.toMap(BaseDo::getId, MessageTemplate::getTitle));
        }
        List<MultiLanguageBo> titleBos = new ArrayList<>();
        if (!CollectionUtils.isEmpty(msgTempMap)) {
            titleBos = remoteMultiLanguageService.listByIds(msgTempMap.values().stream().map(String::valueOf).collect(Collectors.toList()));
        }
        Map<Long, String> boMap = titleBos.stream().collect(Collectors.toMap(MultiLanguageBo::getLangId, MultiLanguageBo::getLangCode));
        List<SuggestionExcel> data = new ArrayList<>();
        for (Suggestion e : list) {
            SuggestionExcel vo = new SuggestionExcel();
            BeanUtils.copyProperties(e, vo);
            vo.setSuggestionId(e.getId());
            vo.setTitle(com.chervon.operation.config.MultiLanguageUtil.getByLangCode(Optional.ofNullable(e.getTitleLangCode()).orElse(""), LocaleContextHolder.getLocale().getLanguage()));
            List<Long> longs = map.get(e.getId());
            StringBuilder sb = new StringBuilder();
            if (!CollectionUtils.isEmpty(longs)) {
                for (Long l : longs) {
                    Long aLong = msgTempMap.get(l);
                    if (aLong != null) {
                        String t = com.chervon.operation.config.MultiLanguageUtil.getByLangCode(Optional.ofNullable(boMap.get(aLong)).orElse(""), LocaleContextHolder.getLocale().getLanguage());
                        sb.append(t).append(",");
                    }
                }
            }
            String s = sb.toString();
            if (StringUtils.isNotBlank(s)) {
                vo.setMsg(s.substring(0, sb.length() - 1));
            }
            vo.setCreateTime(DateTimeZoneUtil.format(e.getCreateTime(), req.getZone()));
            vo.setUpdateTime(DateTimeZoneUtil.format(e.getUpdateTime(), req.getZone()));
            data.add(vo);
        }
        return data;
    }

}
