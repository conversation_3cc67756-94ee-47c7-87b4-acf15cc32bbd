package com.chervon.operation.service.impl;

import com.alibaba.excel.EasyExcel;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.chervon.common.core.domain.PageResult;
import com.chervon.common.core.enums.ApplicationEnum;
import com.chervon.common.core.utils.DateTimeZoneUtil;
import com.chervon.common.core.utils.SnowFlake;
import com.chervon.common.mybatis.config.BaseDo;
import com.chervon.configuration.api.core.MultiLanguageBo;
import com.chervon.configuration.api.service.RemoteMultiLanguageService;
import com.chervon.operation.api.exception.OperationErrorCode;
import com.chervon.operation.config.ExceptionMessageUtil;
import com.chervon.operation.config.MultiLanguageUtil;
import com.chervon.operation.domain.bo.DictBo;
import com.chervon.operation.domain.bo.DictNodeBo;
import com.chervon.operation.domain.dataobject.*;
import com.chervon.operation.domain.dto.CommonOperationProductDto;
import com.chervon.operation.domain.dto.CommonOperationProductPageDto;
import com.chervon.operation.domain.dto.operationguidance.common.*;
import com.chervon.operation.domain.vo.operationguidance.OperationGuidanceVo;
import com.chervon.operation.domain.vo.operationguidance.common.CommonOperationGuidanceExcel;
import com.chervon.operation.domain.vo.operationguidance.common.CommonOperationGuidanceManagePageVo;
import com.chervon.operation.domain.vo.operationguidance.common.CommonOperationGuidanceVo;
import com.chervon.operation.mapper.CommonOperationGuidanceMapper;
import com.chervon.operation.service.*;
import com.chervon.technology.api.RemoteTechProductOperationService;
import com.chervon.technology.api.vo.CommonProductVo;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.chervon.common.core.constant.CommonConstant.LINK_SIZE;
import static com.chervon.common.core.constant.CommonConstant.TEXT_SIZE;
import static com.chervon.operation.api.exception.OperationErrorCode.*;

/**
 * <AUTHOR>
 * @date 2022/10/25 11:21
 */
@Service
public class CommonOperationGuidanceServiceImpl extends ServiceImpl<CommonOperationGuidanceMapper, CommonOperationGuidance> implements CommonOperationGuidanceService {

    @Autowired
    private OperationGuidanceService operationGuidanceService;

    @DubboReference
    private RemoteMultiLanguageService remoteMultiLanguageService;

    @DubboReference
    private RemoteTechProductOperationService remoteTechProductOperationService;

    @Autowired
    private CategoryService categoryService;

    @Autowired
    private ProductOperationGuidanceService productOperationGuidanceService;

    @Autowired
    private DictService dictService;

    private List<Long> findNameLangIds(String name) {
        List<Long> res = new ArrayList<>();
        if (StringUtils.isBlank(name)) {
            return res;
        }
        List<CommonOperationGuidance> list = this.list();
        List<Long> nameLangIds = list.stream().map(CommonOperationGuidance::getNameLangId).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(nameLangIds)) {
            return res;
        }
        Map<@NotBlank String, @NotNull List<MultiLanguageBo>> map = remoteMultiLanguageService.listByTextLike(new HashMap<String, List<Long>>() {{
            put(name, nameLangIds);
        }}, LocaleContextHolder.getLocale().getLanguage());
        return map.get(name).stream().map(MultiLanguageBo::getLangId).collect(Collectors.toList());
    }

    @Override
    public PageResult<CommonOperationGuidanceManagePageVo> managePage(CommonOperationGuidanceManagePageDto req) {
        PageResult<CommonOperationGuidanceManagePageVo> res = new PageResult<>(req.getPageNum(), req.getPageNum());
        List<Long> nameLangIds = findNameLangIds(req.getName());
        // 如果没有匹配的多语言id，则返回空
        if (StringUtils.isNotBlank(req.getName()) && CollectionUtils.isEmpty(nameLangIds)) {
            return res;
        }
        IPage<CommonOperationGuidance> page = this.getBaseMapper().selectManagePage(new Page<>(req.getPageNum(), req.getPageSize()), req, nameLangIds);
        if (CollectionUtils.isEmpty(page.getRecords())) {
            return res;
        }
        res.setTotal(page.getTotal());
        List<OperationGuidance> operationGuidanceList = operationGuidanceService.listByIds(page.getRecords().stream().map(CommonOperationGuidance::getOperationGuidanceId).filter(Objects::nonNull).distinct().collect(Collectors.toList()));
        Map<Long, OperationGuidance> map = operationGuidanceList.stream().collect(Collectors.toMap(BaseDo::getId, Function.identity()));
        res.setList(page.getRecords().stream().map(e -> {
            CommonOperationGuidanceManagePageVo vo = new CommonOperationGuidanceManagePageVo();
            BeanUtils.copyProperties(e, vo);
            vo.setCommonOperationGuidanceId(e.getId());
            OperationGuidanceVo operationGuidance = new OperationGuidanceVo();
            OperationGuidance guidance = map.getOrDefault(e.getOperationGuidanceId(), new OperationGuidance());
            BeanUtils.copyProperties(guidance, operationGuidance);
            operationGuidance.setName(MultiLanguageUtil.getByLangCode(Optional.ofNullable(guidance.getNameLangCode()).orElse(""), LocaleContextHolder.getLocale().getLanguage()));
            operationGuidance.setDescription(MultiLanguageUtil.getByLangCode(Optional.ofNullable(guidance.getDescriptionLangCode()).orElse(""), LocaleContextHolder.getLocale().getLanguage()));
            vo.setOperationGuidance(operationGuidance);
            vo.setCreateBy(e.getCreateBy());
            vo.setUpdateBy(e.getUpdateBy());
            vo.setCreateTime(e.getCreateTime());
            vo.setUpdateTime(e.getUpdateTime());
            return vo;
        }).collect(Collectors.toList()));
        return res;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void add(CommonOperationGuidanceDto req) {
        check(req);
        OperationGuidance operationGuidance = operationGuidanceService.add(req.getOperationGuidance());
        CommonOperationGuidance one = new CommonOperationGuidance();
        one.setOperationGuidanceId(operationGuidance.getId());
        one.setNameLangId(operationGuidance.getNameLangId());
        save(one);
    }

    private void check(CommonOperationGuidanceDto req) {
        if (req.getOperationGuidance() == null) {
            throw ExceptionMessageUtil.getException(OperationErrorCode.OPERATION_COMMON_OPERATION_GUIDANCE_NULL);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void edit(CommonOperationGuidanceDto req) {
        if (req.getCommonOperationGuidanceId() == null) {
            throw ExceptionMessageUtil.getException(OperationErrorCode.OPERATION_COMMON_OPERATION_GUIDANCE_ID_NULL);
        }
        CommonOperationGuidance common = this.getById(req.getCommonOperationGuidanceId());
        if (common == null) {
            throw ExceptionMessageUtil.getException(OperationErrorCode.OPERATION_COMMON_OPERATION_GUIDANCE_NULL);
        }
        check(req);
        req.getOperationGuidance().setOperationGuidanceId(common.getOperationGuidanceId());
        operationGuidanceService.edit(req.getOperationGuidance());
        CommonOperationGuidance one = new CommonOperationGuidance();
        one.setId(common.getId());

        this.updateById(one);
    }

    @Override
    public CommonOperationGuidanceVo detail(Long commonOperationGuidanceId) {
        if (commonOperationGuidanceId == null) {
            throw ExceptionMessageUtil.getException(OperationErrorCode.OPERATION_COMMON_OPERATION_GUIDANCE_ID_NULL);
        }
        CommonOperationGuidance common = this.getById(commonOperationGuidanceId);
        if (common == null) {
            throw ExceptionMessageUtil.getException(OperationErrorCode.OPERATION_COMMON_OPERATION_GUIDANCE_NULL);
        }
        CommonOperationGuidanceVo res = new CommonOperationGuidanceVo();
        res.setCommonOperationGuidanceId(common.getId());

        OperationGuidanceVo operationGuidance = new OperationGuidanceVo();
        OperationGuidance guidance = operationGuidanceService.getById(common.getOperationGuidanceId());
        if (guidance != null) {
            BeanUtils.copyProperties(guidance, operationGuidance);
            operationGuidance.setName(MultiLanguageUtil.getByLangCode(Optional.ofNullable(guidance.getNameLangCode()).orElse(""), LocaleContextHolder.getLocale().getLanguage()));
            operationGuidance.setDescription(MultiLanguageUtil.getByLangCode(Optional.ofNullable(guidance.getDescriptionLangCode()).orElse(""), LocaleContextHolder.getLocale().getLanguage()));
        }
        res.setOperationGuidance(operationGuidance);
        return res;
    }

    @Override
    public PageResult<CommonProductVo> productPage(CommonOperationProductPageDto req) {
        //校验参数
        if (req.getCommonId() == null) {
            throw ExceptionMessageUtil.getException(OperationErrorCode.OPERATION_COMMON_OPERATION_GUIDANCE_ID_NULL);
        }
        CommonOperationGuidance common = this.getById(req.getCommonId());
        if (common == null) {
            throw ExceptionMessageUtil.getException(OperationErrorCode.OPERATION_COMMON_OPERATION_GUIDANCE_NULL);
        }
        PageResult<CommonProductVo> res = new PageResult<>(req.getPageNum(), req.getPageSize());

        List<Long> productIds = productOperationGuidanceService.listProductIdByCommonId(req.getCommonId());
        if (CollectionUtils.isEmpty(productIds)) {
            return res;
        }
        PageResult<CommonProductVo> page = remoteTechProductOperationService.page(LocaleContextHolder.getLocale().getLanguage(), productIds, req.getProductId(), req.getCategoryId(), req.getModel(), req.getCommodityModel(), req.getProductName(), req.getPageNum(), req.getPageSize());
        if (CollectionUtils.isEmpty(page.getList())) {
            return res;
        }
        //查询品类多语言
        List<Long> categoryIds = page.getList().stream().map(CommonProductVo::getCategoryId).collect(Collectors.toList());
        List<Category> categories = new ArrayList<>();
        if (!CollectionUtils.isEmpty(categoryIds)) {
            categories = categoryService.listByIds(categoryIds);
        }
        //查询产品多语言
        Map<Long, String> collect = categories.stream().collect(Collectors.toMap(BaseDo::getId, Category::getCategoryName));
        List<String> ids = new ArrayList<>();
        ids.addAll(collect.values());
        ids.addAll(page.getList().stream().map(CommonProductVo::getProductName).collect(Collectors.toList()));
        List<MultiLanguageBo> bos = new ArrayList<>();
        if (!CollectionUtils.isEmpty(ids)) {
            bos = remoteMultiLanguageService.listByIds(ids);
        }
        Map<Long, String> map = bos.stream().collect(Collectors.toMap(MultiLanguageBo::getLangId, MultiLanguageBo::getLangCode));
        page.getList().forEach(e -> {
                String nameId = collect.get(e.getCategoryId());
                String nameCode = map.get(Long.parseLong(nameId));
                e.setCategoryName(com.chervon.operation.config.MultiLanguageUtil.getByLangCode(Optional.ofNullable(nameCode).orElse(""), LocaleContextHolder.getLocale().getLanguage()));
                String productNameCode = StringUtils.isEmpty(e.getProductName())?null:map.get(Long.parseLong(e.getProductName()));
                e.setProductName(com.chervon.operation.config.MultiLanguageUtil.getByLangCode(Optional.ofNullable(productNameCode).orElse(""), LocaleContextHolder.getLocale().getLanguage()));
        });
        return page;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void productAdd(CommonOperationProductDto req) {
        if (req.getCommonId() == null) {
            throw ExceptionMessageUtil.getException(OperationErrorCode.OPERATION_COMMON_OPERATION_GUIDANCE_ID_NULL);
        }
        CommonOperationGuidance common = this.getById(req.getCommonId());
        if (common == null) {
            throw ExceptionMessageUtil.getException(OperationErrorCode.OPERATION_COMMON_OPERATION_GUIDANCE_NULL);
        }
        if (CollectionUtils.isEmpty(req.getRelatedProduct())) {
            return;
        }
        // 在产品操作指导新增通用部分
        addCommonToProduct(common, req.getRelatedProduct().stream().map(String::valueOf).distinct().collect(Collectors.toList()));
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void productDelete(CommonOperationProductDto req) {
        if (req.getCommonId() == null) {
            throw ExceptionMessageUtil.getException(OperationErrorCode.OPERATION_COMMON_OPERATION_GUIDANCE_ID_NULL);
        }
        CommonOperationGuidance common = this.getById(req.getCommonId());
        if (common == null) {
            throw ExceptionMessageUtil.getException(OperationErrorCode.OPERATION_COMMON_OPERATION_GUIDANCE_NULL);
        }
        if (CollectionUtils.isEmpty(req.getRelatedProduct())) {
            return;
        }
        // 在产品操作指导删除通用部分
        deleteCommonFromProduct(common, req.getRelatedProduct().stream().map(String::valueOf).collect(Collectors.toList()));
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void delete(Long commonOperationGuidanceId) {
        if (commonOperationGuidanceId == null) {
            throw ExceptionMessageUtil.getException(OperationErrorCode.OPERATION_COMMON_OPERATION_GUIDANCE_ID_NULL);
        }
        CommonOperationGuidance common = this.getById(commonOperationGuidanceId);
        if (common == null) {
            throw ExceptionMessageUtil.getException(OperationErrorCode.OPERATION_COMMON_OPERATION_GUIDANCE_NULL);
        }
        List<Long> langIds = new ArrayList<>();
        if (common.getNameLangId() != null) {
            langIds.add(common.getNameLangId());
        }
        //判断是否绑定了产品
        if (productOperationGuidanceService.countProductIdByCommonId(common.getId())>0) {
            throw ExceptionMessageUtil.getException(OperationErrorCode.OPERATION_COMMON_OPERATION_GUIDANCE_RELATED_PRODUCT_CANNOT_DELETE);
        }

        OperationGuidance operationGuidance = operationGuidanceService.getById(common.getOperationGuidanceId());
        if (operationGuidance != null) {
             if (operationGuidance.getDescriptionLangId() != null) {
                 langIds.add(operationGuidance.getDescriptionLangId());
             }
            if (operationGuidance.getNameLangId() != null) {
                langIds.add(operationGuidance.getNameLangId());
            }
            operationGuidanceService.removeById(common.getOperationGuidanceId());
        }
        if (langIds.size() > 0) {
            // 清理多语言
            remoteMultiLanguageService.deleteByLangIds(langIds);
        }
        this.removeById(common.getId());
    }

    @Override
    public String download(Long commonOperationGuidanceId) {
        if (commonOperationGuidanceId == null) {
            throw ExceptionMessageUtil.getException(OperationErrorCode.OPERATION_COMMON_OPERATION_GUIDANCE_ID_NULL);
        }
        CommonOperationGuidance common = this.getById(commonOperationGuidanceId);
        if (common == null) {
            throw ExceptionMessageUtil.getException(OperationErrorCode.OPERATION_COMMON_OPERATION_GUIDANCE_NULL);
        }
        OperationGuidance guidance = operationGuidanceService.getById(common.getOperationGuidanceId());
        return Optional.ofNullable(guidance).orElse(new OperationGuidance()).getUrl();
    }

    /**
     * 批量绑定通用操作指导和产品关系
     * @param common
     * @param pIds
     */

    private void addCommonToProduct(CommonOperationGuidance common, List<String> pIds) {
        //校验参数产品id集合是否被绑定过
        productOperationGuidanceService.checkBoundByCommonIdAndPIds(common.getId(),pIds);

        //组装批量保存
        List<ProductOperationGuidance> data = new ArrayList<>();
        pIds.forEach(e -> {
                ProductOperationGuidance instance = new ProductOperationGuidance();
                instance.setProductId(Long.parseLong(e));
                instance.setCommonId(common.getId());
                instance.setOperationGuidanceId(common.getOperationGuidanceId());
                instance.setSequence(0);
                data.add(instance);
        });
        productOperationGuidanceService.saveBatch(data);

    }

    private void deleteCommonFromProduct(CommonOperationGuidance common, List<String> pIds) {
        if (!CollectionUtils.isEmpty(pIds)) {
            productOperationGuidanceService.remove(new LambdaQueryWrapper<ProductOperationGuidance>()
                    .eq(ProductOperationGuidance::getCommonId, common.getId())
                    .in(ProductOperationGuidance::getProductId, pIds));
        }
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<String> importCommonOperationGuidance(MultipartFile file) {
        List<String> res = new ArrayList<>();
        List<CommonOperationGuidanceRead> data;
        try {
            data = EasyExcel.read(file.getInputStream()).head(CommonOperationGuidanceRead.class).sheet().doReadSync();
        } catch (Exception e) {
            throw ExceptionMessageUtil.getException(OPERATION_COMMON_OPERATION_GUIDANCE_READ_EXCEL_ERROR);
        }
        if (CollectionUtils.isEmpty(data)) {
            throw ExceptionMessageUtil.getException(OPERATION_COMMON_OPERATION_GUIDANCE_READ_EXCEL_EMPTY);
        }
        if (data.size() > 5000) {
            throw ExceptionMessageUtil.getException(OPERATION_COMMON_OPERATION_GUIDANCE_READ_EXCEL_MORE_THAN_5000);
        }
        if (!validImport(data, res)) {
            return res;
        }
        List<String> update = new ArrayList<>();
        List<String> collect = data.stream().map(CommonOperationGuidanceRead::getInstanceId).collect(Collectors.toList());
        List<OperationGuidance> orOperationGuidance = new ArrayList<>();
        if (!CollectionUtils.isEmpty(collect)) {
            orOperationGuidance = operationGuidanceService.list(new LambdaQueryWrapper<OperationGuidance>().in(OperationGuidance::getInstanceId, collect));
        }
        Map<Long, OperationGuidance> orOperationGuidanceMap = orOperationGuidance.stream().collect(Collectors.toMap(OperationGuidance::getInstanceId, Function.identity()));

        Map<Long, String> mUpdate = new HashMap<>();
        Map<String, String> mCreate = new HashMap<>();
        List<Long> orgUpdateIds = new ArrayList<>();
        final int[] flag = {1};
        List<OperationGuidance> list = data.stream().map(e -> {
            e.setDescription(Optional.ofNullable(e.getDescription()).orElse(""));
            OperationGuidance operationGuidance = new OperationGuidance();
            operationGuidance.setName(e.getName());
            operationGuidance.setDescription(e.getDescription());
            operationGuidance.setTypeCode("2");
            operationGuidance.setUrl(e.getUrl());
            operationGuidance.setFormat("");
            operationGuidance.setUploadFileName("");
            if (StringUtils.isNotBlank(e.getInstanceId())) {
                OperationGuidance og = orOperationGuidanceMap.get(Long.parseLong(e.getInstanceId()));
                if (og != null) {
                    update.add(String.valueOf(og.getId()));
                    operationGuidance.setId(og.getId());
                    orgUpdateIds.add(og.getId());
                    if (og.getNameLangId() != null) {
                        mUpdate.put(og.getNameLangId(), e.getName());
                    } else {
                        mCreate.put(flag[0] + "", e.getName());
                        operationGuidance.setName(flag[0] + "");
                        flag[0]++;
                    }
                    if (og.getDescriptionLangId() != null) {
                        mUpdate.put(og.getDescriptionLangId(), e.getDescription());
                    } else {
                        if (StringUtils.isNotBlank(e.getDescription())) {
                            mCreate.put(flag[0] + "", e.getDescription());
                            operationGuidance.setDescription(flag[0] + "");
                            flag[0]++;
                        }
                    }
                } else {
                    operationGuidance.setInstanceId(SnowFlake.nextId());
                    mCreate.put(flag[0] + "", e.getName());
                    operationGuidance.setName(flag[0] + "");
                    flag[0]++;
                    if (StringUtils.isNotBlank(e.getDescription())) {
                        mCreate.put(flag[0] + "", e.getDescription());
                        operationGuidance.setDescription(flag[0] + "");
                        flag[0]++;
                    }
                }
            } else {
                operationGuidance.setInstanceId(SnowFlake.nextId());
                mCreate.put(flag[0] + "", e.getName());
                operationGuidance.setName(flag[0] + "");
                flag[0]++;
                if (StringUtils.isNotBlank(e.getDescription())) {
                    mCreate.put(flag[0] + "", e.getDescription());
                    operationGuidance.setDescription(flag[0] + "");
                    flag[0]++;
                }
            }
            return operationGuidance;
        }).collect(Collectors.toList());
        if (mUpdate.size() > 0) {
            remoteMultiLanguageService.simpleUpdateMultiLanguages(ApplicationEnum.OPERATION_PLATFORM.getName(), mUpdate, LocaleContextHolder.getLocale().getLanguage());
        }
        if (mCreate.size() > 0) {
            Map<String, MultiLanguageBo> boMap = remoteMultiLanguageService.simpleCreateMultiLanguages(ApplicationEnum.OPERATION_PLATFORM.getName(), mCreate, LocaleContextHolder.getLocale().getLanguage());
            list.forEach(e -> {
                if (e.getNameLangId() == null) {
                    MultiLanguageBo bo = boMap.get(e.getName());
                    e.setNameLangId(bo.getLangId());
                    e.setNameLangCode(bo.getLangCode());
                    e.setName(mCreate.get(e.getName()));
                }
                if (e.getDescriptionLangId() == null && StringUtils.isNotBlank(e.getDescription())) {
                    MultiLanguageBo bo = boMap.get(e.getDescription());
                    e.setDescriptionLangId(bo.getLangId());
                    e.setDescriptionLangCode(bo.getLangCode());
                    e.setDescription(mCreate.get(e.getDescription()));
                }
            });
        }
        operationGuidanceService.saveOrUpdateBatch(list);
        if (!orgUpdateIds.isEmpty()) {
            operationGuidanceService.update(new OperationGuidance(), new LambdaUpdateWrapper<OperationGuidance>().set(OperationGuidance::getSize, null).in(OperationGuidance::getId, orgUpdateIds));
        }
        List<CommonOperationGuidance> insert = new ArrayList<>();
        list.forEach(e -> {
            if (!update.contains(String.valueOf(e.getId()))) {
                insert.add(new CommonOperationGuidance() {{
                    this.setOperationGuidanceId(e.getId());
                    this.setNameLangId(e.getNameLangId());

                }});
            }
        });
        if (insert.size() > 0) {
            this.saveBatch(insert);
        }
        return res;
    }

    private boolean validImport(List<CommonOperationGuidanceRead> data, List<String> res) {
        List<Integer> idString = new ArrayList<>();
        Map<String, List<Integer>> idMap = new HashMap<>();
        List<Integer> nameNullFlag = new ArrayList<>();
        List<Integer> urlNullFlag = new ArrayList<>();
        List<Integer> urlFlag = new ArrayList<>();
        List<Integer> nameSizeFlag = new ArrayList<>();
        List<Integer> urlSizeFlag = new ArrayList<>();
        List<Integer> descriptionSizeFlag = new ArrayList<>();

        for (int i = 2, j = data.size() + 2; i < j; i++) {
            CommonOperationGuidanceRead read = data.get(i - 2);

            if (StringUtils.isNotBlank(read.getInstanceId())) {
                try {
                    Long.parseLong(read.getInstanceId());
                } catch (Exception e) {
                    idString.add(i);
                }
                if (idMap.get(read.getInstanceId()) == null) {
                    List<Integer> ids = new ArrayList<>();
                    ids.add(i);
                    idMap.put(read.getInstanceId(), ids);
                } else {
                    idMap.get(read.getInstanceId()).add(i);
                }
            }

            if (StringUtils.isBlank(read.getName())) {
                nameNullFlag.add(i);
            } else if (read.getName().length() > TEXT_SIZE) {
                nameSizeFlag.add(i);
            }

            if (StringUtils.isBlank(read.getUrl())) {
                urlNullFlag.add(i);
            } else if (read.getUrl().length() > LINK_SIZE) {
                urlSizeFlag.add(i);
            } else if (!(read.getUrl().startsWith("http://") || read.getUrl().startsWith("https://"))) {
                urlFlag.add(i);
            }

            if (StringUtils.isNotBlank(read.getDescription()) && read.getDescription().length() > TEXT_SIZE) {
                descriptionSizeFlag.add(i);
            }
        }
        // 检测id是否为数字
        if (idString.size() > 0) {
            res.add("第" + idString.stream().map(String::valueOf).collect(Collectors.joining("，")) + "行，【文件ID】非法");
        }
        // 检测id是否唯一
        for (Map.Entry<String, List<Integer>> entry : idMap.entrySet()) {
            List<Integer> value = entry.getValue();
            if (value.size() != 1) {
                res.add("第" + value.stream().map(String::valueOf).collect(Collectors.joining("，")) + "行，【文件ID】重复");
            }
        }
        // 检测文件名称是否必填
        if (nameNullFlag.size() > 0) {
            res.add("第" + nameNullFlag.stream().map(String::valueOf).collect(Collectors.joining("，")) + "行，【文件名称】为空");
        }
        // 检测文件名称长度
        if (nameSizeFlag.size() > 0) {
            res.add("第" + nameSizeFlag.stream().map(String::valueOf).collect(Collectors.joining("，")) + "行，【文件名称】内容超过" + TEXT_SIZE + "个字符");
        }
        // 检测url是否必填
        if (urlNullFlag.size() > 0) {
            res.add("第" + urlNullFlag.stream().map(String::valueOf).collect(Collectors.joining("，")) + "行，【文件地址】为空");
        }
        // 检测url长度
        if (urlSizeFlag.size() > 0) {
            res.add("第" + urlSizeFlag.stream().map(String::valueOf).collect(Collectors.joining("，")) + "行，【文件地址】内容超过" + LINK_SIZE + "个字符");
        }
        // 检测url不是http://或者https://开头
        if (urlFlag.size() > 0) {
            res.add("第" + urlFlag.stream().map(String::valueOf).collect(Collectors.joining("，")) + "行，【文件地址】不是以https://或者http://开头");
        }

        if (!res.isEmpty()) {
            return false;
        }

        List<Long> ids = data.stream().filter(e -> StringUtils.isNotBlank(e.getInstanceId())).map(e -> Long.parseLong(e.getInstanceId())).distinct().collect(Collectors.toList());
        if (!ids.isEmpty()) {
            List<OperationGuidance> operationGuidances = operationGuidanceService.list(new LambdaQueryWrapper<OperationGuidance>().in(OperationGuidance::getInstanceId, ids));
            List<Long> existIds = operationGuidances.stream().map(OperationGuidance::getInstanceId).collect(Collectors.toList());
            for (int i = 2, j = data.size() + 2; i < j; i++) {
                CommonOperationGuidanceRead read = data.get(i - 2);
                if (StringUtils.isBlank(read.getInstanceId())) {
                    continue;
                }
                if (!existIds.contains(Long.parseLong(read.getInstanceId()))) {
                    res.add("第" + i + "行，【文件ID】不存在");
                }
            }
            if (!res.isEmpty()) {
                return false;
            }
            Map<Long, Long> instanceIdIdMap = operationGuidances.stream().collect(Collectors.toMap(OperationGuidance::getInstanceId, BaseDo::getId));
            List<CommonOperationGuidance> commonOperationGuidances = this.list(new LambdaQueryWrapper<CommonOperationGuidance>().in(CommonOperationGuidance::getOperationGuidanceId, instanceIdIdMap.values()));
            Map<Long, CommonOperationGuidance> commonOperationGuidanceMap = commonOperationGuidances.stream().collect(Collectors.toMap(CommonOperationGuidance::getOperationGuidanceId, Function.identity()));
            for (int i = 2, j = data.size() + 2; i < j; i++) {
                CommonOperationGuidanceRead read = data.get(i - 2);
                if (StringUtils.isBlank(read.getInstanceId())) {
                    continue;
                }
                CommonOperationGuidance commonOperationGuidance = commonOperationGuidanceMap.get(instanceIdIdMap.get(Long.parseLong(read.getInstanceId())));
                if (commonOperationGuidance == null) {
                    res.add("第" + i + "行，【问题ID】不存在");
                }
            }
        }
        return res.isEmpty();
    }

    @Override
    public List<CommonOperationGuidanceExcel> listData(CommonOperationGuidanceManagePageDto req) {
        List<CommonOperationGuidanceExcel> res = new ArrayList<>();
        List<Long> nameLangIds = findNameLangIds(req.getName());
        // 如果没有匹配的多语言id，则返回空
        if (StringUtils.isNotBlank(req.getName()) && CollectionUtils.isEmpty(nameLangIds)) {
            return res;
        }
        List<CommonOperationGuidance> list = this.getBaseMapper().selectManageList(req, nameLangIds);
        if (CollectionUtils.isEmpty(list)) {
            return res;
        }

        List<OperationGuidance> operationGuidanceList = operationGuidanceService.listByIds(list.stream().map(CommonOperationGuidance::getOperationGuidanceId).filter(Objects::nonNull).distinct().collect(Collectors.toList()));
        Map<Long, OperationGuidance> map = operationGuidanceList.stream().collect(Collectors.toMap(BaseDo::getId, Function.identity()));
        return list.stream().map(e -> {
            CommonOperationGuidanceExcel vo = new CommonOperationGuidanceExcel();
            OperationGuidance guidance = map.getOrDefault(e.getOperationGuidanceId(), new OperationGuidance());
            BeanUtils.copyProperties(guidance, vo);
            vo.setName(MultiLanguageUtil.getByLangCode(Optional.ofNullable(guidance.getNameLangCode()).orElse(""), LocaleContextHolder.getLocale().getLanguage()));
            vo.setDescription(com.chervon.operation.config.MultiLanguageUtil.getByLangCode(Optional.ofNullable(guidance.getDescriptionLangCode()).orElse(""), LocaleContextHolder.getLocale().getLanguage()));
            vo.setCreateTime(DateTimeZoneUtil.format(e.getCreateTime(), req.getZone()));
            vo.setUpdateTime(DateTimeZoneUtil.format(e.getUpdateTime(), req.getZone()));
            // 创建人和修改人
            vo.setCreateBy(e.getCreateBy());
            vo.setUpdateBy(e.getUpdateBy());

            // 设置体积
            if (guidance.getSize() != null) {
                if (guidance.getSize() < 1024) {
                    vo.setSize(guidance.getSize() + "B");
                } else {
                    vo.setSize(new BigDecimal(String.valueOf(guidance.getSize())).divide(BigDecimal.valueOf(1024), 0, RoundingMode.HALF_UP) + "KB");
                }
            }
            return vo;
        }).collect(Collectors.toList());
    }

    @Override
    public List<Long> productIdList(Long commonOperationGuidanceId) {
        if (commonOperationGuidanceId == null) {
            throw ExceptionMessageUtil.getException(OperationErrorCode.OPERATION_COMMON_OPERATION_GUIDANCE_ID_NULL);
        }
        CommonOperationGuidance common = this.getById(commonOperationGuidanceId);
        if (common == null) {
            throw ExceptionMessageUtil.getException(OperationErrorCode.OPERATION_COMMON_OPERATION_GUIDANCE_NULL);
        }
        return productOperationGuidanceService.listProductIdByCommonId(commonOperationGuidanceId);
    }
}
