package com.chervon.operation.rpc;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.chervon.common.core.constant.CommonConstant;
import com.chervon.common.core.domain.MultiLanguageVo;
import com.chervon.common.core.domain.PageResult;
import com.chervon.common.core.utils.ConvertUtil;
import com.chervon.configuration.api.core.MultiLanguageBo;
import com.chervon.configuration.api.service.RemoteMultiLanguageService;
import com.chervon.operation.api.RemoteMessageTemplateService;
import com.chervon.operation.api.dto.MessageTemplateDto;
import com.chervon.operation.api.dto.MessageTemplateIdDto;
import com.chervon.operation.api.dto.MessageTemplateListDto;
import com.chervon.operation.api.vo.*;
import com.chervon.operation.domain.dataobject.MessageTemplate;
import com.chervon.operation.service.MessageTemplateService;
import jodd.util.StringUtil;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2022-09-07 20:04
 **/
@DubboService
@Slf4j
@AllArgsConstructor
public class RemoteMessageTemplateServiceImpl implements RemoteMessageTemplateService {
    private final MessageTemplateService messageTemplateService;

    @DubboReference
    private RemoteMultiLanguageService remoteMultiLanguageService;

    /**
     * 获取多语言Vo
     *
     * @param id 多语言langId
     * @return 多语言Vo
     */
    public MultiLanguageVo getMultiLanguageVo(String lang, String id) {
        MultiLanguageBo multiLanguageBo = remoteMultiLanguageService.getById(id);
        return new MultiLanguageVo(multiLanguageBo.getLangId(),
            com.chervon.operation.config.MultiLanguageUtil.getByLangCode(multiLanguageBo.getLangCode(), lang));
    }

    @Override
    @Deprecated
    public List<MessageTemplateListByTypeVo> listByType(Integer type) {
        LambdaQueryWrapper<MessageTemplate> wrapper = new LambdaQueryWrapper<MessageTemplate>()
            .eq(MessageTemplate::getType, type)
            .select(MessageTemplate::getId, MessageTemplate::getTitle);
        List<MessageTemplate> messageTemplates = messageTemplateService.list(wrapper);
        if (messageTemplates.isEmpty()) {
            return null;
        }
        List<MessageTemplateListByTypeVo> result = new ArrayList<>();
        for (MessageTemplate messageTemplate : messageTemplates) {
            MessageTemplateListByTypeVo messageTemplateListByTypeVo = new MessageTemplateListByTypeVo();
            messageTemplateListByTypeVo.setId(messageTemplate.getId());
            // 获取模板标题多语言
            messageTemplateListByTypeVo.setTitle(getMultiLanguageVo(LocaleContextHolder.getLocale().getLanguage(), messageTemplate.getTitle().toString()));
            // 获取模板内容多语言
            messageTemplateListByTypeVo.setContent(getMultiLanguageVo(LocaleContextHolder.getLocale().getLanguage(), messageTemplate.getContent().toString()));
            result.add(messageTemplateListByTypeVo);
        }
        return result;
    }

    @Override
    public List<MessageTemplateBo> list(MessageTemplateDto messageTemplateDto) {
        LambdaQueryWrapper<MessageTemplate> wrapper = new LambdaQueryWrapper<MessageTemplate>();
        wrapper.eq(null != messageTemplateDto.getId(), MessageTemplate::getId, messageTemplateDto.getId());
        wrapper.eq(null != messageTemplateDto.getType(), MessageTemplate::getType, messageTemplateDto.getType());
        wrapper.eq(MessageTemplate::getIsDeleted, 0);
        if (StringUtil.isNotEmpty(messageTemplateDto.getTitle())) {
            LambdaQueryWrapper<MessageTemplate> wrapperMessageTemplate = new LambdaQueryWrapper<>();
            wrapperMessageTemplate.eq(MessageTemplate::getIsDeleted, 0);
            List<MessageTemplate> messageTemplateList = messageTemplateService.list(wrapperMessageTemplate);
            List<Long> ids = messageTemplateList.stream().map(MessageTemplate::getTitle)
                .collect(Collectors.toList());
            Map<String, List<Long>> query = new HashMap<>();
            query.put(messageTemplateDto.getTitle(), ids);
            Map<String, List<MultiLanguageBo>> listByText = remoteMultiLanguageService.listByTextLike(query, "zh");
            List<MultiLanguageBo> langIdlist = listByText.get(messageTemplateDto.getTitle());
            List<Long> langIds = langIdlist.stream().map(MultiLanguageBo::getLangId).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(langIds)) {
                wrapper.in(MessageTemplate::getTitle, langIds);
            } else {
                return new ArrayList<MessageTemplateBo>();
            }
        }
        List<MessageTemplate> messageTemplateList = messageTemplateService.list(wrapper);
        List<MessageTemplateBo> result = new ArrayList<>();
        for (MessageTemplate messageTemplate : messageTemplateList) {
            MessageTemplateBo messageTemplateBo = ConvertUtil.convert(messageTemplate, MessageTemplateBo.class);
            // 获取模板名称多语言
            messageTemplateBo.setName(getMultiLanguageVo(LocaleContextHolder.getLocale().getLanguage(), messageTemplate.getName().toString()));
            // 获取模板标题多语言
            messageTemplateBo.setTitle(getMultiLanguageVo(LocaleContextHolder.getLocale().getLanguage(), messageTemplate.getTitle().toString()));
            // 获取模板内容多语言
            messageTemplateBo.setContent(getMultiLanguageVo(LocaleContextHolder.getLocale().getLanguage(), messageTemplate.getContent().toString()));
            result.add(messageTemplateBo);
        }
        return result;
    }

    @Override
    public MessageTemplateBo get(String lang, Long id) {
        MessageTemplate messageTemplate = messageTemplateService.getById(id);
        if (null == messageTemplate) {
            return null;
        }
        MessageTemplateBo result = ConvertUtil.convert(messageTemplate, MessageTemplateBo.class);
        // 获取模板名称多语言
        result.setName(getMultiLanguageVo(lang, messageTemplate.getName().toString()));
        // 获取模板标题多语言
        result.setTitle(getMultiLanguageVo(lang, messageTemplate.getTitle().toString()));
        // 获取模板内容多语言
        result.setContent(getMultiLanguageVo(lang, messageTemplate.getContent().toString()));
        result.setMessageDisplayType(messageTemplate.getMessageDisplayType());
        return result;
    }

    @Override
    public List<RemoteSpinnerVo> listSpinner(String lang) {
        LambdaQueryWrapper<MessageTemplate> lambdaQueryWrapper = new LambdaQueryWrapper<MessageTemplate>()
            .select(MessageTemplate::getId, MessageTemplate::getTitle);
        List<MessageTemplate> messageTemplateList = messageTemplateService.list(lambdaQueryWrapper);
        List<RemoteSpinnerVo> result = new ArrayList<>(messageTemplateList.size());
        messageTemplateList.forEach(messageTemplate -> {
            RemoteSpinnerVo remoteSpinnerVo = new RemoteSpinnerVo();
            remoteSpinnerVo.setId(messageTemplate.getId());
            MultiLanguageVo multiLanguageVo = getMultiLanguageVo(lang, messageTemplate.getTitle().toString());
            remoteSpinnerVo.setTitle(multiLanguageVo.getMessage());
            result.add(remoteSpinnerVo);
        });
        return result;
    }

    @Override
    public PageResult<MessageTemplateListVo> page(String lang, MessageTemplateListDto messageTemplateListDto) {
        LocaleContextHolder.setLocale(new Locale(lang));
        return messageTemplateService.page(messageTemplateListDto);
    }

    @Override
    public MessageTemplateDetailVo detail(String lang, MessageTemplateIdDto messageTemplateIdDto) {
        LocaleContextHolder.setLocale(new Locale(lang));
        return messageTemplateService.detail(messageTemplateIdDto);
    }

    @Override
    public Map<String, Object> fetchTitleInfo(Long templateId) {
        Map<String, Object> res = new HashMap<>();
        if (templateId == null) {
            return res;
        }
        MessageTemplate one = messageTemplateService.getById(templateId);
        if (one != null && one.getTitle() != null) {
            res.put("titleLangId", one.getTitle());
            res.put("titleLangCode", remoteMultiLanguageService.getById(one.getTitle() + "").getLangCode());
        }
        return res;
    }

    @Override
    public Boolean check(Long messageTemplateId) {
        LambdaQueryWrapper<MessageTemplate> wrapper = new LambdaQueryWrapper<MessageTemplate>()
            .eq(MessageTemplate::getId, messageTemplateId).last("limit 1");
        return messageTemplateService.count(wrapper) > CommonConstant.ZERO;
    }

    @Override
    public Map<Long, Map<String, String>> listTitleForProductFault(Set<Long> messageTemplateIds) {
        List<MessageTemplate> messageTemplates = messageTemplateService.list(new LambdaQueryWrapper<MessageTemplate>()
            .in(MessageTemplate::getId, messageTemplateIds));
        List<String> titleLangIds = messageTemplates.stream().map(m -> m.getTitle().toString()).collect(Collectors.toList());
        log.debug("titleLangIds.size(): {}", titleLangIds.size());
        //Map<langId,Map<language,content>>：  多语言id value-(key-语种 value-多语言内容)
        Map<String, Map<String, String>> langMapMap = remoteMultiLanguageService.batchGetLanguageContentByListLang(titleLangIds,Arrays.asList("en", "de", "es", "nl", "fr"));
        // key-消息模板ID, value-(key-多语言类型 value-多语言内容)
        Map<Long, Map<String, String>> result = new HashMap<>();
        messageTemplates.forEach(messageTemplate -> result.put(messageTemplate.getId(),
            langMapMap.getOrDefault(messageTemplate.getTitle().toString(), new HashMap<>())));
        return result;
    }

    @Override
    public Map<Long, Map<String, String>> listContentForProductFault(Set<Long> messageTemplateIds) {
        List<MessageTemplate> messageTemplates = messageTemplateService.list(new LambdaQueryWrapper<MessageTemplate>()
                .in(MessageTemplate::getId, messageTemplateIds));
        List<String> contentLangIds = messageTemplates.stream().map(m -> m.getContent().toString()).collect(Collectors.toList());
        log.debug("ContentLangIds.size(): {}", contentLangIds.size());
        // key-多语言id value-(key-多语言 value-多语言内容)
        Map<String, Map<String, String>> langMapMap = remoteMultiLanguageService.listLanguageContentForAllLanguagesByLangIds(contentLangIds);
        // key-消息模板ID, value-(key-多语言类型 value-多语言内容)
        Map<Long, Map<String, String>> result = new HashMap<>();
        messageTemplates.forEach(messageTemplate -> result.put(messageTemplate.getId(),
                langMapMap.getOrDefault(messageTemplate.getContent().toString(), new HashMap<>())));
        return result;
    }
}
