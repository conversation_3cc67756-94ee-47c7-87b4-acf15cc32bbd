package com.chervon.operation.rpc;

import com.chervon.operation.api.RemoteFleetDealerNaService;
import com.chervon.operation.api.dto.AppDealerSearchDto;
import com.chervon.operation.api.vo.AppDealerNaVo;
import com.chervon.operation.api.vo.AppDealerVo;
import com.chervon.operation.domain.dataobject.DealerNa;
import com.chervon.operation.service.DealerNaService;
import com.chervon.operation.util.PointUtil;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/8/1 11:29
 */
@Service
@Slf4j
@DubboService
@AllArgsConstructor
public class RemoteFleetDealerNaServiceImpl implements RemoteFleetDealerNaService {

    private final DealerNaService dealerNaService;

    @Override
    public List<AppDealerNaVo> search(String lang, AppDealerSearchDto req) {
        Map<String, Double> point = PointUtil.find4Point(req.getLat(), req.getLng(), 30);
        List<AppDealerVo> list = dealerNaService.list(null, req.getLat(), req.getLng(),
                point.get("minLat"), point.get("maxLat"), point.get("minLng"), point.get("maxLng"), false);
        return this.handleAppDealerList(list, req.getContent());
    }

    @Override
    public AppDealerNaVo detail(String lang, Long dealerId) {
        DealerNa dealerNa = dealerNaService.getById(dealerId);
        AppDealerNaVo res = new AppDealerNaVo();
        if (dealerNa == null) {
            return res;
        }
        BeanUtils.copyProperties(dealerNa, res);
        res.setDealerId(dealerNa.getId());
        res.setCategory(Arrays.asList(Optional.ofNullable(dealerNa.getCategory()).orElse("").split(",")));
        return res;
    }

    @Override
    public Integer countByDealerIds(List<Long> dealerIds) {
        if (CollectionUtils.isEmpty(dealerIds)) {
            return 0;
        }
        return dealerNaService.countByIds(dealerIds);
    }

    @Override
    public List<AppDealerNaVo> listByDealerIds(String lang, List<Long> dealerIds, Double lat, Double lng, String searchContent) {
        if (CollectionUtils.isEmpty(dealerIds)) {
            return new ArrayList<>();
        }
        List<AppDealerVo> list = dealerNaService.listWithDistanceByIds(lat, lng, dealerIds);
        return this.handleAppDealerList(list, searchContent);
    }

    private List<AppDealerNaVo> handleAppDealerList(List<AppDealerVo> list, String searchContent) {
        if (CollectionUtils.isEmpty(list)) {
            return new ArrayList<>();
        }
        List<AppDealerNaVo> res = list.stream().map(e -> {
            AppDealerNaVo vo = new AppDealerNaVo();
            BeanUtils.copyProperties(e, vo);
            vo.setCategory(Arrays.asList(Optional.ofNullable(e.getCa()).orElse("").split(",")));
            return vo;
        }).collect(Collectors.toList());
        if (StringUtils.isNotBlank(searchContent)) {
            res.removeIf(e -> {
                boolean name = StringUtils.containsIgnoreCase(e.getName(), searchContent);
                boolean address = StringUtils.containsIgnoreCase(e.getAddress(), searchContent);
                boolean country = StringUtils.containsIgnoreCase(e.getCountry(), searchContent);
                boolean state = StringUtils.containsIgnoreCase(e.getState(), searchContent);
                boolean city = StringUtils.containsIgnoreCase(e.getCity(), searchContent);
                boolean zipCode= StringUtils.containsIgnoreCase(e.getZipcode(), searchContent);
                return !(name || address || country || state || city || zipCode);
            });
        }
        return res;
    }
}
