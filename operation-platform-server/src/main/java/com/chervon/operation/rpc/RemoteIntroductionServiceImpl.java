package com.chervon.operation.rpc;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.chervon.common.core.domain.MultiLanguageVo;
import com.chervon.operation.api.RemoteIntroductionService;
import com.chervon.operation.api.dto.IntroductionTypeDto;
import com.chervon.operation.api.vo.IntroductionVo;
import com.chervon.operation.domain.dataobject.Introduction;
import com.chervon.operation.service.IntroductionService;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.BeanUtils;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2022-09-02 10:19
 **/
@DubboService
@Service
@Slf4j
public class RemoteIntroductionServiceImpl implements RemoteIntroductionService {

    private final IntroductionService introductionService;

    public RemoteIntroductionServiceImpl(IntroductionService introductionService) {
        this.introductionService = introductionService;
    }

    @Override
    public List<IntroductionVo> listIntroductionByType(IntroductionTypeDto req) {
        String lang = LocaleContextHolder.getLocale().getLanguage();
        if (req == null || req.getProductId() == null || req.getType() == null || !Arrays.asList(0, 1, 2).contains(req.getType())) {
            return new ArrayList<>();
        }
        List<Introduction> list = introductionService.list(new LambdaQueryWrapper<Introduction>()
                .eq(Introduction::getProductId, req.getProductId())
                .eq(Introduction::getType, req.getType())
                .orderByAsc(Introduction::getCreateTime)
        );
        String finalLang = lang;
        return list.stream().map(e -> {
            IntroductionVo vo = new IntroductionVo();
            BeanUtils.copyProperties(e, vo);
            vo.setIntroductionId(e.getId());
            vo.setContent(new MultiLanguageVo(e.getContentLangId(),
                    com.chervon.operation.config.MultiLanguageUtil.getByLangCode(e.getContentLangCode(), finalLang)));
            return vo;
        }).collect(Collectors.toList());
    }
}
