package com.chervon.operation.rpc;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.chervon.common.core.error.ErrorCode;
import com.chervon.common.core.exception.ServiceException;
import com.chervon.common.core.utils.StringUtils;
import com.chervon.operation.api.RemoteFleetCategoryService;
import com.chervon.operation.api.query.FleetCategoryQuery;
import com.chervon.operation.api.vo.FleetCategoryListVo;
import com.chervon.operation.api.vo.FleetProductCategoryVo;
import com.chervon.operation.domain.dataobject.FleetCategory;
import com.chervon.operation.domain.dataobject.FleetProductCategory;
import com.chervon.operation.service.FleetCategoryService;
import com.chervon.operation.service.FleetProductCategoryService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2023-08-10 19:32
 **/
@DubboService
@Service
@Slf4j
@AllArgsConstructor
public class RemoteFleetCategoryServiceImpl implements RemoteFleetCategoryService {

    private final FleetCategoryService fleetCategoryService;

    private final FleetProductCategoryService fleetProductCategoryService;

    @Override
    public List<FleetCategoryListVo> list(FleetCategoryQuery query) {
        List<FleetCategory> fleetCategories = fleetCategoryService.list(new LambdaQueryWrapper<FleetCategory>()
                .eq(FleetCategory::getLanguage, query.getLang())
                .eq(StringUtils.isNotBlank(query.getCode()), FleetCategory::getCode, query.getCode())
                .eq(!Objects.isNull(query.getIotFlag()),FleetCategory::getIotFlag, query.getIotFlag())
                .in(!CollectionUtils.isEmpty(query.getCodes()), FleetCategory::getCode, query.getCodes())
                .eq(null != query.getCategoryLevel(), FleetCategory::getLevel, query.getCategoryLevel())
                .eq(StringUtils.isNotBlank(query.getParentCategoryCode()), FleetCategory::getParentCode, query.getParentCategoryCode()));
        return fleetCategories.stream().map(e -> {
            FleetCategoryListVo vo = new FleetCategoryListVo();
            vo.setCategoryLevel(e.getLevel());
            vo.setCode(e.getCode());
            vo.setParentCategoryCode(e.getParentCode());
            vo.setCategoryName(e.getCategoryName());
            vo.setLang(query.getLang());
            return vo;
        }).collect(Collectors.toList());
    }

    @Override
    public List<FleetProductCategoryVo> listProductCategory(List<String> productModels) {
        if (CollectionUtils.isEmpty(productModels)) {
            //注意：此处只查询了productModel，其他字段为null，设备绑定不能走无入参场景
            List<FleetProductCategory> list = fleetProductCategoryService.list(new LambdaQueryWrapper<FleetProductCategory>()
                    .isNotNull(FleetProductCategory::getProductModel)
                    .select(FleetProductCategory::getProductModel,FleetProductCategory::getFirstCategoryCode,FleetProductCategory::getSecondCategoryCode));
            return list.stream().map(a->new FleetProductCategoryVo().setProductModel(a.getProductModel())).collect(Collectors.toList());
        }
        //以下设备绑定逻辑根据productModels查询List<FleetProductCategoryVo>全字段返回；
        return getFleetProductCategoryVosByProductModels(productModels);
    }

    @NotNull
    private List<FleetProductCategoryVo> getFleetProductCategoryVosByProductModels(List<String> productModels) {
        if(CollectionUtils.isEmpty(productModels)){
            throw new ServiceException(ErrorCode.PARAMETER_NOT_PROVIDED,"productModels");
        }
        //以下设备绑定逻辑根据productModels查询List<FleetProductCategoryVo>全字段返回；
        List<FleetProductCategory> list = fleetProductCategoryService.list(new LambdaQueryWrapper<FleetProductCategory>().in(FleetProductCategory::getProductModel, productModels));
        //获取一级分类
        List<String> firstCategoryCodes = list.stream().map(FleetProductCategory::getFirstCategoryCode).collect(Collectors.toList());
        List<FleetCategory> fleetCategories = new ArrayList<>();
        if (!firstCategoryCodes.isEmpty()) {
            fleetCategories = fleetCategoryService.list(new LambdaQueryWrapper<FleetCategory>().in(FleetCategory::getCode, firstCategoryCodes));
        }
        //根据一级分类获取customType
        Map<String, FleetCategory> collect = fleetCategories.stream().collect(Collectors.toMap(FleetCategory::getCode, Function.identity(), (k1, k2) -> k1));
        return list.stream().map(e -> {
            FleetProductCategoryVo vo = new FleetProductCategoryVo();
            vo.setProductModel(e.getProductModel());
            vo.setFirstCategoryCode(e.getFirstCategoryCode());
            vo.setSecondCategoryCode(e.getSecondCategoryCode());
            vo.setCustomType(collect.getOrDefault(e.getFirstCategoryCode(), new FleetCategory()).getCustomType());
            return vo;
        }).collect(Collectors.toList());
    }

    @Override
    public String getFleetSecondCategoryName(String model, String lang) {
        if (StringUtils.isBlank(model)) {
            return null;
        }
        FleetProductCategory fleetProductCategory = fleetProductCategoryService.getOne(new LambdaQueryWrapper<FleetProductCategory>()
                .eq(FleetProductCategory::getProductModel, model));
        if (fleetProductCategory == null || StringUtils.isBlank(fleetProductCategory.getSecondCategoryCode())) {
            return null;
        }
        // 取品类默认描述
        FleetCategory fleetCategory = fleetCategoryService.getOne(new LambdaQueryWrapper<FleetCategory>()
                .eq(FleetCategory::getCode, fleetProductCategory.getSecondCategoryCode())
                .eq(FleetCategory::getLanguage, lang));
        if (fleetCategory == null) {
            return null;
        }
        return fleetCategory.getCategoryName();
    }
}
