package com.chervon.operation.controller;

import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.util.DesensitizedUtil;
import com.chervon.authority.sdk.annotation.Log;
import com.chervon.authority.sdk.enums.BusinessType;
import com.chervon.common.core.domain.LoginSysUser;
import com.chervon.common.core.domain.PageResult;
import com.chervon.common.core.domain.SingleInfoReq;
import com.chervon.common.core.utils.CsvUtil;
import com.chervon.common.core.utils.DateTimeZoneUtil;
import com.chervon.common.core.utils.StringUtils;
import com.chervon.fleet.user.api.entity.dto.CompanyAdminChangeRecordDto;
import com.chervon.fleet.user.api.entity.dto.FleetCompanyPageDto;
import com.chervon.fleet.user.api.entity.vo.AdminChangeRecordVo;
import com.chervon.fleet.user.api.entity.vo.FleetCompanyExcel;
import com.chervon.fleet.user.api.entity.vo.FleetCompanyVo;
import com.chervon.fleet.user.api.service.RemoteFleetUserCenterService;
import com.chervon.operation.api.exception.OperationErrorCode;
import com.chervon.operation.config.ExceptionMessageUtil;
import com.chervon.operation.domain.bo.DictBo;
import com.chervon.operation.domain.bo.DictNodeBo;
import com.chervon.operation.domain.dto.FleetCompanyLogoffDto;
import com.chervon.operation.service.DictService;
import com.chervon.usercenter.api.service.SysUserQueryService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2022-08-15 14:18
 **/
@Slf4j
@Api(tags = "fleet租户管理")
@RestController
@RequestMapping("/fleet/company")
public class FleetCompanyController {

    @DubboReference
    private RemoteFleetUserCenterService remoteFleetUserCenterService;

    @DubboReference
    private SysUserQueryService sysUserQueryService;

    private final static String FLEET_COMPANY_STATE = "fleet_company_state";

    @Autowired
    private DictService dictService;

    @ApiOperation("分页接口")
    @PostMapping("page")
    @Log(businessType = BusinessType.VIEW)
    public PageResult<FleetCompanyVo> page(@RequestBody FleetCompanyPageDto req) {
        PageResult<FleetCompanyVo> res = remoteFleetUserCenterService.companyPage(req);
        if (res != null && !CollectionUtils.isEmpty(res.getList())) {
            res.getList().forEach(e -> {
                if (StringUtils.isNotBlank(e.getAdminEmail())) {
                    e.setAdminEmail(DesensitizedUtil.email(e.getAdminEmail()));
                }
            });
        }
        return res;
    }

    @ApiOperation("详情接口")
    @PostMapping("detail")
    @Log(businessType = BusinessType.VIEW)
    public FleetCompanyVo detail(@RequestBody SingleInfoReq<Long> req) {
        if (req.getReq() == null) {
            return new FleetCompanyVo();
        }
        FleetCompanyVo res = remoteFleetUserCenterService.companyDetail(req.getReq());
        if (res != null && StringUtils.isNotBlank(res.getAdminEmail())) {
            res.setAdminEmail(DesensitizedUtil.email(res.getAdminEmail()));
        }
        return res;
    }

    @ApiOperation("停用")
    @PostMapping("deactivate")
    @Log(businessType = BusinessType.DEACTIVATE)
    public void deactivate(@RequestBody SingleInfoReq<Long> req) {
        if (req.getReq() == null) {
            throw ExceptionMessageUtil.getException(OperationErrorCode.OPERATION_FLEET_COMPANY_ID_NULL);
        }
        remoteFleetUserCenterService.companyDeactivate(req.getReq());
    }

    @ApiOperation("启用")
    @PostMapping("enable")
    @Log(businessType = BusinessType.ENABLE)
    public void enable(@RequestBody SingleInfoReq<Long> req) {
        if (req.getReq() == null) {
            throw ExceptionMessageUtil.getException(OperationErrorCode.OPERATION_FLEET_COMPANY_ID_NULL);
        }
        remoteFleetUserCenterService.companyEnable(req.getReq());
    }

    @ApiOperation("注销")
    @PostMapping("logoff")
    @Log(businessType = BusinessType.LOGOFF)
    public void logoff(@RequestBody FleetCompanyLogoffDto req) {
        if (req == null) {
            req = new FleetCompanyLogoffDto();
        }
        if (StringUtils.isBlank(req.getPassword())) {
            throw ExceptionMessageUtil.getException(OperationErrorCode.OPERATION_FLEET_COMPANY_LOGOFF_PASSWORD_BRAND);
        }
        if (req.getCompanyId() == null) {
            throw ExceptionMessageUtil.getException(OperationErrorCode.OPERATION_FLEET_COMPANY_ID_NULL);
        }
        LoginSysUser loginSysUser = (LoginSysUser) StpUtil.getSession().get(StpUtil.getLoginIdAsString());
        if (!sysUserQueryService.checkAd(loginSysUser.getEmployeeNumber(), req.getPassword())) {
            throw ExceptionMessageUtil.getException(OperationErrorCode.OPERATION_FLEET_COMPANY_LOGOFF_PASSWORD_ERROR);
        }
        remoteFleetUserCenterService.companyLogoff(req.getCompanyId());
    }

    @ApiOperation("管理员变更记录")
    @PostMapping("adminChangeRecord")
    @Log(businessType = BusinessType.VIEW)
    public PageResult<AdminChangeRecordVo> adminChangeRecord(@RequestBody CompanyAdminChangeRecordDto req) {
        PageResult<AdminChangeRecordVo> res = remoteFleetUserCenterService.companyAdminChangeRecord(req);
        if (res != null && !CollectionUtils.isEmpty(res.getList())) {
            res.getList().forEach(e -> {
                if (StringUtils.isNotBlank(e.getAdminEmail())) {
                    e.setAdminEmail(DesensitizedUtil.email(e.getAdminEmail()));
                }
            });
        }
        return res;
    }

    @ApiOperation(value = "导出")
    @PostMapping("export")
    @Log(businessType = BusinessType.EXPORT)
    public void export(@RequestBody FleetCompanyPageDto req, HttpServletResponse response) throws IOException {
        List<FleetCompanyExcel> data = remoteFleetUserCenterService.companyListData(req);
        handleDataLanguage(data, req.getZone());
        CsvUtil.export(FleetCompanyExcel.class, data, "FleetCompany", response);
    }

    private void handleDataLanguage(List<FleetCompanyExcel> data, int zone) {
        if (CollectionUtils.isEmpty(data)) {
            return;
        }
        List<DictBo> dictList = dictService.listByDictName(LocaleContextHolder.getLocale().getLanguage(), Collections.singletonList(FLEET_COMPANY_STATE));
        Map<String, DictBo> collect = dictList.stream().collect(Collectors.toMap(DictBo::getDictName, Function.identity()));
        data.forEach(e -> {
            // 邮箱脱敏
            if (StringUtils.isNotBlank(e.getAdminEmail())) {
                e.setAdminEmail(DesensitizedUtil.email(e.getAdminEmail()));
            }
            // 设置状态
            e.setCompanyState(collect.get(FLEET_COMPANY_STATE).getNodes()
                    .stream()
                    .filter(i -> StringUtils.equals(i.getLabel(), CsvUtil.unFormat(e.getCompanyState())))
                    .findFirst()
                    .orElse(new DictNodeBo())
                    .getDescription());
            // 设置时间
            if (StringUtils.isNotBlank(e.getRegisterTime())) {
                e.setRegisterTime(DateTimeZoneUtil.format(new Date(Long.parseLong(CsvUtil.unFormat(e.getRegisterTime()))), zone));
            }
        });
    }
}
