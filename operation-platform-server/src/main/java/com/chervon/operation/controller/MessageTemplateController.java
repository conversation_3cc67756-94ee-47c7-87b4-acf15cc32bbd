package com.chervon.operation.controller;

import com.chervon.authority.sdk.annotation.Log;
import com.chervon.authority.sdk.enums.BusinessType;
import com.chervon.common.core.domain.PageResult;
import com.chervon.common.core.domain.R;
import com.chervon.operation.domain.dto.message.template.MessageTemplateAddDto;
import com.chervon.operation.domain.dto.message.template.MessageTemplateEditDto;
import com.chervon.operation.api.dto.MessageTemplateIdDto;
import com.chervon.operation.api.dto.MessageTemplateListDto;
import com.chervon.operation.api.vo.MessageTemplateDetailVo;
import com.chervon.operation.api.vo.MessageTemplateListVo;
import com.chervon.operation.service.MessageTemplateService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 消息模板相关接口
 *
 * <AUTHOR>
 * @since 2022-09-07 16:04
 **/
@Api(tags = "消息模板相关接口")
@RestController
@RequestMapping("/message/template")
public class MessageTemplateController {
    @Resource
    private MessageTemplateService messageTemplateService;

    /**
     * 添加消息模板
     *
     * @param messageTemplateAddDto 添加消息模板Dto
     * @return 添加结果
     */
    @ApiOperation("添加消息模板")
    @PostMapping("/add")
    @Log(businessType = BusinessType.INSERT)
    public R<?> add(@RequestBody @Validated MessageTemplateAddDto messageTemplateAddDto) {
        messageTemplateService.add(messageTemplateAddDto);
        return R.ok();
    }

    /**
     * 编辑消息模板
     *
     * @param messageTemplateEditDto 编辑消息模板Dto
     * @return 编辑结果
     */
    @ApiOperation("编辑消息模板")
    @PostMapping("/edit")
    @Log(businessType = BusinessType.EDIT)
    public R<?> edit(@RequestBody @Validated MessageTemplateEditDto messageTemplateEditDto) {
        messageTemplateService.edit(messageTemplateEditDto);
        return R.ok();
    }

    /**
     * 删除消息模板
     *
     * @param messageTemplateIdDto 消息模板Id
     * @return 删除结果
     */
    @ApiOperation("删除消息模板")
    @PostMapping("/delete")
    @Log(businessType = BusinessType.DELETE)
    public R<?> delete(@RequestBody @Validated MessageTemplateIdDto messageTemplateIdDto) {
        messageTemplateService.delete(messageTemplateIdDto);
        return R.ok();
    }

    /**
     * 分页获取消息模板列表
     *
     * @param messageTemplateListDto 搜索条件
     * @return 分页结果
     */
    @ApiOperation("分页获取消息模板列表")
    @PostMapping("/page")
    @Log(businessType = BusinessType.VIEW)
    public R<PageResult<MessageTemplateListVo>> page(@RequestBody @Validated MessageTemplateListDto messageTemplateListDto) {
        return R.ok(messageTemplateService.page(messageTemplateListDto));
    }

    /**
     * 获取消息模板详情
     *
     * @param messageTemplateIdDto 消息模板Id
     * @return 消息模板详情
     */
    @ApiOperation("获取消息模板详情")
    @PostMapping("/detail")
    @Log(businessType = BusinessType.VIEW)
    public R<MessageTemplateDetailVo> detail(@RequestBody @Validated MessageTemplateIdDto messageTemplateIdDto) {
        return R.ok(messageTemplateService.detail(messageTemplateIdDto));
    }
}
