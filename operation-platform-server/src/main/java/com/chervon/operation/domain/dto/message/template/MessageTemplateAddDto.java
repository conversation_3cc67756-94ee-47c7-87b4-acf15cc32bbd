package com.chervon.operation.domain.dto.message.template;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 消息模板添加Dto
 *
 * <AUTHOR>
 * @since 2022-09-07 16:07
 **/
@Data
public class MessageTemplateAddDto implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 模板名称
     */
    @ApiModelProperty("模板名称")
    @NotEmpty(message = "模板名称不能为空")
    private String name;
    /**
     * 模板标题
     */
    @ApiModelProperty("模板标题")
    @NotEmpty(message = "模板标题不能为空")
    private String title;
    /**
     * 模板内容
     */
    @ApiModelProperty("模板内容")
    @NotEmpty(message = "模板内容不能为空")
    private String content;
    /**
     * 模板类型：0 设备消息
     */
    @ApiModelProperty("模板类型：0 设备消息")
    @NotNull
    private Integer type;
    /**
     * 消息展示类型： 1 text，2 voice
     */
    private Integer messageDisplayType;
}
