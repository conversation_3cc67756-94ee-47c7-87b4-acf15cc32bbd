package com.chervon.operation.domain.dto.message.template;

import com.chervon.common.core.domain.MultiLanguageVo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @since 2022-09-07 16:21
 **/
@Data
public class MessageTemplateEditDto implements Serializable {
    /**
     * 模板Id
     */
    @ApiModelProperty("模板Id")
    private Long id;
    /**
     * 模板名称
     */
    @ApiModelProperty("模板名称")
    @NotNull
    private MultiLanguageVo name;
    /**
     * 模板标题
     */
    @ApiModelProperty("模板标题")
    @NotNull
    private MultiLanguageVo title;
    /**
     * 模板内容
     */
    @ApiModelProperty("模板内容")
    @NotNull
    private MultiLanguageVo content;
    /**
     * 模板类型：0 设备消息
     */
    @ApiModelProperty("模板类型：0 设备消息")
    @NotNull
    private Integer type;
    /**
     * 消息展示类型： 1 text，2 voice
     */
    private Integer messageDisplayType;
}
