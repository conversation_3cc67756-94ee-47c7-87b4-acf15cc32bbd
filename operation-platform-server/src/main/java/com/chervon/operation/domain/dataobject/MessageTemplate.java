package com.chervon.operation.domain.dataobject;

import com.chervon.common.mybatis.config.BaseDo;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 消息模板
 *
 * <AUTHOR>
 * @since 2022-09-07 14:07
 **/
@EqualsAndHashCode(callSuper = true)
@Data
public class MessageTemplate extends BaseDo {
    /**
     * 模板名称(多语言Id)
     */
    private Long name;
    /**
     * 模板标题(多语言Id)
     */
    private Long title;
    /**
     * 模板内容(多语言Id)
     */
    private Long content;
    /**
     * 模板类型：0设备消息
     * 维护在字典，目前只有设备消息
     */
    private Integer type;
    /**
     * 消息展示类型： 1 text，2 voice
     */
    private Integer messageDisplayType;
}
