package com.chervon.iot.middle.api.service;

import com.chervon.iot.middle.api.dto.query.TimeQueryDto;
import com.chervon.iot.middle.api.dto.query.UsageShadowQuery;
import com.chervon.iot.middle.api.pojo.PowerLoadVo;
import com.chervon.iot.middle.api.pojo.SnowBlowerUsageVo;
import com.chervon.iot.middle.api.pojo.usage.UsageKeyValue;

import java.util.List;

/**
 * 设备使用情况
 * <AUTHOR>
 * @date 2023/11/29 19:40
 */
public interface RemoteIotDataUsageService {

    /**
     * 查询72002设备上次最近使用完成的记录
     * @param deviceId: 设备id
     **/
    SnowBlowerUsageVo selectIotLastHourUsageData(String deviceId);

    /**
     * 查询72002设备最后一小时时间段内的负载数据集合
     * @param query
     */
    PowerLoadVo selectLastHourAvgWorkLoad(UsageShadowQuery query);

    /**
     * 获取设备三个月内平均功耗
     * @param query
     * @return
     */
    Integer getHistoryAvgPowerConsumed(UsageShadowQuery query);

    /**
     * 查询设备使用轨迹记录
     * @param query1: 查询条件1
     * @param query2: 查询条件2
     * @return Map<String, Object> 返回查询结果Map
     */
    Object getUsageTrackHistory(UsageShadowQuery query1, UsageShadowQuery query2);


    /**
     * 汇聚设备天粒度工况记录
     * @param query: 查询条件
     * @return UsageKeyValue 返回查询结果
     */
    List<UsageKeyValue> getDayHistory(UsageShadowQuery query, List<String> timeQueryList);


    /**
     * 汇聚设备周、月粒度工况记录
     * @param query: 查询条件
     * @param timeQueryList: 查询时间列表
     * @return long 返回查询结果
     */
    List<UsageKeyValue> getWMHistory(UsageShadowQuery query, List<TimeQueryDto> timeQueryList);


    /**
     * 汇聚设备历史总工况记录
     * @param query: 查询条件
     * @return long 返回查询结果
     */
    Long getTotalHistory(UsageShadowQuery query);

    /**
     * 设备最新上报数据时间
     * @param deviceId 设备ID
     * @return long 返回查询结果
     */
    Long getLastSyncedTime(String deviceId);

}
