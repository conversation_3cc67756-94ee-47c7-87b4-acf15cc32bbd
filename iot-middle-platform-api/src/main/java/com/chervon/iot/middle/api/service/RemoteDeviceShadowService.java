package com.chervon.iot.middle.api.service;

import com.chervon.common.core.domain.PageRequest;
import com.chervon.common.core.domain.PageResult;
import com.chervon.iot.middle.api.dto.device.IotDeviceShadowBatchDto;
import com.chervon.iot.middle.api.dto.device.IotDeviceShadowDto;
import com.chervon.iot.middle.api.dto.device.IotReportShadowDto;
import com.chervon.iot.middle.api.dto.log.ShadowLogPageDto;
import com.chervon.iot.middle.api.enums.ThingModelType;
import com.chervon.iot.middle.api.vo.device.IotDeviceServiceVo;
import com.chervon.iot.middle.api.vo.log.DeviceShadowLogVo;
import com.chervon.iot.middle.api.vo.shadow.IotDeviceShadowItemVo;
import com.chervon.iot.middle.api.dto.device.IotPublishDto;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 设备影子 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-07-11
 */
public interface RemoteDeviceShadowService {

    /**
     * 发送自定义topic
     * <AUTHOR>
     * @date 13:35 2022/5/20
     * @param iotPublishDto:
     * @return: java.lang.Boolean
     **/
    Boolean publish(IotPublishDto iotPublishDto);

    /**
     * 更新单个设备属性、事件、服务
     * <AUTHOR>
     * @date 15:56 2022/5/11
     * @param iotDeviceShadowDto:
     * @return: java.lang.Boolean
     **/
    Object updateShadowItem(IotDeviceShadowDto iotDeviceShadowDto);

    /**
     * 批量更新设备影子上报与下发
     *
     * <AUTHOR>
     * @date 15:56 2022/5/11
     * @return: java.lang.Boolean
     **/
    void updateDesired(IotDeviceShadowBatchDto iotDeviceShadowBatchDto);

    /**
     * 更新命名影子上报值
     * <AUTHOR>
     * @date 15:56 2022/5/11
     * @param iotReportShadowDto:
     * @return: java.lang.Boolean
     **/
    void updateNamedShadowItems(IotReportShadowDto iotReportShadowDto);

    /**
     * 更新命名影子上报值
     * <AUTHOR>
     * @date 15:56 2022/5/11
     * @param iotReportShadowDto:
     * @return: java.lang.Boolean
     **/
    void updateNamedShadowItems(List<IotReportShadowDto> iotReportShadowDto);

    /**
     * 获取单个设备属性、事件、服务
     * <AUTHOR>
     * @date 15:27 2022/5/11
     * @param deviceId: 设备标识
     * @param identifier: 属性标识
     * @return: java.lang.Object
     **/
    Object getShadowItem(String deviceId, String identifier);

    /**
     * 获取单个设备属性、事件、服务
     * <AUTHOR>
     * @date 15:27 2022/5/11
     * @param deviceId: 设备标识
     * @param identifier: 属性标识
     * @param param: 属性参数
     * @return: java.lang.Object
     **/
    Object getShadowItem(String deviceId, String identifier, String param);

    /**
     * 获取属性、事件、服务列表
     * <AUTHOR>
     * @date 15:57 2022/5/11
     * @param deviceId: 设备标识
     * @param propertyKeys: 属性key列表
     * @return: java.util.Map<java.lang.String,java.lang.Object>
     **/
    Map<String, Object> listShadowItems(String deviceId, List<String> propertyKeys);

    /**
     * 获取所有属性列表
     * <AUTHOR>
     * @date 15:57 2022/5/11
     * @param deviceId: 设备标识
     * @param type: 物模型类型 为null时不区分类型
     * @return: java.util.Map<java.lang.String,java.lang.Object>
     **/
    Map<String, Object> listAllShadowItems(String deviceId, ThingModelType type);

    /**
     * 分页获取带有物模型定义的设备属性列表
     * <AUTHOR>
     * @date 10:18 2022/7/18
     * @param pageRequest:
     * @param deviceId:
     * @param type:
     @return com.chervon.iot.middle.api.vo.page.PageResult
     **/
    PageResult<IotDeviceShadowItemVo> pageItemsWithThingModel(PageRequest pageRequest,
        String deviceId, ThingModelType type);

    /**
     * 获取单个设备事件
     * <AUTHOR>
     * @date 15:27 2022/5/11
     * @param deviceId: 设备标识
     * @param eventKey: 事件标识
     * @return: java.lang.Object
     **/
    Object getEvent(String deviceId, String eventKey);

    /**
     * 获取事件列表
     * <AUTHOR>
     * @date 15:57 2022/5/11
     * @param deviceId: 设备标识
     * @param eventKeys: 事件key列表
     * @return: java.util.Map<java.lang.String,java.lang.Object>
     **/
    Map<String, Object> listEvent(String deviceId, List<String> eventKeys);

    /**
     * 获取所有事件列表
     * <AUTHOR>
     * @date 15:57 2022/5/11
     * @param deviceId: 设备标识
     * @return: java.util.Map<java.lang.String,java.lang.Object>
     **/
    Map<String, Object> listAllEvent(String deviceId);

    /**
     * 执行服务
     * <AUTHOR>
     * @date 11:58 2022/5/13
     * @param iotDeviceServiceVo:
     * @return: java.lang.Boolean
     **/
    Boolean executeService(IotDeviceServiceVo iotDeviceServiceVo);

    /**
     * 获取设备在线状态
     * <AUTHOR>
     * @date 16:12 2022/5/11
     * @param deviceId:
     * @return: java.lang.Boolean
     **/
    Boolean getOnlineStatus(String deviceId);

    /**
     * 获取设备最后上线时间
     * <AUTHOR>
     * @date 16:12 2022/5/11
     * @param deviceId:
     * @return: Date
     **/
    Date getLastOnlineTime(String deviceId);

    /**
     * 分页获取设备属性历史
     * <AUTHOR>
     * @date 16:50 2022/7/21
     * @param shadowLogPageDto: 按时间分组，identifiers必须为同时上报
     * @return com.chervon.common.core.domain.PageResult
     * <com.chervon.iot.middle.api.vo.log.DeviceShadowLogVo>
     **/
    PageResult<DeviceShadowLogVo> pageDeviceShadowLog(ShadowLogPageDto shadowLogPageDto);
    
    /**
     * 删除设备影子
     * <AUTHOR>
     * @date 16:34 2022/8/30
     * @param deviceId:
     * @param shadowName:
     * @return void
     **/
    void removeShadow(String deviceId, String shadowName);
}
