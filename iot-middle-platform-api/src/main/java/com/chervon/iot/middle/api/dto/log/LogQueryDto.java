package com.chervon.iot.middle.api.dto.log;

import com.chervon.common.core.domain.PageRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 设备日志查询dto
 * <AUTHOR>
 */
@ApiModel(description = "设备日志分页请求dto")
@Data
public class LogQueryDto extends PageRequest {


    private static final long serialVersionUID = -7366249712008018231L;

    /**
     * 设备Id
     */
    @ApiModelProperty("设备Id")
    private String deviceId;
    /**
     * 搜索的日志标题
     */
    @ApiModelProperty("查询的日志标题")
    private String title;
    /**
     * 产品id(用于模糊搜索日志标题时定位模板范围,title不为空时必填）
     */
    @ApiModelProperty(value = "产品Id",required = false)
    private Long productId;

    /**
     * 日志模板id
     */
    @ApiModelProperty("日志模板id")
    private Long logTemplateId;

    /**
     * 根据标题匹配到的日志模板id
     */
    @ApiModelProperty(hidden = true,value = "根据标题匹配到的日志模板id", required = false)
    private List<Long> listLogTemplateId;


}
