package com.chervon.iot.middle.api.service;

import com.chervon.iot.middle.api.dto.query.FleetUsageQuery;
import com.chervon.iot.middle.api.vo.usage.FleetBatteryDailyUsageVo;
import com.chervon.iot.middle.api.vo.usage.FleetChargerDailyUsageVo;
import com.chervon.iot.middle.api.vo.usage.FleetToolDailyUsageVo;

import java.util.List;

/**
 * 61001设备使用情况
 * <AUTHOR>
 * @date 2024/2/26 17:40
 */
public interface RemoteFleetDailyUsageService {
    /**
     * 查询FLEET工具设备日使用数据汇总
     * @param query 查询条件
     * @return FleetToolDailyUsageVo
     */
    List<FleetToolDailyUsageVo> getToolDayUsage(List<FleetUsageQuery> query);
    /**
     * 查询FLEET充电器设备日使用数据汇总
     * @param query 查询条件
     * @return FleetChargerDailyUsageVo
     */
    List<FleetChargerDailyUsageVo> getChargerDayUsage(List<FleetUsageQuery> query);
    /**
     * 查询FLEET电池包设备日使用数据汇总
     * @param query 查询条件
     * @return FleetBatteryDailyUsageVo
     */
    List<FleetBatteryDailyUsageVo> getBatteryDayUsage(List<FleetUsageQuery> query);
}
