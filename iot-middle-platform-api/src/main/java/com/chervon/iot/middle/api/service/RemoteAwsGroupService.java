package com.chervon.iot.middle.api.service;

import com.chervon.iot.middle.api.dto.group.AddAwsStaticGroupDto;
import com.chervon.iot.middle.api.dto.device.IotDynamicGroupDto;
import com.chervon.iot.middle.api.dto.device.IotGroupDeviceDto;
import com.chervon.iot.middle.api.dto.group.AddDevice2GroupStaticDto;
import com.chervon.iot.middle.api.vo.device.IotDeviceQueryListVo;

import java.util.List;

/**
 * aws 物品组相关api
 */
public interface RemoteAwsGroupService {
    /**
     * 查询分组下设备
     *
     * @param iotGroupDeviceDto
     * @return
     */
    IotDeviceQueryListVo searchGroupDevice(IotGroupDeviceDto iotGroupDeviceDto);

    /**
     * 新增设备动态分组
     *
     * @param iotDynamicGroupDto:
     * <AUTHOR>
     * @date 15:30 2022/3/1
     * @return: String
     **/
    @Deprecated
    String addDynamicGroup(IotDynamicGroupDto iotDynamicGroupDto);


    /**
     * 新增设备静态分组
     */
    String addStaticGroup(AddAwsStaticGroupDto addAwsStaticGroupDto);

    /**
     * 获取分组的arn
     *
     * @param groupName: 分组名称
     * <AUTHOR>
     * @date 2022年10月10日
     * @return: String
     **/
    String getThingGroupArn(String groupName);

    /**
     * 更新设备动态分组
     *
     * @param iotDynamicGroupDto:
     * <AUTHOR>
     * @date 15:50 2022/3/1
     * @return: void
     **/
    void updateDynamicGroup(IotDynamicGroupDto iotDynamicGroupDto);

    /**
     * 添加设备至静态分组
     *
     */

    void addDevice2StaticGroup(AddDevice2GroupStaticDto addDevice2GroupStaticDto);

    /**
     * 批量添加设备至静态分组
     */
    void batchAddDevice2StaticGroup(String groupName,List<String> deviceIds);

    /**
     * 删除设备动态分组
     *
     * @param groupName:
     * <AUTHOR>
     * @date 16:06 2022/3/1
     * @return: void
     **/
    void deleteDynamicGroup(String groupName);

    /**
     * 删除设备静态分组
     *
     * @param groupName
     */

    void deleteStaticGroup(String groupName);

    /**
     * 根据设备id获取分组名称列表
     *
     * @param deviceId:
     * @return java.util.List<java.lang.String>
     * <AUTHOR>
     * @date 17:30 2022/8/24
     **/
    List<String> listGroupNames(String deviceId);
}
