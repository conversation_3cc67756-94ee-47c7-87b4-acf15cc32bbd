package com.chervon.iot.middle.api.service;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023/4/23 15:40
 */
public interface RemoteIotDataService {

    /**
     * 保存设备上报日志
     *
     * @param deviceId:
     * @param reported:
     * @return void
     * <AUTHOR>
     * @date 18:47 2022/7/19
     **/
    void saveReportedLog(String deviceId, Object reported);

    /**
     * 获取设备最后一次指定列的日志(order by ts desc)
     * @param productKey 产品id
     * @param deviceId 设备id
     * @param columns 指定查询的列值
     * @return 指定列的最后一条数据值
     */
    Map<String,Object> getLatestColumnLog(String productKey, String deviceId, String columns,String condition);
    /**
     * 获取指定时间范围内最后一条数据值（order by idf_1024 desc)
     * @param productKey 产品id
     * @param deviceId 设备id
     * @param columns 指定查询的列值
     * @param condition 过滤条件
     * @return 指定列的最后一条数据值
     */
    Map<String,Object> getLatestDataBy1024(String productKey, String deviceId, String columns,String condition);

    /**
     * 获取挂载的电池包接口
     * @param productKey
     * @param deviceId
     * @return String 电池包设备id
     */
    public String getMountedBattery(String productKey, String deviceId);
}
