package com.chervon.iot.middle.api.dto.device;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @className IotDeviceShadowBatchDto
 * @description
 * @date 2022年11月29日
 */
@Data
public class IotDeviceShadowBatchDto implements Serializable {

	private static final long serialVersionUID = 1L;
	/**
	 * 设备id
	 **/
	private String deviceId;


	/**
	 * 影子数据
	 */
	List<IotDeviceShadowDto> iotDeviceShadowDtoList;

	/**
	 * 指令
	 */
	private String instructions;

	/**
	 * reported: 上报信息 desired：下发指令
	 **/
	private String actionType;

}
