package com.chervon.iot.middle.api.service;

import com.chervon.iot.middle.api.dto.query.TimeQueryDto;
import com.chervon.iot.middle.api.dto.query.UsageShadowQuery;
import com.chervon.iot.middle.api.pojo.usage.UsageKeyValue;
import com.chervon.iot.middle.api.vo.usage.TracksHistoryVo;
import java.util.List;

/**
 * 61001设备使用情况
 * <AUTHOR>
 * @date 2024/2/26 17:40
 */
public interface RemoteIotZTRUsageService {
    /**
     * 获取61001割草机设备使用数据
     * @param query 查询条件
     * @return TracksHistoryVo
     */
    TracksHistoryVo getTracksHistoryVo(UsageShadowQuery query);

    /**
     * 获取61001割草机设备日工作时间历史记录
     * @param query 查询条件
     * @return Map<String, Long>
     */
    List<UsageKeyValue> getWorkingTimeHistory(UsageShadowQuery query);

    /**
     * 获取61001割草机设备日割草面积历史记录
     * @param query 查询条件
     * @return Map<String, Long>
     */
    List<UsageKeyValue> getDayCuttingAreaHistory(UsageShadowQuery query);

    /**
     * 按指定时间间隔汇总统计工作时长
     * @param query 设备使用查询对象
     * @param timeQueryList 时间查询对象
     * @return List<UsageKeyValue>
     */
    List<UsageKeyValue> getSumWorkingTime(UsageShadowQuery query,List<TimeQueryDto> timeQueryList);
    /**
     * 按指定时间间隔汇总统计割草面积
     * @param query 设备使用查询对象
     * @param timeQueryList 时间查询对象
     * @return List<UsageKeyValue>
     */
    List<UsageKeyValue> getSumCuttingArea(UsageShadowQuery query,List<TimeQueryDto> timeQueryList);

    /**
     * 获取61001割草机设备总工作时间
     * @param deviceId  设备ID
     * @return Long
     */
    Long getTotalWorkTime(String deviceId);

    /**
     * 获取61001割草机设备总割草面积
     * @param deviceId 设备ID
     * @return Long
     */
    Long getTotalCuttingArea(String deviceId);
}
