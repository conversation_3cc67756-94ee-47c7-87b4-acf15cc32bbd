package com.chervon.iot.middle.api.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/9/16 14:17
 */
@Data
@ApiModel(description = "iot 物模型值查询对象")
public class IotThingQueryDto implements Serializable {

    @ApiModelProperty("设备id")
    private String deviceId;

    @ApiModelProperty("物模型id集合")
    private List<String> thingIds;
}
