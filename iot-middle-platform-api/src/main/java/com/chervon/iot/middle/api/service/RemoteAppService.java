package com.chervon.iot.middle.api.service;

import com.chervon.iot.middle.api.dto.IotThingQueryDto;
import com.chervon.iot.middle.api.vo.device.IotDeviceCertP12Vo;
import com.chervon.iot.middle.api.vo.device.IotDeviceCertVo;

import java.io.IOException;
import java.security.KeyStoreException;
import java.security.NoSuchAlgorithmException;
import java.security.cert.CertificateException;
import java.util.List;
import java.util.Map;

/**
 * App服务类
 *
 * <AUTHOR>
 * @date 14:56 2022/5/16
 **/
public interface RemoteAppService {

    /**
     * 获取IoT core证书，返回pem格式字符串
     *
     * @return com.chervon.iot.middle.api.vo.device.IotDeviceCertVo
     * <AUTHOR>
     * @date 16:31 2022/7/15
     **/
    IotDeviceCertVo getIotCertPem();


    /**
     * 获取IoT core证书，返回pem格式字符串 从s3
     *
     * @return com.chervon.iot.middle.api.vo.device.IotDeviceCertVo
     * <AUTHOR>
     * @date 16:31 2022/7/15
     **/
    IotDeviceCertVo getIotCertPemFromS3(Integer businessType);


    /**
     * 获取IoT core证书，返回p12格式
     *
     * @return com.chervon.iot.middle.api.vo.device.IotDeviceCertP12Vo
     * <AUTHOR>
     * @date 16:32 2022/7/15
     **/
    IotDeviceCertP12Vo getIotCertP12(Integer businessType)
            throws CertificateException, NoSuchAlgorithmException, KeyStoreException, IOException;

    /**
     * 查询指定设备指定物模型值
     *
     * @param req 查询条件
     * @return 物模型数据
     */
    Map<String, Object> reportedData(IotThingQueryDto req);
}
