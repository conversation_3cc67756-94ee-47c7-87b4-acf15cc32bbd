package com.chervon.iot.middle.api.service;

import com.chervon.common.core.domain.PageResult;
import com.chervon.iot.middle.api.dto.log.DeviceLogParamDto;
import com.chervon.iot.middle.api.dto.log.LogPageDto;
import com.chervon.iot.middle.api.dto.log.TopologyPageDto;
import com.chervon.iot.middle.api.vo.device.DeviceTopologyVo;
import com.chervon.iot.middle.api.vo.log.DeviceLogVo;
import com.chervon.iot.middle.api.vo.log.DeviceRunParamVo;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 设备影子 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-07-11
 */
public interface RemoteDeviceLogService {

    /**
     * 获取设备最近一次的更新日志
     *
     * @param deviceId:                设备id
     * @param identifier:物模型属性、事件、服务id
     * @param isReported:true          上报 false 下发 null 不区分
     * @return org.springframework.boot.configurationprocessor.json.JSONObject
     * <AUTHOR>
     * @date 14:06 2022/7/11
     **/
    Object getLastUpdateLog(String deviceId, String identifier, Boolean isReported);


    /**
     * 获取设备最近一次的更新日志
     *
     * @return org.springframework.boot.configurationprocessor.json.JSONObject
     * <AUTHOR>
     * @date 14:06 2022/7/11
     **/
    Map<String, Integer> getLastLogByIdentifier(DeviceRunParamVo deviceRunParamVo, List<String> identifierList);

    /**
     * 获取最大速度
     *
     * @return org.springframework.boot.configurationprocessor.json.JSONObject
     * <AUTHOR>
     * @date 14:06 2022/7/11
     **/
    Map<String, Integer> getMaxByIdentifier(DeviceRunParamVo deviceRunParamVo, String identifier);

    /**
     * 设备日志列表
     *
     * @param logPageDto
     * @return java.util.List<com.chervon.iot.middle.api.vo.log.DeviceLogVo>
     * <AUTHOR>
     * @date 14:43 2022/7/11
     **/
    PageResult<DeviceLogVo> listDeviceLogs(LogPageDto logPageDto);

    /**
     * 获取设备日志列表
     *
     * @param logPageDto
     * @return java.util.List<com.chervon.iot.middle.api.vo.log.DeviceLogVo>
     * <AUTHOR>
     * @date 14:43 2022/7/11
     **/
    List<DeviceLogVo> getDeviceLogList(DeviceLogParamDto logPageDto);

    /**
     * 分页获取设备日志列表
     *
     * @param logPageDto:
     * @return java.util.List<com.chervon.iot.middle.api.vo.log.DeviceLogVo>
     * <AUTHOR>
     * @date 14:43 2022/7/11
     **/
    PageResult<DeviceLogVo> pageDeviceLogs(LogPageDto logPageDto);

    /**
     * 获取设备日志列表-不分页
     *
     * @param logPageDto:
     * @return java.util.List<com.chervon.iot.middle.api.vo.log.DeviceLogVo>
     * <AUTHOR>
     * @date 14:43 2022/7/11
     **/
    List<DeviceLogVo> getDeviceLogList(LogPageDto logPageDto);

    /**
     * 分页获取拓扑记录
     *
     * @param topologyPageDto:
     * @return com.chervon.iot.middle.api.vo.page.PageResult<com.chervon.iot.middle.api.vo.device.DeviceTopologyVo>
     * <AUTHOR>
     * @date 15:14 2022/7/11
     **/
    PageResult<DeviceTopologyVo> pageDeviceTopology(TopologyPageDto topologyPageDto);
}
