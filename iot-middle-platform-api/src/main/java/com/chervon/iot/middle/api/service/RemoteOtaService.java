package com.chervon.iot.middle.api.service;

import com.chervon.iot.middle.api.pojo.ota.JobDocument;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 升级作业服务
 * </p>
 *
 * <AUTHOR>
 * @since 2022-07-11
 */
public interface RemoteOtaService {

    /**
     * 推送升级作业
     * @param jobId 任务id
     * @param targets 任务目标列表，分组或设备ARN列表
     * @param jobDocument 任务文档
     *
     *
     * 待设备端升级后删除
     */
    @Deprecated
    void pushOtaJob(String jobId, List<String> targets, JobDocument jobDocument);

    /**
     * 发送OTA升级通知
     * @param jobId
     * @param productSnCode
     */
    void publishOtaNotifyMessage(Long jobId, String productSnCode);

    /**
     * 取消升级作业
     * @param jobId
     */
    void cancelOtaJob(String jobId);

    /**
     * 获取总成固件版本信息
     * <AUTHOR>
     * @date 13:56 2022/7/11
     * @param deviceId: 设备id
     * @param componentNumber: 总成零件号
     * @return java.lang.String
     **/
    String getFirmwareVersion(String deviceId, String componentNumber);

    /**
     * 获取所有总成固件版本信息
     * <AUTHOR>
     * @date 13:56 2022/7/11
     * @param deviceId: 设备id
     * @return java.lang.String
     **/
    Map<String, String> getAllFirmwareVersion(String deviceId);

    /**
     * 获取总层零件版本信息Map
     * @param deviceId 设备Id
     * @param componentNumbers 总成零件号列表
     * @return 总成零件号-固件版本号Map
     */
    Map<String, String> getFirmwareVersionMap(String deviceId, List<String> componentNumbers);

    /**
     * 批量停止job
     * <AUTHOR>
     * @date 10:24 2022/10/3
     * @param jobIds:
     * @return void
     **/
    void stopJob(List<Long> jobIds);

}
