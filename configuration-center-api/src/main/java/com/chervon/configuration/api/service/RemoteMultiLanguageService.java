package com.chervon.configuration.api.service;

import com.chervon.configuration.api.core.MultiLanguageRespBo;
import com.chervon.configuration.api.core.StaticMultiLanguageReqDto;
import com.chervon.configuration.api.core.MultiLanguageBo;
import org.springframework.util.CollectionUtils;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.*;

/**
 * <AUTHOR>
 * @date 2022/7/13 9:25
 */
public interface RemoteMultiLanguageService {

    /**
     * 简易批量创建多语言
     *
     * @param applicationName 应用名称
     * @param contents        内容，键值对，输出按照key输出
     * @param language        zh\en\fr\jp
     * @return 多语言信息
     */
    Map<String, MultiLanguageBo> simpleCreateMultiLanguages(String applicationName, Map<String, String> contents, String language);

    /**
     * 创建单个多语言
     *
     * @param applicationName 应用名称
     * @param content         多语言文案
     * @param language        zh\en\fr\jp
     * @return 多语言id
     */
    default MultiLanguageBo simpleCreateMultiLanguage(String applicationName, String content, String language) {
        Map<String, MultiLanguageBo> map = this.simpleCreateMultiLanguages(applicationName, new HashMap<String, String>(16) {{
            put("1", content);
        }}, language);
        return map.getOrDefault("1", new MultiLanguageBo());
    }

    /**
     * 批量获取多语言信息
     * 有redis缓存
     *
     * @param langIds 多语言id
     * @return 多语言信息集合
     */
    List<MultiLanguageBo> listByIds(List<String> langIds);

    /**
     * 根据多语言id获取多语言信息
     *
     * @param langId 多语言id
     * @return 多语言信息
     */
    default MultiLanguageBo getById(String langId) {
        List<MultiLanguageBo> bos = this.listByIds(Collections.singletonList(langId));
        if (CollectionUtils.isEmpty(bos)) {
            return new MultiLanguageBo();
        }
        return bos.get(0);
    }

    /**
     * 批量修改多语言
     *
     * @param applicationName     应用名称
     * @param langIdNewContentMap 多语言id为key  更新内容为value 的map
     * @param language            zh\en\fr\jp
     */
    void simpleUpdateMultiLanguages(String applicationName, Map<Long, String> langIdNewContentMap, String language);

    /**
     * 单个修改多语言
     *
     * @param applicationName 应用名称
     * @param langId          多语言id
     * @param newContent      修改后的内容
     * @param language        zh\en\fr\jp
     */
    default void simpleUpdateMultiLanguage(String applicationName, Long langId, String newContent, String language) {
        simpleUpdateMultiLanguages(applicationName, new HashMap<Long, String>(16) {{
            put(langId, newContent);
        }}, language);
    }

    /**
     * 根据文案查询多语言信息
     *
     * @param query 查询条件，key是查询文案，list是指定多语言id范围列表
     * @param lang  zh\en\fr\jp
     * @param type  类型 equal相等，like匹配，默认like
     * @return key是查询文案，list是多语言信息集合，只返回langId和langCode
     */
    Map<@NotBlank String, @NotNull List<MultiLanguageBo>> listByText(@NotEmpty Map<@NotBlank String, @NotEmpty List<Long>> query, @NotBlank String lang, String type);

    /**
     * 根据文案查询多语言信息--包含
     *
     * @param query 查询条件，key是查询文案，list是指定多语言id范围列表
     * @param lang  zh\en\fr\jp
     * @return key是查询文案，list是多语言信息集合，只返回langId和langCode
     */
    default Map<@NotBlank String, @NotNull List<MultiLanguageBo>> listByTextLike(@NotEmpty Map<@NotBlank String, @NotEmpty List<Long>> query, @NotBlank String lang) {
        return listByText(query, lang, "like");
    }

    /**
     * 根据文案查询多语言信息--相同
     *
     * @param query 查询条件，key是查询文案，list是指定多语言id范围列表
     * @param lang  zh\en\fr\jp
     * @return key是查询文案，list是多语言信息集合，只返回langId和langCode
     */
    default Map<@NotBlank String, @NotNull List<MultiLanguageBo>> listByTextEqual(@NotEmpty Map<@NotBlank String, @NotEmpty List<Long>> query, @NotBlank String lang) {
        return listByText(query, lang, "equal");
    }

    /**
     * 创建富文本多语言
     *
     * @param applicationName 系统code
     * @param content         内容
     * @param language        语言
     * @return 语言内容
     */
    MultiLanguageBo simpleCreateRichMultiLanguage(String applicationName, String content, String language);


    /**
     * 根据code获取多语言文案
     *
     * @param code 多语言code
     * @return 多语言信息
     */
    default String simpleFindMultiLanguageByCode(String code) {
        return simpleFindMultiLanguageByCodes(Collections.singletonList(code), null).get(code);
    }

    default String simpleFindMultiLanguageByCode(String code, String targetLang) {
        return simpleFindMultiLanguageByCodes(Collections.singletonList(code), targetLang).get(code);
    }

    /**
     * 批量根据code获取多语言文案
     *
     * @param codes 多语言code集合
     * @return 多语言信息
     */
    Map<String, String> simpleFindMultiLanguageByCodes(Collection<String> codes, String targetLang);

    default Map<String, String> simpleFindMultiLanguageByCodes(Collection<String> codes) {
        return simpleFindMultiLanguageByCodes(codes, null);
    }

    /**
     * 根据系统code获取系统页面多语言
     *
     * @param sysCode 系统多语言code
     * @return 多语言信息
     */
    default Map<String, String> listLanguageBySysCode(String sysCode) {
        Map<String, String> res = listLanguageBySysCodes(Collections.singletonList(sysCode)).get(sysCode);
        if (res == null) {
            res = new HashMap<>();
        }
        return res;
    }

    /**
     * 根据系统code获取系统页面多语言
     *
     * @param sysCodes 系统多语言code
     * @return 多语言信息
     */
    Map<String, Map<String, String>> listLanguageBySysCodes(Collection<String> sysCodes);

    /**
     * 根据系统code获取系统页面多语言
     *
     * @param sysCodes 系统多语言code
     * @param lang 语言
     * @return 多语言信息
     */
    Map<String, Map<String, String>> listLanguageBySysCodes(Collection<String> sysCodes, String lang);

    /**
     * 根据多语言id列表获取所有存在的语言的内容
     *
     * @param langIds 多语言id列表
     * @return key-多语言id value-(key-多语言 value-多语言内容)
     */
    Map<String, Map<String, String>> listLanguageContentForAllLanguagesByLangIds(Collection<String> langIds);

    /**
     * 根据LangIds和语种集合获取所有多语言信息
     * @param langIds
     * @param listLanguage
     * @return Map<langId,Map<language,content>>
     */
    Map<String, Map<String, String>> batchGetLanguageContentByListLang(List<String> langIds,List<String> listLanguage);

    /**
     * 根据多语言code批量删除
     *
     * @param langCodes 多语言code集合
     */
    void deleteByLangCodes(List<String> langCodes);

    /**
     * 根据多语言id批量删除
     *
     * @param langIds 多语言id集合
     */
    void deleteByLangIds(List<Long> langIds);

    /**
     * APP、RN变更静态多语言查询
     * @date 2024/3/8
     * @param multiLanguageRequestDto 请求参数
     * @return com.chervon.iot.app.domain.vo.multilanguage.MultiLanguageRespBo
     **/
    MultiLanguageRespBo getMultiLanguage(StaticMultiLanguageReqDto multiLanguageRequestDto);


}
