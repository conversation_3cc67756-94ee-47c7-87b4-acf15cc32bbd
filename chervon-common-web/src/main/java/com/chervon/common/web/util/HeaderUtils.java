package com.chervon.common.web.util;

import cn.hutool.core.util.NumberUtil;
import com.chervon.common.core.error.ErrorCode;
import com.chervon.common.core.exception.ServiceException;
import com.chervon.common.web.header.HeaderContext;
import com.chervon.common.web.header.HeaderContextHolder;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.StringUtils;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.util.HashMap;
import java.util.Locale;
import java.util.Map;

@Slf4j
public class HeaderUtils {
    /**
     * 当前用户的登陆token
     */
    public static final String TOKEN = "token";
    /**
     * 语言
     */
    public static final String LANGUAGE = "lang";
    /**
     * 设备类型
     */
    public static final String DEVICE_TYPE = "deviceType";

    /**
     * 当前用户名(登录名)
     */
    public static final String USER_NAME = "userName";
    /**
     * 当前用户姓名(真实姓名)
     */
    public static final String NAME = "name";
    /**
     * 当前用户id
     */
    public static final String USER_ID = "uid";
    /**
     * 租户Id:companyId
     */
    public static final String COMPANY_ID="companyId";
    /**
     * 用户类型
     */
    public static final String USER_TYPE="userType";

    /**
     * 设备id
     */
    public static final String DEVICE_ID = "deviceId";
    /**
     * 日志追踪id
     */
    public static final String TRACE_ID="traceId";
    /**
     * 获取客户端IP的真实地址Header名
     */
    protected static final String[] IP_HEADER_NAME_ARRAY =
            new String[]{"x-forwarded-for", "Proxy-Client-IP", "WL-Proxy-Client-IP", "HTTP_CLIENT_IP", "HTTP_X_FORWARDED_FOR"};

    /**
     * 获取用户真实IP地址，不使用request.getRemoteAddr();的原因是有可能用户使用了代理软件方式避免真实IP地址,
     * <p>
     * 可是，如果通过了多级反向代理的话，X-Forwarded-For的值并不止一个，而是一串IP值，究竟哪个才是真正的用户端的真实IP呢？
     * 答案是取X-Forwarded-For中第一个非unknown的有效IP字符串。
     * 如：X-Forwarded-For：*************, *************, *************, *************
     * 用户真实IP为： *************
     *
     * @param request
     */
    public static String getRequestIp(HttpServletRequest request) {
        String ip = null;
        for (String headerName : IP_HEADER_NAME_ARRAY) {
            ip = request.getHeader(headerName);
            if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
                break;
            }
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getRemoteAddr();
        }
        ip = ip.equals("0:0:0:0:0:0:0:1") ? "127.0.0.1" : ip;
        return ip;
    }

    /**
     * 获取用户ID
     */
    public static Long getUserId() {
        final String strUserId = getHeader(USER_ID, "0");
        if(NumberUtil.isNumber(strUserId)){
            return Long.parseLong(strUserId);
        }
        return 0L;
    }

    /**
     * 获取用户名
     */
    public static String getUserName() {
        return getHeader(USER_NAME,"");
    }
    /**
     * 获取租户Id
     */
    public static Long getCompanyId() {
        final String strTenantId =  getHeader(COMPANY_ID,"");
        if(NumberUtil.isNumber(strTenantId)){
            return Long.parseLong(strTenantId);
        }
        return 0L;
    }
    /**
     * 获取用户真实姓名
     */
    public static String getName() {
        return getHeader(NAME,"");
    }

    /**
     * 获取Token值
     */
    public static String getToken() {
        return getHeader(TOKEN);
    }

    public static String getTraceId(){
        return getHeader(TRACE_ID);
    }

    /**
     * * 获取终端设备id
     * @return
     */
    public static String getDeviceId(){
        return getHeader(DEVICE_ID);
    }

    /**
     * 获取设备类型
     */
    public static Integer getDeviceType() {
        String type = getHeader(DEVICE_TYPE);
        if(NumberUtil.isNumber(type)){
            return Integer.parseInt(type);
        }
        return 0;
    }
    /**
     * 获取用户类型
     */
    public static Integer getUserType() {
        String userType = getHeader(USER_TYPE);
        if(NumberUtil.isNumber(userType)){
            return Integer.parseInt(userType);
        }
        return 0;
    }
    /**
     * 语言码
     */
    public static String getLanguage() {
        return getHeader(LANGUAGE, "en").toLowerCase(Locale.ROOT);
    }

    /**
     * 获取Header的内容
     * @param name
     */
    private static String getHeader(String name, String defaultValue) {
        ServletRequestAttributes requestAttributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        if (null != requestAttributes) {
            HttpServletRequest ctx = requestAttributes.getRequest();
            String value = ctx.getHeader(name);
            if (value==null || value.equals("")) {
                return defaultValue;
            }
            return value;
        }
        return defaultValue;
    }

    /**
     * 获取Header的内容
     *
     * @param name
     */
    public static String getHeader(String name) {
        return getHeader(name, null);
    }

    /**
     * 获取所有的Header
     *
     * @return
     */
    private static Map<String, String> getAllHeader() {
        HeaderContext ctx = HeaderContextHolder.getCtx();
        if (ctx == null) {
            return new HashMap<>(0);
        }
        return ctx.getAllHeader();
    }
}
