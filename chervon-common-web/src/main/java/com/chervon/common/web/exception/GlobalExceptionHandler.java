package com.chervon.common.web.exception;

import com.chervon.common.core.constant.Constants;
import com.chervon.common.core.constant.StringPool;
import com.chervon.common.core.domain.R;
import com.chervon.common.core.error.DefaultError;
import com.chervon.common.core.error.ErrorCode;
import com.chervon.common.core.error.IError;
import com.chervon.common.core.exception.ExceptionUtils;
import com.chervon.common.core.exception.ServiceException;
import com.chervon.common.web.controller.I18nController;
import com.chervon.common.web.entity.I18nErrorDto;
import com.chervon.common.web.util.UserContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.converter.HttpMessageNotReadableException;
import org.springframework.validation.FieldError;
import org.springframework.web.HttpRequestMethodNotSupportedException;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.MissingServletRequestParameterException;
import org.springframework.web.bind.UnsatisfiedServletRequestParameterException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import javax.servlet.http.HttpServletRequest;
import java.lang.reflect.UndeclaredThrowableException;
import java.net.BindException;
import java.util.Objects;
import java.util.Optional;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 默认异常处理
 */
//@ConditionalOnWebApplication(type = ConditionalOnWebApplication.Type.SERVLET)
//@ConditionalOnClass({Servlet.class, DispatcherServlet.class, WebMvcConfigurer.class})
@RestControllerAdvice
@Slf4j
public class GlobalExceptionHandler {

    static {
        ExceptionUtils.registerParameterException(MissingServletRequestParameterException.class);
        ExceptionUtils.registerParameterException(UnsatisfiedServletRequestParameterException.class);
        ExceptionUtils.registerParameterException(BindException.class);
    }

    /**
     * 没有声明的异常
     * @param request 请求
     * @param ex      异常
     * @return 结果
     */
    @ExceptionHandler(value = UndeclaredThrowableException.class)
    public R<Object> undeclaredThrowableHandler(HttpServletRequest request, Exception ex) {
        Exception cause = (Exception) ex.getCause().getCause();
        R<Object> dataResult;
        if (cause instanceof ServiceException) {
            ServiceException openServiceException = (ServiceException) cause;
            dataResult = this.HandleServiceException(request, openServiceException);
        } else {
            dataResult = asDataResult(cause);
        }
        log.error("undeclaredThrowableHandler .", ex);
        return dataResult;
    }

    /**
     * 默认全局异常处理
     *
     * @param request 请求
     * @param ex      异常
     * @return 结果
     */
    @ExceptionHandler(value = Exception.class)
    public R<?> defaultErrorHandler(HttpServletRequest request, Exception ex) {
        R<?> dataResult = asDataResult(ex);
        log.error("defaultErrorHandler .", ex);
        return dataResult;
    }

    /**
     * 参数异常
     *
     * @param request 请求
     * @param ex      异常
     * @return 结果
     */
    @ExceptionHandler(value = {IllegalArgumentException.class, MethodArgumentNotValidException.class})
    public R<?> illegalArgumentException(HttpServletRequest request, Exception ex) {
        R<?> dataResult = asDataResult(ex);
        log.error("illegalArgumentException .", ex);
        return dataResult;
    }

    /**
     * 业务异常处理器
     *
     * @param request
     * @param ex
     * @return
     */
    @ExceptionHandler(value = {ServiceException.class})
    public R<Object> HandleServiceException(HttpServletRequest request, ServiceException ex) {
        Throwable innerEx = ex.getCause();
        R<Object> dataResult = null;
        if (innerEx instanceof UndeclaredThrowableException) {
            dataResult = this.undeclaredThrowableHandler(request, (Exception) innerEx);
        } else {
            dataResult = asDataResult(ex);
        }
        return dataResult;
    }

    /**
     * 不支持的method调用
     *
     * @param ex
     * @return
     */
    @ExceptionHandler(value = {HttpRequestMethodNotSupportedException.class})
    public R<?> notSupportMethod(HttpServletRequest request, HttpRequestMethodNotSupportedException ex) {
        R<?> dataResult = i18NResult(ErrorCode.NOT_SUPPORT_METHOD, new String[]{request.getMethod().toUpperCase()});
        log.error("notSupportMethod .", ex);
        return dataResult;
    }

    /**
     * 处理POST请求入参格式错误
     *
     * @param e 业务异常
     * @return
     */
    @ResponseBody
    @ExceptionHandler(value = {HttpMessageNotReadableException.class})
    public R<?> handleHttpMessageException(HttpMessageNotReadableException e) {
        log.error("处理POST请求入参异常:{}", e.getMessage());
        R<?> dataResult = i18NResult(ErrorCode.PARAMETER_ERROR,"httpRequest");

        return dataResult;
    }

    private static final Pattern ARGUMENT_VALID_REGEX = Pattern.compile("^\\$(\\d+),(\\w+)$");

    /**
     * 异常转换成DataResult
     *
     * @param throwable 异常
     * @return 结果
     */
    public static R<Object> asDataResult(Throwable throwable) {
        // 参数错误
        if (ExceptionUtils.isParameterException(throwable)) {
            log.error(throwable.getMessage(), throwable);
            return i18NResult(ErrorCode.PARAMETER_ERROR);
        }

        if (throwable instanceof MethodArgumentNotValidException) {
            log.error(throwable.getMessage(), throwable);

            MethodArgumentNotValidException e = (MethodArgumentNotValidException) throwable;
            FieldError fieldError = e.getBindingResult().getFieldError();
            String message = null;
            if (fieldError != null) {
                message = fieldError.getDefaultMessage();
            }
            if (message == null) {
                message = "";
            }

            if (message.startsWith("$")) {
                Matcher matcher = ARGUMENT_VALID_REGEX.matcher(message);
                if (matcher.find()) {
                    String code = matcher.group(1);
                    String args = matcher.group(2);
                    ErrorCode error = ErrorCode.getError(code);
                    return i18NResult(error, args);
                }
            }
            return i18NResult(DefaultError.of(ErrorCode.PARAMETER_ERROR.getCode(), message));
        }

        // 用户自定义异常
        ServiceException serviceException = ExceptionUtils.getUserDefinedException(throwable);
        if (serviceException != null) {
            log.error(throwable.getMessage(), throwable);
            return i18NResult(DefaultError.of(serviceException.getCode(), serviceException.getMessage()), serviceException.getParameters());
        }

        //超时异常
        if (ExceptionUtils.isTimeOutException(throwable)) {
            log.error(throwable.getMessage(), throwable);
            return i18NResult(ErrorCode.SERVER_TIME_OUT);
        }

        log.error(throwable.getMessage(), throwable);
        //服务异常
        return i18NResult(DefaultError.of(ErrorCode.INTERNAL_SERVER_ERROR.getCode(),throwable.getMessage()));
    }

    public static <T> R<T> i18NResult(IError error, String... parameters) {
        String language = Optional.ofNullable(UserContext.getClientInfo().getLanguage()).orElse(Constants.DEFAULT_LANGUAGE);
        I18nErrorDto i18NError = I18nController.getI18NError(language, error.getCode());
            if(!Objects.isNull(i18NError)){
                if(ErrorCode.INTERNAL_SERVER_ERROR.getCode().equals(error.getCode())){
                    error.setMessage(i18NError.getReason()+ StringPool.COLON +error.getMessage());
                }
                else{
                    error.setMessage(i18NError.getReason());
                }
            }
        return R.failed(error,parameters);
    }
}
