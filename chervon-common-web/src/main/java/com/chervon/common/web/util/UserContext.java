package com.chervon.common.web.util;

import com.chervon.common.web.entity.ClientInfo;
import org.springframework.util.StringUtils;
import java.util.Objects;

/**
 * 客户端上下文对象缓存
 * <AUTHOR> 2023/6/26
 */
public class UserContext {
    private static ThreadLocal<ClientInfo> CLIENT_INFO=new ThreadLocal<>();

    /**
     * * 设置用户信息
     * @param clientInfo
     */
    public static void setClientInfo(ClientInfo clientInfo){
        CLIENT_INFO.set(clientInfo);
    }

    /**
     * * 移除用户信息
     */
    public static void remove(){
        if(CLIENT_INFO.get() != null){
            CLIENT_INFO.remove();
        }
    }

    /**
     * * 读取用户信息
     * @return
     */
    public static ClientInfo getClientInfo(){
        ClientInfo clientInfo = CLIENT_INFO.get();
        if(!Objects.isNull(clientInfo)){
            return clientInfo;
        }
        clientInfo = new ClientInfo();
        CLIENT_INFO.set(clientInfo);
        return CLIENT_INFO.get();
    }

    /**
     * 获取用户ID
     */
    public static Long getUserId() {
        final Long userId = HeaderUtils.getUserId();
        if(userId>0L){
            return userId;
        }
        return getClientInfo().getUserId();
    }

    /**
     * 获取用户名
     */
    public static String getUserName() {
        return getClientInfo().getUserName();
    }

    public static void setUserName(String userName){
        getClientInfo().setUserName(userName);
    }
    /**
     * 获取租户Id
     */
    public static Long getCompanyId() {
        return getClientInfo().getCompanyId();
    }

    public static void setCompanyId(Long companyId){
        getClientInfo().setCompanyId(companyId);
    }
    /**
     * 获取用户真实姓名
     */
    public static String getName() {
        return getClientInfo().getName();
    }
    public static void setName(String name){
        getClientInfo().setName(name);
    }

    public static String getTraceId(){
        final String traceId = HeaderUtils.getTraceId();
        if(StringUtils.hasText(traceId)){
            return traceId;
        }
        return getClientInfo().getTraceId();
    }

    /**
     * 获取设备类型
     */
    public static Integer getDeviceType() {
        final Integer deviceType = HeaderUtils.getDeviceType();
        if(deviceType>0){
            return deviceType;
        }
        return getClientInfo().getDeviceType();
    }

    public static String getDeviceId(){
        final String deviceId = HeaderUtils.getDeviceId();
        if(StringUtils.hasText(deviceId)){
            return deviceId;
        }
        return getClientInfo().getDeviceId();
    }
    /**
     * 获取用户角色类型
     */
    public static Integer getUserType() {
        return getClientInfo().getUserType();
    }

    public static void setUserType(Integer userType){
        getClientInfo().setUserType(userType);
    }

    /**
     * 语言码
     */
    public static String getLanguage() {
        final String language = HeaderUtils.getLanguage();
        if(StringUtils.hasText(language)){
            return language;
        }
        return getClientInfo().getLanguage();
    }

}
