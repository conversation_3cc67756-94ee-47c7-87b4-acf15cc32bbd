package com.chervon.common.core.error;

import lombok.extern.slf4j.Slf4j;
import java.util.Arrays;

/**
 * 系统级别错误编码
 */
@Slf4j
public enum ErrorCode implements IError {
    //================服务异常 0~1000=================
    INTERNAL_SERVER_ERROR("500", "Service internal exception"),
    SERVER_TIME_OUT("602", "Server Time Out"),

    //================"系统框架中异常" 1001~2000=================
    UNAUTHORIZED("1401", "User is not logged in"),
    FORBIDDEN("1403", "Permission denied"),
    NOT_SUPPORT_METHOD("1405", "[{0}] Not Supported"),
    PARAMETER_ERROR("1450", "Parameter [{0}] error"),
    PARAMETER_NOT_PROVIDED("1451", "Parameter [{0}] not provided"),
    PARAMETER_FORMAT_ERROR("1452", "Parameter[{0}]Format error"),
    PARAMETER_TOO_LONG("1464", "The length of parameter [{0}] is too long"),
    BUSINESS_OPERATE_WAITING("1466", "Background business operation is in progress, please wait"),

    DATA_NOT_FOUND("1470", "no data found:[{0}]"),
    DATA_DUPLICATION("1472", "data duplication:[{0}]"),
    DB_OPERATION_ERROR("1474", "database operate error[{0}]"),
    ;

    private final String code;
    private String message;

    @Override
    public String getCode() {
        return code;
    }

    @Override
    public String getMessage() {
        return message;
    }

    ErrorCode(String code, String message) {
        this.code = code;
        this.message = message;
    }

    @Override
    public void setMessage(String message) {
        this.message = message;
    }

    public static ErrorCode getError(String code) {
        return Arrays.stream(values())
            .filter(x -> x.getCode().equals(code))
            .findFirst()
            .orElse(null);
    }
}
