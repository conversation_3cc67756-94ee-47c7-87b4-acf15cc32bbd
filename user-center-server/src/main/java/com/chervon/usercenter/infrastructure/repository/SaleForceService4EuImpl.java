package com.chervon.usercenter.infrastructure.repository;

import com.chervon.common.core.constant.CommonConstant;
import com.chervon.common.core.utils.ConvertUtil;
import com.chervon.common.core.utils.JsonUtils;
import com.chervon.common.core.utils.StringUtils;
import com.chervon.usercenter.api.dto.sf.SfUserAddDto;
import com.chervon.usercenter.api.dto.sf.SfWarrantyRegisterDto;
import com.chervon.usercenter.api.dto.sf.eu.SfDeviceSyncDto;
import com.chervon.usercenter.api.dto.sf.eu.SfResponseDto;
import com.chervon.usercenter.api.dto.sf.eu.SfUserSyncDto;
import com.chervon.usercenter.api.dto.sf.eu.SfWarrantyDto;
import com.chervon.usercenter.api.exception.UserCenterErrorCode;
import com.chervon.usercenter.api.service.SaleForceService;
import com.chervon.usercenter.api.vo.sf.SfProductQueryVo;
import com.chervon.usercenter.api.vo.sf.SfQueryVo;
import com.chervon.usercenter.api.vo.sf.SfTokenVo;
import com.chervon.usercenter.api.vo.sf.SfUserRecord;
import com.chervon.usercenter.api.vo.sf.SfWarrantyRecord;
import com.chervon.usercenter.api.vo.sf.SfWarrantyRegisterVo;
import com.chervon.usercenter.config.ExceptionMessageUtil;
import com.chervon.usercenter.domain.constant.UserCenterConstant;
import com.chervon.usercenter.infrastructure.config.SfUserConfig;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * SF平台同步操作，欧洲方向
 * <AUTHOR>
 * @date 2024/6/19
 */
@Service(UserCenterConstant.EU)
@DubboService(group = UserCenterConstant.EU)
@Slf4j
public class SaleForceService4EuImpl extends SaleForceServiceImpl implements SaleForceService {
    private static final String EU_SF_URL = "/services/apexrest/BrandWebsiteEU/v1/";
    private static final DateTimeFormatter YYYY_MM_DD = DateTimeFormatter.ofPattern("yyyy-MM-dd");
    private static final String MMDDYY = "MMddyy";
    private static final String LOWES = "Lowe's";
    private static final String DEFAULT_PLACE_PURCHASE = "Stationary Stores";
    private static final String SERVICES_DATA_URI = "/services/data/";

    @Autowired
    private SfUserConfig sfUserConfig;

    @Override
    public String addSfUser(SfUserAddDto sfUserAddDto) {
        //先发起查询sf有没有该用户
        SfUserRecord sfUserRecord=getSfUserBySfEmail(sfUserAddDto.getUsername__c());
        if(Objects.nonNull(sfUserRecord)){
            return sfUserRecord.getSfUserId();
        }

        SfTokenVo sfTokenVo = getSfToken();
        HttpHeaders headers = initEuSfHeaders(sfTokenVo, "createResidentialUser", null, null);
        String url = sfTokenVo.getInstance_url() + EU_SF_URL;
        SfUserSyncDto userInfo = new SfUserSyncDto();
        userInfo.setEmailAddress(sfUserAddDto.getPersonEmail());
        userInfo.setFirstName(sfUserAddDto.getFirstName());
        userInfo.setLastName(sfUserAddDto.getLastName());
        userInfo.setPassword(sfUserAddDto.getEGO_password__c());
        userInfo.setCountry(sfUserAddDto.getShippingCountry());
        userInfo.setMobilePhone(sfUserAddDto.getPersonMobilePhone());
        userInfo.setStreet(sfUserAddDto.getShippingStreet());
        userInfo.setPostcode(sfUserAddDto.getShippingPostalCode());
        userInfo.setCity(sfUserAddDto.getShippingCity());
        HttpEntity<SfUserSyncDto> request = new HttpEntity<>(userInfo, headers);
        RestTemplate restTemplate = getRestTemplate();
        SfResponseDto response;
        try {
            response = restTemplate.postForObject(url, request, SfResponseDto.class);
        } catch (Exception e) {
            log.error("syncUser2SF error, email:{}, ", sfUserAddDto.getEGO_username__c(), e);
            throw ExceptionMessageUtil.getException(UserCenterErrorCode.USER_CENTER_SALE_FORCE_ADD_USER_ERROR
                    , e.getMessage());
        }
        if(response==null){
            log.warn("syncUser2SF, email:{}, statusCode:null",
                    sfUserAddDto.getEGO_username__c());
            throw ExceptionMessageUtil.getException(UserCenterErrorCode.USER_CENTER_SALE_FORCE_ADD_USER_ERROR);
        }
        if (!UserCenterConstant.SUCCESS_CODE.equals(response.getStatusCode()) ) {
            throw ExceptionMessageUtil.getException(UserCenterErrorCode.USER_CENTER_SALE_FORCE_ADD_USER_ERROR
                    , response.getMessage());
        }
        return response.getId();
    }
    @Override
    public SfUserRecord getSfUserBySfEmail(String email) {
        List<SfUserRecord> sfUserRecords = listSfUser("+WHERE+EGO_username__c='"
                + email + "'+and+RecordType.DeveloperName+=+'Residential_Consumer'" );
        if (org.springframework.util.CollectionUtils.isEmpty(sfUserRecords)) {
            return null;
        }
        return sfUserRecords.get(CommonConstant.ZERO);
    }
    @Override
    public SfWarrantyRegisterVo registerSfWarranty(SfWarrantyRegisterDto sfWarrantyRegisterDto) {
        if(sfWarrantyRegisterDto == null || CollectionUtils.isEmpty(sfWarrantyRegisterDto.getRecords())) {
            return null;
        }

        SfWarrantyRegisterDto.WRecord wRecord = sfWarrantyRegisterDto.getRecords().get(0);
        List<SfWarrantyDto> euWarrantyItems = Lists.newArrayList();
        SfWarrantyRegisterDto.WarrantyItems warrantyItems = wRecord.getWarranty_Items__r();
        if(warrantyItems == null || CollectionUtils.isEmpty(warrantyItems.getRecords())) {
            return null;
        }
        List<SfWarrantyRegisterDto.WIRecord> records = warrantyItems.getRecords();
        for(SfWarrantyRegisterDto.WIRecord wiRecord : records) {
            SfWarrantyDto warrantyDto = new SfWarrantyDto();
            warrantyDto.setSerialNumber(wiRecord.getSerial_Number__c());
            warrantyDto.setProductModelNumber(wiRecord.getProduct_Model__c());
            String sfWarrantyId = getSfWarrantyId(wiRecord.getSerial_Number__c());
            if(StringUtils.isNotEmpty(sfWarrantyId)) {
                warrantyDto.setRecordId(sfWarrantyId);
            }
            euWarrantyItems.add(warrantyDto);
        }

        SfDeviceSyncDto deviceInfo = new SfDeviceSyncDto();
        deviceInfo.setWarrantyItems(euWarrantyItems);
        deviceInfo.setCustomerId(wRecord.getAccountCustomer__c());
        deviceInfo.setMasterModelNumber(wRecord.getMaster_Product__c());
        deviceInfo.setPlaceOfPurchase(wRecord.getPlace_of_Purchase__c());
        //如果购买地址为空，设置为默认地址
        if(StringUtils.isBlank(deviceInfo.getPlaceOfPurchase())) {
            deviceInfo.setPlaceOfPurchase(DEFAULT_PLACE_PURCHASE);
        }
        if(wRecord.getGift__c()) {
            deviceInfo.setReceiptStatus("gift");
        } else if(wRecord.getLost_Receipt__c()) {
            deviceInfo.setReceiptStatus("lost");
        } else {
            deviceInfo.setReceiptStatus("uploaded");
        }
        deviceInfo.setProductUseType(wRecord.getProduct_Use_Type2__c());
        if(StringUtils.isNotEmpty(wRecord.getPurchase_Date__c())) {
            LocalDate date = LocalDate.parse(wRecord.getPurchase_Date__c(), YYYY_MM_DD);
            long milli = date.toEpochDay() * 24 * 3600000L;
            deviceInfo.setPurchaseDate(DateFormatUtils.format(milli, MMDDYY));
        }
        deviceInfo.setReceiptUrl(wRecord.getImage_of_Receipt__c());

        SfTokenVo sfTokenVo = getSfToken();
        RestTemplate restTemplate = getRestTemplate();
        HttpHeaders headers = initEuSfHeaders(sfTokenVo, "registerWarrantyNew", null, null);
        HttpEntity<SfDeviceSyncDto> request = new HttpEntity<>(deviceInfo, headers);
        SfResponseDto response;
        try {
            log.info("registerSfWarranty request body is: {}", JsonUtils.toJsonString(deviceInfo));
            response = restTemplate.postForObject(sfTokenVo.getInstance_url() + EU_SF_URL,
                    request, SfResponseDto.class);
        } catch (Exception e) {
            log.error("syncDevice2SF error, customerId:{}, ", deviceInfo.getCustomerId(), e);
            throw ExceptionMessageUtil.getException(
                    UserCenterErrorCode.USER_CENTER_SALE_FORCE_REGISTER_WARRANTY_ERROR, e.getMessage());
        }
        if(response==null){
            log.warn("syncDevice2SF, customerId:{}, statusCode:null",
                    deviceInfo.getCustomerId());
            throw ExceptionMessageUtil.getException(UserCenterErrorCode.USER_CENTER_SALE_FORCE_REGISTER_WARRANTY_ERROR);
        }
        if(!UserCenterConstant.SUCCESS_CODE.equals(response.getStatusCode())) {
            log.warn("syncDevice2SF fail, request deviceInfo:{}", JsonUtils.toJsonString(deviceInfo));
            throw ExceptionMessageUtil.getException(UserCenterErrorCode.USER_CENTER_SALE_FORCE_REGISTER_WARRANTY_ERROR
                    , response.getMessage());
        }
        return new SfWarrantyRegisterVo();
    }

    @Override
    public List<SfProductQueryVo> listSfProduct(String productModel, String Country_of_Origin__c) {
        SfTokenVo sfTokenVo = getSfToken(false);
        RestTemplate restTemplate = getRestTemplate();
        HttpHeaders headers = initHeader(sfTokenVo.getAccess_token());

        String url = sfTokenVo.getInstance_url() + SERVICES_DATA_URI + sfUserConfig.getApiVersion() + "/query?q=" +
                "SELECT Id FROM Product2 " +
                "WHERE Master_Product__c='" + productModel + "'";
        ParameterizedTypeReference<SfQueryVo<SfProductQueryVo>> responseBodyType =
                new ParameterizedTypeReference<SfQueryVo<SfProductQueryVo>>() {};
        try {
            ResponseEntity<SfQueryVo<SfProductQueryVo>> response = restTemplate.exchange(url,
                    HttpMethod.GET,
                    new HttpEntity<String>(headers),
                    responseBodyType);
            List<SfProductQueryVo> list = ConvertUtil.convertList(
                    Objects.requireNonNull(response.getBody()).getRecords()
                    , SfProductQueryVo.class);
            if (!org.springframework.util.CollectionUtils.isEmpty(list)) {
                return list;
            }
            return Collections.emptyList();
        } catch (Exception e) {
            throw ExceptionMessageUtil.getException(UserCenterErrorCode.USER_CENTER_SALE_FORCE_LIST_PRODUCT_ERROR);
        }
    }

    @Override
    public List<SfWarrantyRecord> getWarrantyBySn(String sn) {
        SfTokenVo sfTokenVo = getSfToken();
        RestTemplate restTemplate = getRestTemplate();
        HttpHeaders headers = initHeader(sfTokenVo.getAccess_token());
        String url = sfTokenVo.getInstance_url() + SERVICES_DATA_URI + sfUserConfig.getApiVersion() + "/query?q=" +
                "SELECT+Id,Receipt_Status__c,Receipt_Link__c,Consumer__c,Product_Use_Type2__c,Purchase_Place__c," +
                "Purchase_Date__c,Serial_Number__c,LastModifiedDate" +
                "+FROM+Warranty_Item__c+WHERE+Consumer__c+!=+NULL+AND+Brand__c+=+'EGO'+AND+Serial_Number__c+=+'"+sn+"'";
        ParameterizedTypeReference<SfQueryVo<SfWarrantyRecord>> responseBodyType =
                new ParameterizedTypeReference<SfQueryVo<SfWarrantyRecord>>() {
        };
        ResponseEntity<SfQueryVo<SfWarrantyRecord>> response = restTemplate.exchange(url,
                HttpMethod.GET,
                new HttpEntity<String>(headers),
                responseBodyType);
        SfQueryVo<SfWarrantyRecord> body = response.getBody();
        if (body == null) {
            return Collections.emptyList();
        }
        List<SfWarrantyRecord> records = body.getRecords();
        if(CollectionUtils.isEmpty(records)) {
            return Collections.emptyList();
        }
        return records;
    }

    @Override
    public List<SfWarrantyRecord> getWarrantyByUser(String sfUserId) {
        if(StringUtils.isEmpty(sfUserId)) {
            return Collections.emptyList();
        }
        SfTokenVo sfTokenVo = getSfToken();
        RestTemplate restTemplate = getRestTemplate();
        String url = sfTokenVo.getInstance_url() + SERVICES_DATA_URI + sfUserConfig.getApiVersion() + "/query?" +
                "q=SELECT+Id,Receipt_Status__c,Receipt_Link__c,Consumer__c,Product_Use_Type2__c,Purchase_Place__c," +
                "Purchase_Date__c,Serial_Number__c,LastModifiedDate+FROM+Warranty_Item__c+WHERE+Brand__c+=+'EGO'+AND+" +
                "Consumer__c='" + sfUserId + "'";
        // 请求头设置,x-www-form-urlencoded格式的数据
        HttpHeaders headers = initHeader(sfTokenVo.getAccess_token());
        ParameterizedTypeReference<SfQueryVo<SfWarrantyRecord>> responseBodyType =
                new ParameterizedTypeReference<SfQueryVo<SfWarrantyRecord>>() {};
        ResponseEntity<SfQueryVo<SfWarrantyRecord>> response = restTemplate.exchange(url,
                HttpMethod.GET,
                new HttpEntity<String>(headers),
                responseBodyType);
        SfQueryVo<SfWarrantyRecord> body = response.getBody();
        if ( null == body) {
            return Collections.emptyList();
        }else {
            return body.getRecords();
        }
    }

    @Override
    public List<SfUserRecord> listSfUserUpdatedIn5Min() {
        String userSyncTime = getUserSyncTime();
        String whereStr = "+WHERE+EGO_username__c+!=+NULL+and+EGO_password__c+!=+NULL+" +
                "and+RecordType.DeveloperName+=+'Residential_Consumer'+" +
                "and+LastModifiedDate+>+" + userSyncTime;
        return listSfUser(whereStr);
    }

    /**
     * 设置欧洲SF同步请求的http头部
     *
     */
    private HttpHeaders initEuSfHeaders(SfTokenVo sfTokenVo, String method, String customerId, String email) {
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.add("Authorization", "Bearer " + sfTokenVo.getAccess_token());
        headers.add("appKey", sfTokenVo.getAccess_token());
        headers.add("method", method);
        if(!Strings.isNullOrEmpty(customerId)) {
            headers.add("customerId", customerId);
        }
        if(!Strings.isNullOrEmpty(email)) {
            headers.add("emailAddress", email);
        }
        return headers;
    }

    @Override
    public SfUserRecord getSfUserBySfUserId(String sfUserId) {
        String whereStr = "+WHERE+EGO_username__c+!=+NULL+and+EGO_password__c+!=+NULL+" +
                "and+RecordType.DeveloperName+=+'Residential_Consumer'+" +
                "and+Id='" + sfUserId + "'";
        List<SfUserRecord> sfUserRecords = listSfUser(whereStr);
        if (CollectionUtils.isEmpty(sfUserRecords)) {
            return null;
        }
        return sfUserRecords.get(CommonConstant.ZERO);
    }

    /**
     * 实例化header数据
     * @param accessToken
     * @return
     */
    private HttpHeaders initHeader(String accessToken){
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);
        headers.add("Authorization", "Bearer " + accessToken);
        return headers;
    }
}
