package com.chervon.configuration.dubbo;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.chervon.common.core.constant.StringPool;
import com.chervon.common.core.utils.JsonUtils;
import com.chervon.common.i18n.config.MessageConfig;
import com.chervon.common.redis.utils.RedisUtils;
import com.chervon.configuration.api.core.LanguageContentBo;
import com.chervon.configuration.api.core.MultiLanguageBo;
import com.chervon.configuration.api.core.MultiLanguageRespBo;
import com.chervon.configuration.api.core.StaticMultiLanguageReqDto;
import com.chervon.configuration.api.exception.ConfigurationErrorCode;
import com.chervon.configuration.api.service.RemoteMultiLanguageService;
import com.chervon.configuration.config.AppRnProperties;
import com.chervon.configuration.config.Constant;
import com.chervon.configuration.config.ExceptionMessageUtil;
import com.chervon.configuration.entity.MultiLanguage;
import com.chervon.configuration.entity.MultiLanguageContent;
import com.chervon.configuration.service.MultiLanguageContentService;
import com.chervon.configuration.service.MultiLanguageService;
import com.chervon.configuration.util.ObjectListCompressor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboService;
import org.redisson.api.RMap;
import org.redisson.api.RedissonClient;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;
import java.time.Instant;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/7/13 9:31
 */
@DubboService
@Service
@Slf4j
public class RemoteMultiLanguageServiceImpl implements RemoteMultiLanguageService {

    private static final String LIKE = "like";
    private static final String EQUAL = "equal";
    @Autowired
    private MultiLanguageService multiLanguageService;
    @Autowired
    private MultiLanguageContentService multiLanguageContentService;
    @Autowired
    private MessageConfig messageConfig;
    @Autowired
    private AppRnProperties appRnProperties;

    @Override
    public Map<String, MultiLanguageBo> simpleCreateMultiLanguages(String applicationName, Map<String, String> contents, String language) {
        if (contents == null || contents.isEmpty()) {
            return new HashMap<>();
        }
        LocaleContextHolder.setLocale(new Locale(language));
        return multiLanguageService.simpleCreateMultiLanguages(applicationName, contents);
    }

    @Override
    public List<MultiLanguageBo> listByIds(List<String> langIds) {
        if (langIds == null || langIds.isEmpty()) {
            return new ArrayList<>();
        }
        return listByIdsDb(langIds);
    }

    private List<MultiLanguageBo> listByIdsDb(List<String> langIds) {
        if (CollectionUtil.isEmpty(langIds)) {
            return new ArrayList<>();
        }
        List<MultiLanguageBo> res = new ArrayList<>();
        List<MultiLanguage> multiLanguages = multiLanguageService.listByIds(langIds);
        multiLanguages.forEach(e -> {
            MultiLanguageBo bo = new MultiLanguageBo();
            BeanUtils.copyProperties(e, bo);
            bo.setLangId(e.getId());
            res.add(bo);
        });
        return res;
    }

    @Override
    public void simpleUpdateMultiLanguages(String applicationName, Map<Long, String> langIdNewContentMap, String language) {
        if (langIdNewContentMap == null || langIdNewContentMap.isEmpty()) {
            return;
        }
        LocaleContextHolder.setLocale(new Locale(language));
        multiLanguageService.simpleUpdateMultiLanguages(applicationName, langIdNewContentMap);
    }

    @Override
    public Map<String, List<MultiLanguageBo>> listByText(Map<String, List<Long>> query, String lang, String type) {
        if (StringUtils.isBlank(type)) {
            type = LIKE;
        }
        String t = type;
        if (!Arrays.asList(LIKE, EQUAL).contains(t)) {
            throw ExceptionMessageUtil.getException(ConfigurationErrorCode.MULTI_LANGUAGE_LIST_BY_TEXT_TYPE_ERROR);
        }
        Map<String, List<MultiLanguageBo>> res = new HashMap<>();
        query.forEach((k, v) -> {
            LambdaQueryWrapper<MultiLanguageContent> wrapper = new LambdaQueryWrapper<>();
            wrapper.in(!CollectionUtils.isEmpty(v), MultiLanguageContent::getLangId, v);
            wrapper.select(MultiLanguageContent::getId, MultiLanguageContent::getLangId, MultiLanguageContent::getLangCode);
            wrapper.eq(MultiLanguageContent::getLang, lang);
            if (StringUtils.equals(t, LIKE)) {
                wrapper.like(MultiLanguageContent::getContent, k);
            } else {
                wrapper.eq(MultiLanguageContent::getContent, k);
            }
            List<MultiLanguageContent> list = multiLanguageContentService.list(wrapper);
            res.put(k, list.stream().map(e -> {
                MultiLanguageBo bo = new MultiLanguageBo();
                bo.setLangId(e.getLangId());
                bo.setLangCode(e.getLangCode());
                return bo;
            }).collect(Collectors.toList()));
        });
        return res;
    }

    @Override
    public MultiLanguageBo simpleCreateRichMultiLanguage(String applicationName, String content, String language) {
        LocaleContextHolder.setLocale(new Locale(language));
        return multiLanguageService.simpleCreateRichMultiLanguage(applicationName, content);
    }


    @Override
    public Map<String, String> simpleFindMultiLanguageByCodes(Collection<String> codes, String targetLang) {
        String lang = StringUtils.isNotBlank(targetLang) ? targetLang : LocaleContextHolder.getLocale().getLanguage();
        if (CollectionUtils.isEmpty(codes) || StringUtils.isBlank(lang)) {
            return new HashMap<>();
        }
        return multiLanguageContentService.batchGetByLangCode(codes, lang);
    }

    @Override
    public Map<String, Map<String, String>> listLanguageBySysCodes(Collection<String> sysCodes, String lang) {
        if (CollectionUtils.isEmpty(sysCodes)) {
            return new HashMap<>();
        }
        Map<String, Map<String, String>> res = new HashMap<>();
        for (String sysCode : sysCodes) {
            RedissonClient client = RedisUtils.getClient();
            RMap<String, String> map = client.getMap("multiLanguage:page:" + sysCode + ":" + lang);
            Map<String, String> m = new HashMap<>(map);
            res.put(sysCode, m);
        }
        return res;
    }

    @Override
    public Map<String, Map<String, String>> listLanguageBySysCodes(Collection<String> sysCodes) {
        return listLanguageBySysCodes(sysCodes, LocaleContextHolder.getLocale().getLanguage());
    }

    /**
     * 返回多语言内容结构
     * @param langIds 多语言id列表
     * @return Map<langId,Map<language,content>>
     */
    @Override
    public Map<String, Map<String, String>> listLanguageContentForAllLanguagesByLangIds(Collection<String> langIds) {
        return batchGetLanguageContentByListLang((List<String>) langIds,Arrays.asList("zh", "en", "de", "es", "nl", "fr"));
    }

    /**
     * 返回多语言内容结构
     * @param langIds 多语言id列表
     * @return Map<langId,Map<language,content>>
     */
    @Override
    public Map<String, Map<String, String>> batchGetLanguageContentByListLang(List<String> langIds,List<String> listLanguage) {
        List<MultiLanguageContent> multiLanguageContents = multiLanguageContentService.list(new LambdaQueryWrapper<MultiLanguageContent>()
                .in(MultiLanguageContent::getLangId, langIds)
                .in(MultiLanguageContent::getLang, listLanguage)
        );
        Map<String, Map<String, String>> result = new HashMap<>();
        for (MultiLanguageContent multiLanguageContent : multiLanguageContents) {
            Map<String, String> langContentMap = result.getOrDefault(multiLanguageContent.getLangId().toString(), new HashMap<>());
            langContentMap.put(multiLanguageContent.getLang(), multiLanguageContent.getContent());
            result.put(multiLanguageContent.getLangId().toString(), langContentMap);
        }
        return result;
    }

    /**
     * 根据多语言code批量删除
     *
     * @param langCodes 多语言code集合
     */
    @Override
    public void deleteByLangCodes(List<String> langCodes) {
        multiLanguageService.deleteByLangCodes(langCodes);
    }

    /**
     * 根据多语言id批量删除
     *
     * @param langIds 多语言id集合
     */
    @Override
    public void deleteByLangIds(List<Long> langIds) {
        multiLanguageService.deleteByLangIds(langIds);
    }

    /**
     * 描述：获取app、rn新增和变更静态多语言文案
     *
     * @param multiLanguageRequestDto 请求入参
     * @return com.chervon.iot.app.domain.vo.multilanguage.MultiLanguageRespBo
     * @date 2024/3/8 16:29
     **/
    @Override
    public MultiLanguageRespBo getMultiLanguage(StaticMultiLanguageReqDto multiLanguageRequestDto) {

        // 返回对象
        MultiLanguageRespBo bo = new MultiLanguageRespBo();
        List<LanguageContentBo> data;

        String sysCode = multiLanguageRequestDto.getSysCode();
        String lastRequestTs = multiLanguageRequestDto.getLastRequestTs();
        int isZip = multiLanguageRequestDto.getIsZip();

        // 拼接本地缓存多语言文件路径
        String path = System.getProperty("user.dir") + File.separator + messageConfig.getBaseFolder() + File.separator;
        StringBuilder fileName = null;
        if (StringUtils.isNotEmpty(sysCode) && sysCode.equals(Constant.APP)) {
            fileName = new StringBuilder(appRnProperties.getAppMultiFileName().split("\\.")[0])
                    .append(StringPool.UNDERSCORE).append("%s").append(StringPool.DOT)
                    .append(appRnProperties.getAppMultiFileName().split("\\.")[1]);

        } else if (StringUtils.isNotEmpty(sysCode) && sysCode.equals(Constant.RN)) {
            fileName = new StringBuilder(appRnProperties.getRnMultiFileName().split("\\.")[0])
                    .append(StringPool.UNDERSCORE).append("%s").append(StringPool.DOT)
                    .append(appRnProperties.getRnMultiFileName().split("\\.")[1]);
        }else{
            fileName = new StringBuilder(sysCode)
                    .append(StringPool.UNDERSCORE).append("%s").append(StringPool.DOT)
                    .append("properties");
        }
        String localFile = path + fileName;
        List<String> langList = multiLanguageRequestDto.getLang();

        // 获取变更的多语言词条
        if (!Objects.isNull(langList) && !langList.isEmpty()) {
            langList = langList.stream().map(i -> i.contains("_") ? i.replace("_", "-") : i)
                    .collect(Collectors.toList());
            data = getDiffLanguage(langList, localFile, lastRequestTs);
        } else {
            String[] langConfig = messageConfig.getLangList().split(",");
            data = getDiffLanguage(Arrays.asList(langConfig), localFile, lastRequestTs);
        }
        bo.setLastTime(String.valueOf(Instant.now().getEpochSecond()));
        try {
            if (isZip == 1) {
                bo.setData(ObjectListCompressor.compress(JsonUtils.toJson(data)));
            } else {
                bo.setData(data);
            }
        } catch (IOException e) {
            log.warn("List<LanguageContentBo> serialize to bytes failed!");
            bo.setData(new ArrayList<>());
        }
        return bo;
    }

    /**
     * 描述：比对检索出变更过的多语言词条
     *
     * @return java.util.List<com.chervon.iot.app.domain.vo.multilanguage.LanguageContentBo>
     * @date 2024/3/11 15:43
     **/
    public List<LanguageContentBo> getDiffLanguage(List<String> langList, String localFile, String lastRequestTs) {
        List<LanguageContentBo> data = new ArrayList<>();
        for (String l : langList) {
            String file = String.format(localFile, l);
            if (new File(file).exists()) {
                Properties properties = new Properties();
                try (FileInputStream fis = new FileInputStream(file);
                     InputStreamReader isr = new InputStreamReader(fis, StandardCharsets.UTF_8)) {
                    properties.load(isr);
                    // 读取属性值并拆分
                    LanguageContentBo languageContentVo;
                    for (String key : properties.stringPropertyNames()) {
                        String[] values = properties.getProperty(key).split("\\|");
                        if (values.length == 2) {
                            String value = values[0];
                            String timestamp = values[1];
                            if (Long.parseLong(timestamp) > Long.parseLong(lastRequestTs)) {
                                languageContentVo = new LanguageContentBo(l, key, value);
                                data.add(languageContentVo);
                            }
                        }
                    }
                } catch (IOException e) {
                    log.error("local cache file: %s read failed!", e);
                    return null;
                }
            }
        }
        return data;
    }
}
