package com.chervon.configuration.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.chervon.configuration.config.PageLanguageContent;
import com.chervon.configuration.entity.MultiLanguageContent;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/7/27 16:54
 */
@Mapper
public interface MultiLanguageContentMapper extends BaseMapper<MultiLanguageContent> {
    List<MultiLanguageContent> listBySysCode(@Param("sysCode") String sysCode);

    List<PageLanguageContent> listPageContents();

    List<PageLanguageContent> listPageContentsBySysCode(@Param("sysCode") String sysCode);
}
