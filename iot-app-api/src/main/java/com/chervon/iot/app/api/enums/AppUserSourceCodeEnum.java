package com.chervon.iot.app.api.enums;

import java.util.Arrays;

/**
 * 用户绑定关系来源枚举类
 *
 * <AUTHOR> 2024/11/28
 */
public enum AppUserSourceCodeEnum {
    IOT(0, "IOT"),
    CRM(1, "CRM");

    private int type;
    private String desc;


    AppUserSourceCodeEnum(int type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public int getType() {
        return this.type;
    }

    public String getDesc() {
        return this.desc;
    }

    /**
     * 获取枚举描述信息
     *
     * @param type 类型编码
     * @return 描述信息
     */
    public static String getDesc(int type) {
        return Arrays.stream(values()).filter(x ->
                x.getType() == type
        ).map(AppUserSourceCodeEnum::getDesc).findFirst().orElse(null);
    }

    /**
     * 根据type获取枚举数据
     *
     * @param type 类型编码
     * @return 枚举数据
     */
    public static AppUserSourceCodeEnum getEnum(int type) {
        return Arrays.stream(values()).filter(x ->
                x.getType() == type
        ).findFirst().orElse(null);
    }

    /**
     * 判断是否为CRM来源
     *
     * @param sourceCode 来源编码
     * @return 是否为CRM
     */
    public static boolean isCRM(Integer sourceCode) {
        return sourceCode != null && CRM.getType() == sourceCode;
    }
}