package com.chervon.common.excel.utils;

import com.alibaba.fastjson.JSON;
import com.chervon.common.core.domain.R;
import lombok.extern.slf4j.Slf4j;

import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.nio.charset.StandardCharsets;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2022-08-05 15:15
 **/
@Slf4j
public class ExportUtil {
    public static void exportCsv(HttpServletResponse response, List<?> list, String fileName) throws IOException {
        try {
            response.setContentType("application/csv");
            response.setCharacterEncoding("utf-8");

            //响应的是  .csv 文件的后缀
            response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".csv");
            // 这里需要设置不关闭流
            String filePath = fileName + ".csv";
            File file = new File(filePath);
            if (!file.exists()) {
                file.createNewFile();
            }
            //将数据，写入到 文件里面。 主要是这一行代码逻辑
            cn.hutool.core.text.csv.CsvUtil.getWriter(file, StandardCharsets.UTF_8).writeBeans(list).close();
            ExportUtil.downloadFile(response, file);
            //将该文件删除
            file.delete();
        } catch (Exception e) {
            response.reset();
            response.setContentType("application/json");
            response.setCharacterEncoding("utf-8");
            response.getWriter().println(JSON.toJSONString(R.fail("下载文件失败")));
        }
    }

    public static boolean downloadFile(HttpServletResponse response, File file) {
        FileInputStream fileInputStream = null;
        BufferedInputStream bufferedInputStream = null;
        OutputStream os = null;
        try {
            fileInputStream = new FileInputStream(file);
            bufferedInputStream = new BufferedInputStream(fileInputStream);
            os = response.getOutputStream();
            //MS产本头部需要插入BOM
            //如果不写入这几个字节，会导致用Excel打开时，中文显示乱码
            os.write(new byte[]{(byte) 0xEF, (byte) 0xBB, (byte) 0xBF});
            byte[] buffer = new byte[1024];
            int i = bufferedInputStream.read(buffer);
            while (i != -1) {
                os.write(buffer, 0, i);
                i = bufferedInputStream.read(buffer);
            }
            return true;
        } catch (IOException e) {
            log.error(e.getMessage(), e);
        } finally {
            //关闭流
            if (os != null) {
                try {
                    os.flush();
                    os.close();
                } catch (IOException e) {
                    log.error("ExportUtil -> FileInputStream关闭异常");
                }
            }
            if (bufferedInputStream != null) {
                try {
                    bufferedInputStream.close();
                } catch (IOException e) {
                    log.error("ExportUtil -> FileInputStream关闭异常");
                }
            }
            if (fileInputStream != null) {
                try {
                    fileInputStream.close();
                } catch (IOException e) {
                    log.error("ExportUtil -> FileInputStream关闭异常");
                }
            }
            file.delete();
        }
        return false;
    }
}
