//package com.chervon.message.service.impl;
//
//import cn.hutool.core.util.IdUtil;
//import com.chervon.common.core.error.ErrorCode;
//import com.chervon.common.core.exception.ServiceException;
//import com.chervon.common.core.utils.BeanCopyUtils;
//import com.chervon.common.core.utils.SnowFlake;
//import com.chervon.common.core.utils.StringUtils;
//import com.chervon.commons.db.mongo.dao.MongoBaseDao;
//import com.chervon.message.api.dto.TransferMessageDto;
//import com.chervon.message.api.enums.MessageTypeEnum;
//import com.chervon.message.api.enums.PushResultEnum;
//import com.chervon.message.domain.entity.MessageMongoData;
//import com.chervon.message.domain.entity.UserDeviceMessage;
//import com.chervon.message.domain.entity.UserMarketMessage;
//import com.chervon.message.domain.entity.UserSystemMessage;
//import com.chervon.message.service.MongoMessageService;
//import com.chervon.message.service.UserDeviceMessageService;
//import com.chervon.message.service.UserMarketMessageService;
//import com.chervon.message.service.UserSystemMessageService;
//import lombok.RequiredArgsConstructor;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.data.domain.PageRequest;
//import org.springframework.data.domain.Sort;
//import org.springframework.data.mongodb.core.MongoTemplate;
//import org.springframework.data.mongodb.core.query.Query;
//import org.springframework.stereotype.Service;
//import org.springframework.util.CollectionUtils;
//
//import java.text.MessageFormat;
//import java.time.LocalDate;
//import java.time.format.DateTimeFormatter;
//import java.util.ArrayList;
//import java.util.List;
//import java.util.Objects;
//import java.util.stream.Collectors;
//
///**
// * 服务接口实现
// *
// * <AUTHOR>
// * @since 2024-07-18 17:10:17
// * @description 设备消息
// */
//@Slf4j
//@RequiredArgsConstructor
//@Service
//public class MongoMessageServiceImpl implements MongoMessageService {
//
//    @Autowired
//    private MongoTemplate mongoTemplate;
//
//    @Autowired
//    private UserSystemMessageService userSystemMessageService;
//    @Autowired
//    private UserMarketMessageService userMarketMessageService;
//    @Autowired
//    private UserDeviceMessageService userDeviceMessageService;
//
//    @Override
//    public String transferToMySqlData(TransferMessageDto transferMessageDto) {
//        if (transferMessageDto.getMessageType() < 0 || transferMessageDto.getMessageType() > 2) {
//            return "transferToMySqlData messageType参数类型错误：应为0，1，2";
//        }
//        if(Objects.isNull(transferMessageDto.getPageSize())){
//            transferMessageDto.setPageSize(200);
//        }
//
//        if(CollectionUtils.isEmpty(transferMessageDto.getCollectionName())){
//            transferMessageDto.setCollectionName(new ArrayList<>());
//        }
//        if (StringUtils.isNotEmpty(transferMessageDto.getCollectionPrefix()) &&
//                StringUtils.isNotEmpty(transferMessageDto.getBeginDate()) &&
//                StringUtils.isNotEmpty(transferMessageDto.getEndDate())) {
//            buildSystemMessageCollection(transferMessageDto);
//            buildDeviceMessageCollection(transferMessageDto);
//        }
//        if(CollectionUtils.isEmpty(transferMessageDto.getCollectionName())){
//            return "nothing to do!";
//        }
//
//        log.info("==》本次任务同步开始：集合数："+ transferMessageDto.getCollectionName().size());
//        Integer allCount=0;
//        for (String collectionName : transferMessageDto.getCollectionName()) {
//            Integer count=0;
//            Integer pageIndex = 0;
//            log.info(MessageFormat.format("transferToMySqlData 开始同步集合{0}数据\n",collectionName,pageIndex));
//            while (true) {
//                Integer rowCount= null;
//                try {
//                    //指定集合名称，页码循环累加，直到无法获取到数据停止
//                    rowCount = doTransfer(transferMessageDto.getMessageType(), collectionName, pageIndex,transferMessageDto.getPageSize());
//                } catch (Exception e) {
//                    log.error(MessageFormat.format("transferToMySqlData 集合{0}同步发生异常停止，共同步记录数：{1}，错误信息：{2}\n",collectionName,count,e.getMessage()));
//                    break;
//                }
//                count+=rowCount;
//                if(rowCount<transferMessageDto.getPageSize() || rowCount==0){
//                    log.info(MessageFormat.format("transferToMySqlData 集合{0}同步数据完成，共同步记录数：{1}\n",collectionName,count));
//                    break;
//                }
//                pageIndex++;
//            }
//            allCount+=count;
//        }
//        String resultInfo=MessageFormat.format("《===transferToMySqlData 本次任务同步完成，同步集合数：{0}，同步总记录数：{1}",transferMessageDto.getCollectionName().size(),allCount);
//        log.info(resultInfo);
//        return resultInfo;
//    }
//
//    /**
//     * 是否还有未完成的消息
//     * @param messageType
//     * @param collectionName
//     * @param pageIndex
//     * @return
//     */
//    private Integer doTransfer(Integer messageType, String collectionName, Integer pageIndex,Integer pageSize) {
//        Query query = new Query();
//        query.with(Sort.by(Sort.Direction.ASC, "createTime"));
//        query.with(PageRequest.of(pageIndex, pageSize));
//        List<MessageMongoData> list = mongoTemplate.find(query, MessageMongoData.class, collectionName);
//        //mongo下线临时对象列表，其他环境取消上面注释；
//        if(CollectionUtils.isEmpty(list)){
//            return 0;
//        }
//        if (messageType.equals(MessageTypeEnum.SYS_MSG.getValue())) {
//            List<UserSystemMessage> listMessages = new ArrayList<>();
//            for(MessageMongoData data:list){
//                final UserSystemMessage copy = BeanCopyUtils.copy(data, UserSystemMessage.class);
//                copy.setId(SnowFlake.nextId());
//                copy.setUserId(Long.valueOf(data.getUserId()));
//                copy.setDeviceType(data.getDeviceType().getName());
//                copy.setIfRead(data.getIfRead());
//                copy.setPushTypes(data.getPushTypes().stream().map(String::valueOf).collect(Collectors.joining(",")));
//                copy.setPushResult(data.getPushResult()? PushResultEnum.SUCCESS.getType() :PushResultEnum.FAIL.getType());
//                listMessages.add(copy);
//            }
//            userSystemMessageService.saveBatch(listMessages);
//        } else if (messageType.equals(MessageTypeEnum.MARKETING_MSG.getValue())) {
//            List<UserMarketMessage> listMessages = new ArrayList<>();
//            for(MessageMongoData data:list){
//                final UserMarketMessage copy = BeanCopyUtils.copy(data, UserMarketMessage.class);
//                copy.setId(SnowFlake.nextId());
//                copy.setUserId(Long.valueOf(data.getUserId()));
//                copy.setDeviceType(data.getDeviceType().getName());
//                copy.setIfRead(data.getIfRead());
//                copy.setPushTypes(data.getPushTypes().stream().map(String::valueOf).collect(Collectors.joining(",")));
//                copy.setPushResult(data.getPushResult()? PushResultEnum.SUCCESS.getType() :PushResultEnum.FAIL.getType());
//                listMessages.add(copy);
//            }
//            userMarketMessageService.saveBatch(listMessages);
//        } else if (messageType.equals(MessageTypeEnum.DEVICE_MSG.getValue())) {
//            List<UserDeviceMessage> listMessages = new ArrayList<>();
//            for(MessageMongoData data:list){
//                final UserDeviceMessage copy = BeanCopyUtils.copy(data, UserDeviceMessage.class);
//                copy.setId(SnowFlake.nextId());
//                copy.setUserId(Long.valueOf(data.getUserId()));
//                copy.setProductId(Long.valueOf(data.getProductId()));
//                copy.setDeviceType(data.getDeviceType().getName());
//                copy.setIfRead(data.getIfRead());
//                copy.setPushTypes(data.getPushTypes().stream().map(String::valueOf).collect(Collectors.joining(",")));
//                copy.setPushResult(data.getPushResult()? PushResultEnum.SUCCESS.getType() :PushResultEnum.FAIL.getType());
//                listMessages.add(copy);
//            }
//            userDeviceMessageService.saveBatch(listMessages);
//        }
//        return list.size();
//    }
//
//    public static List<String> getBetweenDateStrList(String beginStr, String endStr) {
//        DateTimeFormatter fmt = DateTimeFormatter.ofPattern("yyyyMMdd");
//        LocalDate begin = LocalDate.parse(beginStr, fmt);
//        LocalDate end = LocalDate.parse(endStr, fmt);
//        List<String> result = new ArrayList();
//        long length = end.toEpochDay() - begin.toEpochDay();
//
//        for(long i = length; i >= 0L; --i) {
//            result.add(end.minusDays(i).format(fmt));
//        }
//        return result;
//    }
//
//    private static void buildSystemMessageCollection(TransferMessageDto transferMessageDto) {
//        if (transferMessageDto.getMessageType().equals(MessageTypeEnum.DEVICE_MSG.getValue())){
//            return;
//        }
//        final List<String> dateStrList = MongoMessageServiceImpl.getBetweenDateStrList(transferMessageDto.getBeginDate(), transferMessageDto.getEndDate());
//        if (!CollectionUtils.isEmpty(dateStrList)) {
//            List<String> listCollectionName = new ArrayList<>();
//            for (String date : dateStrList) {
//                listCollectionName.add(transferMessageDto.getCollectionPrefix() + date);
//            }
//            transferMessageDto.setCollectionName(listCollectionName);
//        }
//    }
//
//    private static void buildDeviceMessageCollection(TransferMessageDto transferMessageDto) {
//        if (!transferMessageDto.getMessageType().equals(MessageTypeEnum.DEVICE_MSG.getValue())
//                || CollectionUtils.isEmpty(transferMessageDto.getListProduct())) {
//            return;
//        }
//        final List<String> dateStrList = MongoMessageServiceImpl.getBetweenDateStrList(transferMessageDto.getBeginDate(), transferMessageDto.getEndDate());
//        if (CollectionUtils.isEmpty(dateStrList)) {
//            return;
//        }
//        List<String> listCollectionName = new ArrayList<>();
//        for (String date : dateStrList) {
//            for (String product : transferMessageDto.getListProduct()) {
//                listCollectionName.add(transferMessageDto.getCollectionPrefix()+ product + date);
//            }
//        }
//        transferMessageDto.setCollectionName(listCollectionName);
//    }
//
//}