package com.chervon.message.rpc.impl;

import com.chervon.common.core.domain.PageResult;
import com.chervon.common.core.error.ErrorCode;
import com.chervon.common.core.exception.ServiceException;
import com.chervon.message.api.RemoteMessageService;
import com.chervon.message.api.dto.*;
import com.chervon.message.api.enums.MessageTypeEnum;
import com.chervon.message.api.exception.MessageErrorCode;
import com.chervon.message.api.vo.LastMessageListVo;
import com.chervon.message.api.vo.MessageVo;
import com.chervon.message.config.ExceptionMessageUtil;
import com.chervon.message.mq.producer.StreamProducer;
import com.chervon.message.service.UserDeviceMessageService;
import com.chervon.message.service.UserMarketMessageService;
import com.chervon.message.service.UserSystemMessageService;
import com.chervon.message.service.pushHandler.MessagePusher;
import com.chervon.message.service.saveHandler.IMessageHandler;
import com.chervon.message.service.saveHandler.MessageFactory;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.concurrent.Callable;
import java.util.concurrent.Future;
import java.util.stream.Collectors;

/**
 * 消息中心消息推送和查询服务
 * <AUTHOR>
 * @date 2024-09-03
 */
@DubboService
@Service
@Slf4j
public class RemoteMessageServiceImpl implements RemoteMessageService {
    @Autowired
    private UserSystemMessageService userSystemMessageService;
    @Autowired
    private UserMarketMessageService userMarketMessageService;
    @Autowired
    private UserDeviceMessageService userDeviceMessageService;
    @Autowired
    private StreamProducer streamProducer;
//    @Autowired
//    private MongoMessageService messageService;
    @Autowired
    private ThreadPoolTaskExecutor executor;

//    @Async
//    @Override
//    public void transferToMySqlData(TransferMessageDto transferMessageDto) {
//        final String info = messageService.transferToMySqlData(transferMessageDto);
//        log.info(info);
//    }

    /**
     * 接收系统消息和营销消息的外部推送
     * @param messages 消息信息
     */
    public void pushMessage(List<MessageDto> messages) {
        if(CollectionUtils.isEmpty(messages)){
            return;
        }
        MessageDto firstMessage = messages.get(0);
        MqMessageDto mqMessageDto=new MqMessageDto();
        mqMessageDto.setMessageType(firstMessage.getMessageType());
        mqMessageDto.setMessageId(firstMessage.getSystemMessageId());
        mqMessageDto.setMessageBody(messages);
        pushMessageToMq(mqMessageDto);
    }

    /**
     * 对其他服务暴漏的发布消息到rocketMq接口
     * @param mqMessageDto 消息对象信息
     */
    @Override
    public void pushMessageToMq(MqMessageDto mqMessageDto) {
        streamProducer.publishMessage(mqMessageDto);
    }

    /**
     * 接收告警消息的设备消息推送接口
     * @param deviceMessages 设备消息
     */
    @Override
    @Async
    public void pushDeviceMessageAsync(List<DeviceMessageDto> deviceMessages) {
        if(CollectionUtils.isEmpty(deviceMessages)){
            return;
        }
        List<MessageDto> messageDtos = deviceMessages.stream().map(a->new MessageDto()
                .setMessageType(MessageTypeEnum.DEVICE_MSG.getValue())
                .setDeviceId(a.getDeviceId())
                .setDeviceType(a.getDeviceType())
                .setPushTypes(a.getPushTypes())
                .setContent(a.getContent())
                .setEmail(a.getEmail())
                .setPhone(a.getPhone())
                .setPushSwitch(a.getPushSwitch())
                .setPayloadData(a.getPayloadData())
                .setPhoneSwitch(a.getPhoneSwitch())
                .setProductId(a.getProductId())
                .setSnsSwitch(a.getSnsSwitch())
                .setSystemMessageId(a.getSystemMessageId())
                .setTitle(a.getTitle())
                .setToken(a.getToken())
                .setUserId(a.getUserId())
                .setDeviceNickName(a.getDeviceNickName())
        ).collect(Collectors.toList());

        MqMessageDto mqMessageDto=new MqMessageDto();
        mqMessageDto.setMessageType(MessageTypeEnum.DEVICE_MSG.getValue());
        mqMessageDto.setMessageId(messageDtos.get(0).getSystemMessageId());
        mqMessageDto.setMessageBody(messageDtos);
        pushMessageToMq(mqMessageDto);
    }

    /**
     * 同步推送的消息接口（慎用废弃）
     * @param deviceMessages 设备消息
     */
    public void pushDeviceMessage(List<MessageDto> deviceMessages) {
        MessagePusher.start(deviceMessages);
    }

    /**
     * 检查用户是否有未读消息存在
     * @param lastMessageDto 请求对象
     * @return 是否存在
     */
    @Override
    public Boolean checkDeviceUnreadMessage(LastMessageDto lastMessageDto) {
        return userDeviceMessageService.checkUserUnReadMessage(lastMessageDto);
    }
    /**
     * 获取用户最后一条消息
     * @param lastMessage 用户和设备信息
     * @return
     */
    @Override
    public LastMessageListVo getLastMessage(LastMessageDto lastMessage) {
        // 定义异步任务
        Callable<List<MessageVo>> deviceMessageTask = () -> {
            return userDeviceMessageService.getUserLastMessage(lastMessage);
        };
        Callable<MessageVo> systemMessageTask = () -> {
            return userSystemMessageService.getUserLastMessage(lastMessage);
        };
        Callable<MessageVo> marketMessageTask = () -> {
            return userMarketMessageService.getUserLastMessage(lastMessage);
        };

        LastMessageListVo messages = new LastMessageListVo();
        try {
            // 提交任务并获取 Future 对象
            Future<List<MessageVo>> deviceMessageFuture = executor.submit(deviceMessageTask);
            Future<MessageVo> systemMessageFuture = executor.submit(systemMessageTask);
            Future<MessageVo> marketMessageFuture = executor.submit(marketMessageTask);

            // 获取系统消息
            messages.setSystemMessage(systemMessageFuture.get());
            // 获取营销消息
            messages.setMarketingMessage(marketMessageFuture.get());
            // 获取设备消息
            messages.setDevicesMessage(deviceMessageFuture.get());
        } catch (Exception e) {
            log.error("getLastMessage线程池异步获取用户最后一条消息失败：{}", e);
        }
        return messages;
    }

     /**
     * 获取用户消息分页列表
     * @param pageRequest 消息信息
     * @return
     */
    @Override
    @SneakyThrows
    public PageResult<MessageVo> getMessageList(SearchMessageInfoDto pageRequest) {
        pageRequest.setPageSize(10);
        if (MessageTypeEnum.SYS_MSG.getValue().equals(pageRequest.getMessageType())) {
            return userSystemMessageService.getPageList(pageRequest);
        } else if (MessageTypeEnum.MARKETING_MSG.getValue().equals(pageRequest.getMessageType())) {
            return userMarketMessageService.getPageList(pageRequest);
        } else if (MessageTypeEnum.DEVICE_MSG.getValue().equals(pageRequest.getMessageType())) {
            return userDeviceMessageService.getPageList(pageRequest);
        } else {
            throw ExceptionMessageUtil.getException(MessageErrorCode.MESSAGE_PUSH_PARAM_ERROR, pageRequest.getMessageType());
        }
    }

    /**
     * 获取消息详情
     * @param messageDetail 消息信息
     * @return
     */
    @Override
    public MessageVo getMessageDetail(MessageDetailDto messageDetail) {
        if (MessageTypeEnum.SYS_MSG.getValue().equals(messageDetail.getMessageType())||MessageTypeEnum.FEEDBACK_MSG.getValue().equals(messageDetail.getMessageType())
                ||MessageTypeEnum.SHARE_MSG.getValue().equals(messageDetail.getMessageType())) {
            return userSystemMessageService.getDetail(messageDetail);
        } else if (MessageTypeEnum.MARKETING_MSG.getValue().equals(messageDetail.getMessageType())) {
            return userMarketMessageService.getDetail(messageDetail);
        } else if (MessageTypeEnum.DEVICE_MSG.getValue().equals(messageDetail.getMessageType())) {
            return userDeviceMessageService.getDetail(messageDetail);
        } else {
            throw new ServiceException(ErrorCode.PARAMETER_NOT_PROVIDED,"messageDetail.getMessageType()");
        }
    }

    /**
     * 删除注销用户的所有消息（逻辑删除）
     * @param userId 用户Id
     * @param deviceInfoList 设备信息
     */
    @Override
    public void deleteMessageByUserId(Long userId, List<DeviceInfoDto> deviceInfoList) {
        // 删除系统消息
        userSystemMessageService.deleteByUserId(userId);
        // 删除营销消息
        userMarketMessageService.deleteByUserId(userId);
        // 删除用户设备消息
        userDeviceMessageService.deleteByUserId(userId,deviceInfoList);
    }

    /**
     * 删除用户指定条件的消息信息（逻辑删除）
     * @param messageDetail 用户Id
     */
    @Override
    public void deleteMessage(MessageDetailDto messageDetail) {
        if (MessageTypeEnum.SYS_MSG.getValue().equals(messageDetail.getMessageType())|| MessageTypeEnum.FEEDBACK_MSG.getValue().equals(messageDetail.getMessageType())||MessageTypeEnum.SHARE_MSG.getValue().equals(messageDetail.getMessageType())) {
            userSystemMessageService.delete(messageDetail);
        } else if (MessageTypeEnum.MARKETING_MSG.getValue().equals(messageDetail.getMessageType())) {
            userMarketMessageService.delete(messageDetail);
        } else if (MessageTypeEnum.DEVICE_MSG.getValue().equals(messageDetail.getMessageType())) {
            userDeviceMessageService.delete(messageDetail);
        } else {
            throw new ServiceException(ErrorCode.PARAMETER_NOT_PROVIDED,"messageDetail.getMessageType()");
        }
    }

    /**
     * web端获取消推送记录-分页
     * @param searchMessage 搜索消息Dto
     * @return 分页结果
     */
    @Override
    public PageResult<MessageVo> getPushRecordPage(SearchMessageDto searchMessage) {
        if (MessageTypeEnum.SYS_MSG.getValue().equals(searchMessage.getMessageType())) {
            return userSystemMessageService.SearchPageList(searchMessage);
        } else if (MessageTypeEnum.MARKETING_MSG.getValue().equals(searchMessage.getMessageType())) {
            return userMarketMessageService.SearchPageList(searchMessage);
        } else if (MessageTypeEnum.DEVICE_MSG.getValue().equals(searchMessage.getMessageType())) {
            return userDeviceMessageService.SearchPageList(searchMessage);
        } else {
            throw new ServiceException(ErrorCode.PARAMETER_ERROR,"searchMessage.getMessageType()");
        }
    }

    /**
     * web端获取消推送消息记录-不分页（导出场景）
     * @param searchMessage 搜索消息Dto
     * @return 不分页结果
     */
    @Override
    public List<MessageVo> getPushRecordList(SearchMessageDto searchMessage) {
        if (MessageTypeEnum.SYS_MSG.getValue().equals(searchMessage.getMessageType())) {
            return userSystemMessageService.getList(searchMessage);
        } else if (MessageTypeEnum.MARKETING_MSG.getValue().equals(searchMessage.getMessageType())) {
            return userMarketMessageService.getList(searchMessage);
        } else if (MessageTypeEnum.DEVICE_MSG.getValue().equals(searchMessage.getMessageType())) {
            return userDeviceMessageService.getList(searchMessage);
        } else {
            throw new ServiceException(ErrorCode.PARAMETER_ERROR,"searchMessage.getMessageType()");
        }
    }

    /**
     * 执行消息统计数量更新落库
     */
    @Override
    public void asyncUpdateMessageCount(String strMessageIdList) {
        for(MessageTypeEnum messageTypeEnum:MessageTypeEnum.values()){
            final IMessageHandler messageInstance = MessageFactory.getMessageInstance(messageTypeEnum.getValue());
            messageInstance.updateMessageCount(strMessageIdList);
        }
    }
}
